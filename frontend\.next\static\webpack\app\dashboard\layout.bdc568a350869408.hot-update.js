"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token) {\n                console.log('⚠️ No auth token found, skipping backend session loading');\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});