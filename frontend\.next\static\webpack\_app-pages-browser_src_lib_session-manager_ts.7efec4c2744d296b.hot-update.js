"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_lib_session-manager_ts",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    isSessionUsedByOtherWindows(sessionId) {\n        if (false) {}\n        // Check all window-specific current session keys in localStorage\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        // Check if any other window (not this one) is using this session\n        for (const key of currentSessionKeys){\n            if (key !== this.getWindowSpecificKey(CURRENT_SESSION_KEY)) {\n                const otherWindowSessionId = localStorage.getItem(key);\n                if (otherWindowSessionId === sessionId) {\n                    console.log(\"\\uD83D\\uDD0D Session \".concat(sessionId, \" is being used by another window (\").concat(key, \")\"));\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    getActiveWindowsForSession(sessionId) {\n        if (false) {}\n        const activeWindows = [];\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        for (const key of currentSessionKeys){\n            const windowSessionId = localStorage.getItem(key);\n            if (windowSessionId === sessionId) {\n                // Extract window ID from key\n                const windowId = key.replace(CURRENT_SESSION_KEY + '_', '');\n                activeWindows.push(windowId);\n            }\n        }\n        return activeWindows;\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    const sessionData = session;\n                    return sessionData.isActive && sessionData.lastModified && Date.now() - sessionData.lastModified < 30 * 60 * 1000; // Active within last 30 minutes\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>{\n                        const latestSession = latest[1];\n                        const currentSession = current[1];\n                        return currentSession.lastModified > latestSession.lastModified ? current : latest;\n                    });\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // For now, focus on localStorage persistence to avoid backend API issues\n            // Backend integration can be enabled later when backend is properly configured\n            console.log('💾 Saving session to localStorage (backend disabled for stability):', sessionId);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // But don't deactivate other sessions - multiple sessions can be active in different tabs\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Only mark session as inactive if no other windows are using it\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                // Check if any other windows are using this session\n                const isUsedByOtherWindows = this.isSessionUsedByOtherWindows(this.currentSessionId);\n                if (!isUsedByOtherWindows) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                } else {\n                    console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" remains active (used by other windows)\"));\n                }\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});