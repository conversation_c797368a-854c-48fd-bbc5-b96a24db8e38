"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_lib_session-manager_ts",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token) {\n                console.log('⚠️ No auth token found, skipping backend session loading');\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend\n        this.initializeBackendConnection();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});