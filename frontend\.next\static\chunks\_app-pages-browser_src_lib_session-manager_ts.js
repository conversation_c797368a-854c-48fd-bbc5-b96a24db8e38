"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_session-manager_ts"],{

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js":
/*!******************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/native.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  randomUUID\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvbmF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tYnJvd3NlclxcbmF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHJhbmRvbVVVSUQgPSB0eXBlb2YgY3J5cHRvICE9PSAndW5kZWZpbmVkJyAmJiBjcnlwdG8ucmFuZG9tVVVJRCAmJiBjcnlwdG8ucmFuZG9tVVVJRC5iaW5kKGNyeXB0byk7XG5leHBvcnQgZGVmYXVsdCB7XG4gIHJhbmRvbVVVSURcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js":
/*!*****************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/regex.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWMsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRyx5Q0FBeUMiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFxyZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLTVdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCkkL2k7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js":
/*!***************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/rng.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcm5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tYnJvd3Nlclxccm5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFVuaXF1ZSBJRCBjcmVhdGlvbiByZXF1aXJlcyBhIGhpZ2ggcXVhbGl0eSByYW5kb20gIyBnZW5lcmF0b3IuIEluIHRoZSBicm93c2VyIHdlIHRoZXJlZm9yZVxuLy8gcmVxdWlyZSB0aGUgY3J5cHRvIEFQSSBhbmQgZG8gbm90IHN1cHBvcnQgYnVpbHQtaW4gZmFsbGJhY2sgdG8gbG93ZXIgcXVhbGl0eSByYW5kb20gbnVtYmVyXG4vLyBnZW5lcmF0b3JzIChsaWtlIE1hdGgucmFuZG9tKCkpLlxubGV0IGdldFJhbmRvbVZhbHVlcztcbmNvbnN0IHJuZHM4ID0gbmV3IFVpbnQ4QXJyYXkoMTYpO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcm5nKCkge1xuICAvLyBsYXp5IGxvYWQgc28gdGhhdCBlbnZpcm9ubWVudHMgdGhhdCBuZWVkIHRvIHBvbHlmaWxsIGhhdmUgYSBjaGFuY2UgdG8gZG8gc29cbiAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAvLyBnZXRSYW5kb21WYWx1ZXMgbmVlZHMgdG8gYmUgaW52b2tlZCBpbiBhIGNvbnRleHQgd2hlcmUgXCJ0aGlzXCIgaXMgYSBDcnlwdG8gaW1wbGVtZW50YXRpb24uXG4gICAgZ2V0UmFuZG9tVmFsdWVzID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLmdldFJhbmRvbVZhbHVlcyAmJiBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzLmJpbmQoY3J5cHRvKTtcblxuICAgIGlmICghZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGdldFJhbmRvbVZhbHVlcyhybmRzOCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js":
/*!*********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\");\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nfunction unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js":
/*!**************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/v4.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\n\nfunction v4(options, buf, offset) {\n  if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n    return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNOO0FBQ3NCOztBQUVqRDtBQUNBLE1BQU0sa0RBQU07QUFDWixXQUFXLGtEQUFNO0FBQ2pCOztBQUVBO0FBQ0EsaURBQWlELCtDQUFHLEtBQUs7O0FBRXpEO0FBQ0EsbUNBQW1DOztBQUVuQztBQUNBOztBQUVBLG9CQUFvQixRQUFRO0FBQzVCO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxTQUFTLDhEQUFlO0FBQ3hCOztBQUVBLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFx2NC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbmF0aXZlIGZyb20gJy4vbmF0aXZlLmpzJztcbmltcG9ydCBybmcgZnJvbSAnLi9ybmcuanMnO1xuaW1wb3J0IHsgdW5zYWZlU3RyaW5naWZ5IH0gZnJvbSAnLi9zdHJpbmdpZnkuanMnO1xuXG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICBpZiAobmF0aXZlLnJhbmRvbVVVSUQgJiYgIWJ1ZiAmJiAhb3B0aW9ucykge1xuICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICB9XG5cbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gIGNvbnN0IHJuZHMgPSBvcHRpb25zLnJhbmRvbSB8fCAob3B0aW9ucy5ybmcgfHwgcm5nKSgpOyAvLyBQZXIgNC40LCBzZXQgYml0cyBmb3IgdmVyc2lvbiBhbmQgYGNsb2NrX3NlcV9oaV9hbmRfcmVzZXJ2ZWRgXG5cbiAgcm5kc1s2XSA9IHJuZHNbNl0gJiAweDBmIHwgMHg0MDtcbiAgcm5kc1s4XSA9IHJuZHNbOF0gJiAweDNmIHwgMHg4MDsgLy8gQ29weSBieXRlcyB0byBidWZmZXIsIGlmIHByb3ZpZGVkXG5cbiAgaWYgKGJ1Zikge1xuICAgIG9mZnNldCA9IG9mZnNldCB8fCAwO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCAxNjsgKytpKSB7XG4gICAgICBidWZbb2Zmc2V0ICsgaV0gPSBybmRzW2ldO1xuICAgIH1cblxuICAgIHJldHVybiBidWY7XG4gIH1cblxuICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2NDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js":
/*!********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/validate.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\");\n\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7O0FBRS9CO0FBQ0EscUNBQXFDLGlEQUFLO0FBQzFDOztBQUVBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFx2YWxpZGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUkVHRVggZnJvbSAnLi9yZWdleC5qcyc7XG5cbmZ1bmN0aW9uIHZhbGlkYXRlKHV1aWQpIHtcbiAgcmV0dXJuIHR5cGVvZiB1dWlkID09PSAnc3RyaW5nJyAmJiBSRUdFWC50ZXN0KHV1aWQpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0ZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    isSessionUsedByOtherWindows(sessionId) {\n        if (false) {}\n        // Check all window-specific current session keys in localStorage\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        // Check if any other window (not this one) is using this session\n        for (const key of currentSessionKeys){\n            if (key !== this.getWindowSpecificKey(CURRENT_SESSION_KEY)) {\n                const otherWindowSessionId = localStorage.getItem(key);\n                if (otherWindowSessionId === sessionId) {\n                    console.log(\"\\uD83D\\uDD0D Session \".concat(sessionId, \" is being used by another window (\").concat(key, \")\"));\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    getActiveWindowsForSession(sessionId) {\n        if (false) {}\n        const activeWindows = [];\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        for (const key of currentSessionKeys){\n            const windowSessionId = localStorage.getItem(key);\n            if (windowSessionId === sessionId) {\n                // Extract window ID from key\n                const windowId = key.replace(CURRENT_SESSION_KEY + '_', '');\n                activeWindows.push(windowId);\n            }\n        }\n        return activeWindows;\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    const sessionData = session;\n                    return sessionData.isActive && sessionData.lastModified && Date.now() - sessionData.lastModified < 30 * 60 * 1000; // Active within last 30 minutes\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>{\n                        const latestSession = latest[1];\n                        const currentSession = current[1];\n                        return currentSession.lastModified > latestSession.lastModified ? current : latest;\n                    });\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // If this session is active, ensure it has a start time for runtime calculation\n                const session = this.sessions.get(currentSessionId);\n                if (session && session.isActive && !this.sessionStartTimes.has(currentSessionId)) {\n                    // Restore start time based on when the session was last modified\n                    const estimatedStartTime = Date.now() - (session.runtime || 0);\n                    this.sessionStartTimes.set(currentSessionId, estimatedStartTime);\n                    console.log(\"⏰ Restored session start time for active session: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // For now, focus on localStorage persistence to avoid backend API issues\n            // Backend integration can be enabled later when backend is properly configured\n            console.log('💾 Saving session to localStorage (backend disabled for stability):', sessionId);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // But don't deactivate other sessions - multiple sessions can be active in different tabs\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Only mark session as inactive if no other windows are using it\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                // Check if any other windows are using this session\n                const isUsedByOtherWindows = this.isSessionUsedByOtherWindows(this.currentSessionId);\n                if (!isUsedByOtherWindows) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                } else {\n                    console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" remains active (used by other windows)\"));\n                }\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

}]);