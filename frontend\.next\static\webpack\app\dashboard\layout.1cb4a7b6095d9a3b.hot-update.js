"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\n// Helper function to get real-time market price from Binance API\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // Primary: Try Binance API with multiple symbol formats\n        const symbol1 = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        const symbol2 = \"\".concat(config.crypto1, \"USDT\").toUpperCase(); // For cross-calculation\n        const symbol3 = \"\".concat(config.crypto2, \"USDT\").toUpperCase(); // For cross-calculation\n        // Try direct pair first\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol1));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Direct price from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"Direct pair \".concat(symbol1, \" not found on Binance, trying cross-calculation...\"));\n        }\n        // Try cross-calculation via USDT if direct pair not available\n        try {\n            const [response1, response2] = await Promise.all([\n                fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol2)),\n                fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol3))\n            ]);\n            if (response1.ok && response2.ok) {\n                const [data1, data2] = await Promise.all([\n                    response1.json(),\n                    response2.json()\n                ]);\n                const price1 = parseFloat(data1.price); // crypto1/USDT\n                const price2 = parseFloat(data2.price); // crypto2/USDT\n                if (price1 > 0 && price2 > 0) {\n                    const crossPrice = price1 / price2; // crypto1/crypto2\n                    console.log(\"✅ Cross-calculated price from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(crossPrice, \" (\").concat(price1, \"/\").concat(price2, \")\"));\n                    return crossPrice;\n                }\n            }\n        } catch (error) {\n            console.warn('Binance cross-calculation failed, trying CoinGecko...', error);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using USD calculation...', geckoError);\n        }\n        // Final fallback - calculate from real-time USD prices\n        try {\n            const crypto1USDPrice = await getRealTimeUSDPrice(config.crypto1);\n            const crypto2USDPrice = await getRealTimeUSDPrice(config.crypto2);\n            const calculatedPrice = crypto1USDPrice / crypto2USDPrice;\n            console.log(\"\\uD83D\\uDCCA Calculated price from real-time USD rates: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(calculatedPrice));\n            return calculatedPrice;\n        } catch (error) {\n            console.warn('Real-time USD price calculation failed, using static fallback...', error);\n        }\n        // Final fallback to static calculation\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using static fallback price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache - shorter duration for more frequent updates\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 5000; // 5 seconds cache for real-time updates\n// Telegram notification queue for offline periods\nlet telegramNotificationQueue = [];\nconst MAX_QUEUE_SIZE = 10; // Limit queue size to prevent memory issues\n// Helper function to get real-time USD price from API with fallback (async version)\nconst getRealTimeUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Try to fetch real-time price from CoinGecko API\n        const coinGeckoId = getCoinGeckoId(crypto);\n        if (coinGeckoId) {\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"));\n            if (response.ok) {\n                var _data_coinGeckoId;\n                const data = await response.json();\n                const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                if (price && price > 0) {\n                    priceCache[cacheKey] = price;\n                    lastPriceUpdate = now;\n                    console.log(\"\\uD83D\\uDCCA Real-time price fetched for \".concat(crypto, \": $\").concat(price));\n                    return price;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn(\"⚠️ Failed to fetch real-time price for \".concat(crypto, \", using fallback:\"), error);\n    }\n    // Fallback to static prices if API fails\n    return getUSDPrice(crypto);\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Restoring active session after page refresh:', {\n                sessionId: currentSessionId,\n                name: currentSession.name,\n                isActive: currentSession.isActive,\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance,\n                targetPriceRows: currentSession.targetPriceRows.length,\n                orderHistory: currentSession.orderHistory.length\n            });\n            // Ensure the session remains active if it was active before refresh\n            if (currentSession.isActive) {\n                console.log('✅ Maintaining active session state after page refresh');\n            }\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Enhanced Telegram notification function with error handling, retry logic, and offline queue\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                // Check if we're online before attempting to send\n                const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                if (!networkMonitor.getStatus().isOnline) {\n                    // Queue the notification for when we're back online\n                    if (telegramNotificationQueue.length < MAX_QUEUE_SIZE) {\n                        telegramNotificationQueue.push({\n                            message,\n                            timestamp: Date.now(),\n                            isError\n                        });\n                        console.log('📤 Queued telegram notification for when online (queue size:', telegramNotificationQueue.length, ')');\n                    } else {\n                        console.warn('⚠️ Telegram notification queue is full, dropping oldest notification');\n                        telegramNotificationQueue.shift(); // Remove oldest\n                        telegramNotificationQueue.push({\n                            message,\n                            timestamp: Date.now(),\n                            isError\n                        });\n                    }\n                    return;\n                }\n                // Add timeout to prevent hanging requests\n                const controller = new AbortController();\n                const timeoutId = setTimeout({\n                    \"TradingProvider.useCallback[sendTelegramNotification].timeoutId\": ()=>controller.abort()\n                }[\"TradingProvider.useCallback[sendTelegramNotification].timeoutId\"], 10000); // 10 second timeout\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Retry logic for non-error notifications\n                    if (!isError && retryCount < 2) {\n                        console.log(\"\\uD83D\\uDD04 Retrying telegram notification (attempt \".concat(retryCount + 1, \"/3)...\"));\n                        setTimeout({\n                            \"TradingProvider.useCallback[sendTelegramNotification]\": ()=>{\n                                sendTelegramNotification(message, isError, retryCount + 1);\n                            }\n                        }[\"TradingProvider.useCallback[sendTelegramNotification]\"], 5000 * (retryCount + 1)); // Exponential backoff: 5s, 10s\n                        return;\n                    }\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError && retryCount >= 2) {\n                        console.error('❌ All telegram notification retry attempts failed');\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Retry logic for network errors (but not for error notifications to avoid loops)\n                if (!isError && retryCount < 2) {\n                    console.log(\"\\uD83D\\uDD04 Retrying telegram notification after network error (attempt \".concat(retryCount + 1, \"/3)...\"));\n                    setTimeout({\n                        \"TradingProvider.useCallback[sendTelegramNotification]\": ()=>{\n                            sendTelegramNotification(message, isError, retryCount + 1);\n                        }\n                    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], 10000 * (retryCount + 1)); // Longer backoff for network errors: 10s, 20s\n                    return;\n                }\n                if (!isError && retryCount >= 2) {\n                    console.error('❌ All telegram notification retry attempts failed due to network errors');\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Initialize fetchMarketPrice after telegram functions\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                // For StablecoinSwap mode, use real-time prices for accurate market calculation\n                if (state.config.tradingMode === \"StablecoinSwap\") {\n                    // Try to get real-time prices first\n                    try {\n                        const crypto1USDPrice = await getRealTimeUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = await getRealTimeUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Real-time):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6), \"\\n            - Calculation: \").concat(crypto1USDPrice, \" \\xf7 \").concat(crypto2USDPrice, \" = \").concat(price.toFixed(6)));\n                    } catch (error) {\n                        // Fallback to cached/static prices\n                        const crypto1USDPrice = getUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = getUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Fallback):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    }\n                } else {\n                    // For SimpleSpot mode, use the existing API-based approach\n                    price = await getMarketPriceFromAPI(state.config);\n                }\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                sendAPIErrorNotification('Price API', \"Failed to fetch market price: \".concat(error)).catch(console.error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Real-time price update effect - fetches live prices from Binance API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price update interval (every 2 seconds as requested)\n            const realTimePriceInterval = setInterval({\n                \"TradingProvider.useEffect.realTimePriceInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            // Fetch fresh price from API instead of using mock fluctuation\n                            await fetchMarketPrice();\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update real-time price:', error);\n                            // Send error notification for price fetch failures\n                            await sendTelegramErrorNotification('Price Update Error', \"Failed to fetch real-time price: \".concat(error));\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.realTimePriceInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(realTimePriceInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        sendTelegramErrorNotification\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        realizedProfitLossCrypto2: estimatedProfitCrypto2,\n                                        realizedProfitLossCrypto1: estimatedProfitCrypto1\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        // Send telegram notification for network disconnection\n                        sendTelegramErrorNotification('Network Disconnection', 'Internet connection lost. Bot has been stopped and session saved automatically. Trading will resume when connection is restored.').catch(console.error);\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        // Process any queued telegram notifications first\n                        processQueuedTelegramNotifications().catch(console.error);\n                        // Send delayed notification about the previous disconnection now that we're back online\n                        sendTelegramErrorNotification('Network Reconnected', 'Internet connection has been restored. The bot was automatically stopped during the disconnection to prevent trading errors. You can now resume trading safely.').catch(console.error);\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 2034,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Y+t5m//CeahI7jJ6Wm+dMUQTqyg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9UcmFkaW5nQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUVxRztBQUVpQjtBQUNsRjtBQUVwQyx3Q0FBd0M7QUFDeEMsTUFBTVcsMkJBQTJCO0lBQUM7SUFBUTtJQUFRO0NBQU07QUFDWDtBQUNOO0FBQ2dCO0FBQ2dDO0FBOEN2RiwwREFBMEQ7QUFDMUQsTUFBTU8sOEJBQThCLENBQUNDO0lBQ25DLDRDQUE0QztJQUM1QyxJQUFJLENBQUNBLE9BQU9DLE9BQU8sSUFBSSxDQUFDRCxPQUFPRSxPQUFPLEVBQUU7UUFDdEMsT0FBTztJQUNUO0lBQ0EseUNBQXlDO0lBQ3pDLE9BQU9DLDZCQUE2Qkg7QUFDdEM7QUFFQSxpRUFBaUU7QUFDakUsaUVBQWlFO0FBQ2pFLE1BQU1JLHdCQUF3QixPQUFPSjtJQUNuQyxJQUFJO1FBQ0YsNENBQTRDO1FBQzVDLElBQUksQ0FBQ0EsT0FBT0MsT0FBTyxJQUFJLENBQUNELE9BQU9FLE9BQU8sRUFBRTtZQUN0QyxPQUFPO1FBQ1Q7UUFFQSx3REFBd0Q7UUFDeEQsTUFBTUcsVUFBVSxHQUFvQkwsT0FBakJBLE9BQU9DLE9BQU8sRUFBa0IsT0FBZkQsT0FBT0UsT0FBTyxFQUFHSSxXQUFXO1FBQ2hFLE1BQU1DLFVBQVUsR0FBa0IsT0FBZlAsT0FBT0MsT0FBTyxFQUFDLFFBQU1LLFdBQVcsSUFBSSx3QkFBd0I7UUFDL0UsTUFBTUUsVUFBVSxHQUFrQixPQUFmUixPQUFPRSxPQUFPLEVBQUMsUUFBTUksV0FBVyxJQUFJLHdCQUF3QjtRQUUvRSx3QkFBd0I7UUFDeEIsSUFBSTtZQUNGLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxzREFBOEQsT0FBUkw7WUFDbkYsSUFBSUksU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU1DLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtnQkFDaEMsTUFBTUMsUUFBUUMsV0FBV0gsS0FBS0UsS0FBSztnQkFDbkMsSUFBSUEsUUFBUSxHQUFHO29CQUNiRSxRQUFRQyxHQUFHLENBQUMsZ0NBQWtEakIsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUF1QmEsT0FBcEJkLE9BQU9FLE9BQU8sRUFBQyxPQUFXLE9BQU5ZO29CQUNsRixPQUFPQTtnQkFDVDtZQUNGO1FBQ0YsRUFBRSxPQUFPSSxPQUFPO1lBQ2RGLFFBQVFHLElBQUksQ0FBQyxlQUF1QixPQUFSZCxTQUFRO1FBQ3RDO1FBRUEsOERBQThEO1FBQzlELElBQUk7WUFDRixNQUFNLENBQUNlLFdBQVdDLFVBQVUsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7Z0JBQy9DYixNQUFNLHNEQUE4RCxPQUFSSDtnQkFDNURHLE1BQU0sc0RBQThELE9BQVJGO2FBQzdEO1lBRUQsSUFBSVksVUFBVVQsRUFBRSxJQUFJVSxVQUFVVixFQUFFLEVBQUU7Z0JBQ2hDLE1BQU0sQ0FBQ2EsT0FBT0MsTUFBTSxHQUFHLE1BQU1ILFFBQVFDLEdBQUcsQ0FBQztvQkFBQ0gsVUFBVVAsSUFBSTtvQkFBSVEsVUFBVVIsSUFBSTtpQkFBRztnQkFDN0UsTUFBTWEsU0FBU1gsV0FBV1MsTUFBTVYsS0FBSyxHQUFHLGVBQWU7Z0JBQ3ZELE1BQU1hLFNBQVNaLFdBQVdVLE1BQU1YLEtBQUssR0FBRyxlQUFlO2dCQUV2RCxJQUFJWSxTQUFTLEtBQUtDLFNBQVMsR0FBRztvQkFDNUIsTUFBTUMsYUFBYUYsU0FBU0MsUUFBUSxrQkFBa0I7b0JBQ3REWCxRQUFRQyxHQUFHLENBQUMsMENBQTREakIsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUF1QjJCLE9BQXBCNUIsT0FBT0UsT0FBTyxFQUFDLE9BQW9Cd0IsT0FBZkUsWUFBVyxNQUFjRCxPQUFWRCxRQUFPLEtBQVUsT0FBUEMsUUFBTztvQkFDNUgsT0FBT0M7Z0JBQ1Q7WUFDRjtRQUNGLEVBQUUsT0FBT1YsT0FBTztZQUNkRixRQUFRRyxJQUFJLENBQUMseURBQXlERDtRQUN4RTtRQUVBLHFEQUFxRDtRQUNyRCxJQUFJO1lBQ0YsTUFBTVcsWUFBWUMsZUFBZTlCLE9BQU9DLE9BQU87WUFDL0MsTUFBTThCLFlBQVlELGVBQWU5QixPQUFPRSxPQUFPO1lBRS9DLElBQUkyQixhQUFhRSxXQUFXO2dCQUMxQixNQUFNdEIsV0FBVyxNQUFNQyxNQUFNLHFEQUFnRnFCLE9BQTNCRixXQUFVLG1CQUEyQixPQUFWRTtnQkFDN0csSUFBSXRCLFNBQVNFLEVBQUUsRUFBRTt3QkFFREM7b0JBRGQsTUFBTUEsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO29CQUNoQyxNQUFNQyxTQUFRRixrQkFBQUEsSUFBSSxDQUFDaUIsVUFBVSxjQUFmakIsc0NBQUFBLGVBQWlCLENBQUNtQixVQUFVO29CQUMxQyxJQUFJakIsUUFBUSxHQUFHO3dCQUNiRSxRQUFRQyxHQUFHLENBQUMsbUNBQXFEakIsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUF1QmEsT0FBcEJkLE9BQU9FLE9BQU8sRUFBQyxPQUFXLE9BQU5ZO3dCQUNyRixPQUFPQTtvQkFDVDtnQkFDRjtZQUNGO1FBQ0YsRUFBRSxPQUFPa0IsWUFBWTtZQUNuQmhCLFFBQVFHLElBQUksQ0FBQyxrREFBa0RhO1FBQ2pFO1FBRUEsdURBQXVEO1FBQ3ZELElBQUk7WUFDRixNQUFNQyxrQkFBa0IsTUFBTUMsb0JBQW9CbEMsT0FBT0MsT0FBTztZQUNoRSxNQUFNa0Msa0JBQWtCLE1BQU1ELG9CQUFvQmxDLE9BQU9FLE9BQU87WUFDaEUsTUFBTWtDLGtCQUFrQkgsa0JBQWtCRTtZQUMxQ25CLFFBQVFDLEdBQUcsQ0FBQywyREFBbUVqQixPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQXVCbUMsT0FBcEJwQyxPQUFPRSxPQUFPLEVBQUMsT0FBcUIsT0FBaEJrQztZQUNuRyxPQUFPQTtRQUNULEVBQUUsT0FBT2xCLE9BQU87WUFDZEYsUUFBUUcsSUFBSSxDQUFDLG9FQUFvRUQ7UUFDbkY7UUFFQSx1Q0FBdUM7UUFDdkMsTUFBTW1CLFlBQVlsQyw2QkFBNkJIO1FBQy9DZ0IsUUFBUUMsR0FBRyxDQUFDLG1DQUFxRGpCLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBdUJvQyxPQUFwQnJDLE9BQU9FLE9BQU8sRUFBQyxPQUFlLE9BQVZtQztRQUNyRixPQUFPQTtJQUVULEVBQUUsT0FBT25CLE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBT2YsNkJBQTZCSDtJQUN0QztBQUNGO0FBRUEseURBQXlEO0FBQ3pELE1BQU04QixpQkFBaUIsQ0FBQ1E7SUFDdEIsTUFBTUMsVUFBcUM7UUFDekMsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxTQUFTO1FBQ1QsUUFBUTtRQUNSLFFBQVE7UUFDUixPQUFPO1FBQ1AsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztJQUNUO0lBQ0EsT0FBT0EsT0FBTyxDQUFDRCxPQUFPaEMsV0FBVyxHQUFHLElBQUk7QUFDMUM7QUFFQSx3RUFBd0U7QUFDeEUsTUFBTWtDLDRCQUE0QixPQUFPQyxRQUFnQkM7SUFDdkQsSUFBSTtRQUNGLGdEQUFnRDtRQUNoRCxJQUFJWixlQUFlVyxXQUFXWCxlQUFlWSxhQUFhO1lBQ3hELE1BQU1DLFdBQVdiLGVBQWVXO1lBQ2hDLE1BQU1HLGVBQWVkLGVBQWVZO1lBRXBDLElBQUlDLGFBQWFDLGNBQWMsT0FBTyxLQUFLLGdCQUFnQjtZQUUzRCx3Q0FBd0M7WUFDeEMsTUFBTW5DLFdBQVcsTUFBTUMsTUFBTSxxREFBK0VrQyxPQUExQkQsVUFBUyxtQkFBOEIsT0FBYkM7WUFDNUcsSUFBSW5DLFNBQVNFLEVBQUUsRUFBRTtvQkFFeUJDO2dCQUR4QyxNQUFNQSxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDLE1BQU1nQyxPQUFPRixZQUFZQyxnQkFBZWhDLGlCQUFBQSxJQUFJLENBQUMrQixTQUFTLGNBQWQvQixxQ0FBQUEsY0FBZ0IsQ0FBQ2dDLGFBQWEsR0FBRztnQkFDekUsSUFBSUMsT0FBTyxHQUFHO29CQUNaN0IsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ3lCLE9BQVZELFFBQU8sS0FBbUJJLE9BQWhCSCxZQUFXLE9BQVUsT0FBTEc7b0JBQzdELE9BQU9BO2dCQUNUO1lBQ0Y7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNQyxpQkFBaUJDLFlBQVlOO1FBQ25DLE1BQU1PLHFCQUFxQkQsWUFBWUw7UUFDdkMsTUFBTUcsT0FBT0MsaUJBQWlCRTtRQUU5QmhDLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEN5QixPQUFWRCxRQUFPLEtBQW1CSSxPQUFoQkgsWUFBVyxPQUFVLE9BQUxHLE1BQUs7UUFDM0UsT0FBT0E7SUFFVCxFQUFFLE9BQU8zQixPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyw0Q0FBNENBO1FBQzFELGlCQUFpQjtRQUNqQixNQUFNNEIsaUJBQWlCQyxZQUFZTjtRQUNuQyxNQUFNTyxxQkFBcUJELFlBQVlMO1FBQ3ZDLE9BQU9JLGlCQUFpQkU7SUFDMUI7QUFDRjtBQUVBLHFFQUFxRTtBQUNyRSxJQUFJQyxhQUF3QyxDQUFDO0FBQzdDLElBQUlDLGtCQUFrQjtBQUN0QixNQUFNQyx1QkFBdUIsTUFBTSx3Q0FBd0M7QUFFM0Usa0RBQWtEO0FBQ2xELElBQUlDLDRCQUE2RixFQUFFO0FBQ25HLE1BQU1DLGlCQUFpQixJQUFJLDRDQUE0QztBQUV2RSxvRkFBb0Y7QUFDcEYsTUFBTW5CLHNCQUFzQixPQUFPTztJQUNqQyxNQUFNYSxNQUFNQyxLQUFLRCxHQUFHO0lBQ3BCLE1BQU1FLFdBQVdmLE9BQU9uQyxXQUFXO0lBRW5DLHFDQUFxQztJQUNyQyxJQUFJMkMsVUFBVSxDQUFDTyxTQUFTLElBQUksTUFBT04sa0JBQW1CQyxzQkFBc0I7UUFDMUUsT0FBT0YsVUFBVSxDQUFDTyxTQUFTO0lBQzdCO0lBRUEsSUFBSTtRQUNGLGtEQUFrRDtRQUNsRCxNQUFNQyxjQUFjM0IsZUFBZVc7UUFDbkMsSUFBSWdCLGFBQWE7WUFDZixNQUFNaEQsV0FBVyxNQUFNQyxNQUFNLHFEQUFpRSxPQUFaK0MsYUFBWTtZQUM5RixJQUFJaEQsU0FBU0UsRUFBRSxFQUFFO29CQUVEQztnQkFEZCxNQUFNQSxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDLE1BQU1DLFNBQVFGLG9CQUFBQSxJQUFJLENBQUM2QyxZQUFZLGNBQWpCN0Msd0NBQUFBLGtCQUFtQjhDLEdBQUc7Z0JBQ3BDLElBQUk1QyxTQUFTQSxRQUFRLEdBQUc7b0JBQ3RCbUMsVUFBVSxDQUFDTyxTQUFTLEdBQUcxQztvQkFDdkJvQyxrQkFBa0JJO29CQUNsQnRDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBOENILE9BQVoyQixRQUFPLE9BQVcsT0FBTjNCO29CQUMxRCxPQUFPQTtnQkFDVDtZQUNGO1FBQ0Y7SUFDRixFQUFFLE9BQU9JLE9BQU87UUFDZEYsUUFBUUcsSUFBSSxDQUFDLDBDQUFpRCxPQUFQc0IsUUFBTyxzQkFBb0J2QjtJQUNwRjtJQUVBLHlDQUF5QztJQUN6QyxPQUFPNkIsWUFBWU47QUFDckI7QUFFQSx1RkFBdUY7QUFDdkYsTUFBTU0sY0FBYyxDQUFDTjtJQUNuQixNQUFNZSxXQUFXZixPQUFPbkMsV0FBVztJQUVuQyxtQ0FBbUM7SUFDbkMsSUFBSTJDLFVBQVUsQ0FBQ08sU0FBUyxFQUFFO1FBQ3hCLE9BQU9QLFVBQVUsQ0FBQ08sU0FBUztJQUM3QjtJQUVBLDRCQUE0QjtJQUM1QixPQUFPRyxrQkFBa0JsQjtBQUMzQjtBQUVBLHdEQUF3RDtBQUN4RCxNQUFNa0Isb0JBQW9CLENBQUNsQjtJQUN6QixNQUFNbUIsWUFBdUM7UUFDM0MsNERBQTREO1FBQzVELE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBQ1IsUUFBUTtRQUNSLFNBQVM7UUFDVCxPQUFPO1FBQ1AsUUFBUTtRQUNSLFFBQVE7UUFDUixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCxjQUFjO1FBQ2QsT0FBTztRQUNQLFFBQVE7UUFDUixPQUFPO1FBQ1AsT0FBTztRQUNQLFFBQVE7UUFDUixPQUFPO1FBQ1AsU0FBUztRQUNULFNBQVM7UUFDVCxPQUFPO1FBQ1AsT0FBTztRQUVQLHNCQUFzQjtRQUN0QixRQUFRO1FBQ1IsUUFBUTtRQUNSLFFBQVE7UUFDUixPQUFPO1FBQ1AsUUFBUTtRQUNSLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCx1QkFBdUI7UUFDdkIsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLFFBQVE7UUFFUixxQkFBcUI7UUFDckIsUUFBUTtRQUNSLFFBQVE7UUFDUixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCwyQkFBMkI7UUFDM0IsU0FBUztRQUNULFFBQVE7UUFDUixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsZ0JBQWdCO1FBQ2hCLE9BQU87UUFDUCxRQUFRO1FBRVIsYUFBYTtRQUNiLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUVQLGVBQWU7UUFDZixPQUFPO1FBQ1AsUUFBUTtRQUNSLFNBQVM7UUFDVCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUVQLHFCQUFxQjtRQUNyQixRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBQ1IsT0FBTztRQUNQLFNBQVM7UUFDVCxPQUFPO1FBQ1AsUUFBUTtRQUNSLE1BQU07UUFDTixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLFFBQVE7UUFFUixjQUFjO1FBQ2QsUUFBUTtRQUNSLFFBQVE7UUFDUixTQUFTO1FBQ1QsUUFBUTtRQUNSLE9BQU87SUFDVDtJQUNBLE9BQU9BLFNBQVMsQ0FBQ25CLE9BQU9uQyxXQUFXLEdBQUcsSUFBSTtBQUM1QztBQUVBLHVGQUF1RjtBQUN2RixNQUFNSCwrQkFBK0IsQ0FBQ0g7SUFDcEMsTUFBTWlDLGtCQUFrQmMsWUFBWS9DLE9BQU9DLE9BQU87SUFDbEQsTUFBTWtDLGtCQUFrQlksWUFBWS9DLE9BQU9FLE9BQU87SUFFbEQscUVBQXFFO0lBQ3JFLE1BQU0yRCxZQUFZNUIsa0JBQWtCRTtJQUVwQywrQkFBK0I7SUFDL0IsTUFBTTJCLGNBQWMsQ0FBQ0MsS0FBS0MsTUFBTSxLQUFLLEdBQUUsSUFBSyxNQUFNLE1BQU07SUFDeEQsTUFBTUMsYUFBYUosWUFBYSxLQUFJQyxXQUFVO0lBRTlDOUMsUUFBUUMsR0FBRyxDQUFDLDRDQUFzRGdCLE9BQXBCakMsT0FBT0MsT0FBTyxFQUFDLE9BQTJCRCxPQUF0QmlDLGlCQUFnQixRQUEwQkUsT0FBcEJuQyxPQUFPRSxPQUFPLEVBQUMsT0FBMkIrRCxPQUF0QjlCLGlCQUFnQixRQUE0QixPQUF0QjhCLFdBQVdDLE9BQU8sQ0FBQztJQUVySixPQUFPRDtBQUNUO0FBeUJBLE1BQU1FLG9CQUFtQztJQUN2Q0MsYUFBYTtJQUNibkUsU0FBUztJQUNUQyxTQUFTO0lBQ1RtRSxTQUFTO0lBQ1RDLFlBQVk7SUFDWkMsV0FBVztJQUNYQyxpQkFBaUI7SUFDakJDLDJCQUEyQjtJQUMzQkMsMkJBQTJCO0lBQzNCQyxxQkFBcUJ0Riw2REFBcUIsQ0FBQyxFQUFFO0FBQy9DO0FBRUEsTUFBTXVGLHNCQUFvQztJQUN4QzVFLFFBQVFtRTtJQUNSVSxpQkFBaUIsRUFBRTtJQUNuQkMsY0FBYyxFQUFFO0lBQ2hCQyxhQUFhM0YsNERBQW9CQTtJQUNqQzRGLG9CQUFvQmpGLDRCQUE0Qm9FO0lBQ2hELGdDQUFnQztJQUNoQ2MsaUJBQWlCO0lBQ2pCQyxnQkFBZ0I7SUFDaEJDLGdCQUFnQjtJQUNoQkMsbUJBQW1CO0lBQ25CQyxlQUFlO0FBQ2pCO0FBRUEsTUFBTUMsZ0NBQWdDLElBQUlDO0FBRTFDLHFDQUFxQztBQUNyQyxNQUFNQyxjQUFjO0FBRXBCLE1BQU1DLDBCQUEwQixDQUFDQztJQUMvQixJQUFJO1FBQ0YsSUFBSSxJQUE2QixFQUFFO1lBQ2pDLE1BQU1DLGNBQWM7Z0JBQ2xCM0YsUUFBUTBGLE1BQU0xRixNQUFNO2dCQUNwQjZFLGlCQUFpQmEsTUFBTWIsZUFBZTtnQkFDdENDLGNBQWNZLE1BQU1aLFlBQVk7Z0JBQ2hDQyxhQUFhVyxNQUFNWCxXQUFXO2dCQUM5QkMsb0JBQW9CVSxNQUFNVixrQkFBa0I7Z0JBQzVDRSxnQkFBZ0JRLE1BQU1SLGNBQWM7Z0JBQ3BDQyxnQkFBZ0JPLE1BQU1QLGNBQWM7Z0JBQ3BDQyxtQkFBbUJNLE1BQU1OLGlCQUFpQjtnQkFDMUNILGlCQUFpQlMsTUFBTVQsZUFBZTtnQkFDdENXLFdBQVdyQyxLQUFLRCxHQUFHO1lBQ3JCO1lBQ0F1QyxhQUFhQyxPQUFPLENBQUNOLGFBQWFPLEtBQUtDLFNBQVMsQ0FBQ0w7UUFDbkQ7SUFDRixFQUFFLE9BQU96RSxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyx5Q0FBeUNBO0lBQ3pEO0FBQ0Y7QUFFQSxNQUFNK0UsNEJBQTRCO0lBQ2hDLElBQUk7UUFDRixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsYUFBYUwsYUFBYU0sT0FBTyxDQUFDWDtZQUN4QyxJQUFJVSxZQUFZO2dCQUNkLE1BQU1FLFNBQVNMLEtBQUtNLEtBQUssQ0FBQ0g7Z0JBQzFCLDJDQUEyQztnQkFDM0MsSUFBSUUsT0FBT1IsU0FBUyxJQUFJLEtBQU10QyxHQUFHLEtBQUs4QyxPQUFPUixTQUFTLEdBQUksS0FBSyxLQUFLLEtBQUssTUFBTTtvQkFDN0UsT0FBT1E7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0YsRUFBRSxPQUFPbEYsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsMkNBQTJDQTtJQUMzRDtJQUNBLE9BQU87QUFDVDtBQUVBLE1BQU1vRixpQkFBaUIsQ0FBQ1osT0FBcUJhO0lBQzNDLE9BQVFBLE9BQU9DLElBQUk7UUFDakIsS0FBSztZQUNILE1BQU1DLFlBQVk7Z0JBQUUsR0FBR2YsTUFBTTFGLE1BQU07Z0JBQUUsR0FBR3VHLE9BQU9HLE9BQU87WUFBQztZQUN2RCxtRkFBbUY7WUFDbkYsSUFBSUgsT0FBT0csT0FBTyxDQUFDekcsT0FBTyxJQUFJc0csT0FBT0csT0FBTyxDQUFDeEcsT0FBTyxFQUFFO2dCQUNwRCxPQUFPO29CQUFFLEdBQUd3RixLQUFLO29CQUFFMUYsUUFBUXlHO29CQUFXekIsb0JBQW9CakYsNEJBQTRCMEc7Z0JBQVc7WUFDbkc7WUFDQSxPQUFPO2dCQUFFLEdBQUdmLEtBQUs7Z0JBQUUxRixRQUFReUc7WUFBVTtRQUN2QyxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHZixLQUFLO2dCQUFFYixpQkFBaUIwQixPQUFPRyxPQUFPLENBQUNDLElBQUksQ0FBQyxDQUFDQyxHQUFtQkMsSUFBc0JELEVBQUVFLFdBQVcsR0FBR0QsRUFBRUMsV0FBVyxFQUFFQyxHQUFHLENBQUMsQ0FBQ0MsS0FBcUJDLFFBQW1CO3dCQUFDLEdBQUdELEdBQUc7d0JBQUVFLFNBQVNELFFBQVE7b0JBQUM7WUFBSTtRQUMvTSxLQUFLO1lBQXdCO2dCQUMzQixNQUFNRSxVQUFVO3VCQUFJekIsTUFBTWIsZUFBZTtvQkFBRTBCLE9BQU9HLE9BQU87aUJBQUMsQ0FBQ0MsSUFBSSxDQUFDLENBQUNDLEdBQW1CQyxJQUFzQkQsRUFBRUUsV0FBVyxHQUFHRCxFQUFFQyxXQUFXLEVBQUVDLEdBQUcsQ0FBQyxDQUFDQyxLQUFxQkMsUUFBbUI7d0JBQUMsR0FBR0QsR0FBRzt3QkFBRUUsU0FBU0QsUUFBUTtvQkFBQztnQkFDak4sT0FBTztvQkFBRSxHQUFHdkIsS0FBSztvQkFBRWIsaUJBQWlCc0M7Z0JBQVE7WUFDOUM7UUFDQSxLQUFLO1lBQTJCO2dCQUM5QixNQUFNQyxjQUFjMUIsTUFBTWIsZUFBZSxDQUFDa0MsR0FBRyxDQUFDLENBQUNDLE1BQzdDQSxJQUFJSyxFQUFFLEtBQUtkLE9BQU9HLE9BQU8sQ0FBQ1csRUFBRSxHQUFHZCxPQUFPRyxPQUFPLEdBQUdNLEtBQ2hETCxJQUFJLENBQUMsQ0FBQ0MsR0FBbUJDLElBQXNCRCxFQUFFRSxXQUFXLEdBQUdELEVBQUVDLFdBQVcsRUFBRUMsR0FBRyxDQUFDLENBQUNDLEtBQXFCQyxRQUFtQjt3QkFBQyxHQUFHRCxHQUFHO3dCQUFFRSxTQUFTRCxRQUFRO29CQUFDO2dCQUN4SixPQUFPO29CQUFFLEdBQUd2QixLQUFLO29CQUFFYixpQkFBaUJ1QztnQkFBWTtZQUNsRDtRQUNBLEtBQUs7WUFBMkI7Z0JBQzlCLE1BQU1FLGVBQWU1QixNQUFNYixlQUFlLENBQUMwQyxNQUFNLENBQUMsQ0FBQ1AsTUFBd0JBLElBQUlLLEVBQUUsS0FBS2QsT0FBT0csT0FBTyxFQUFFQyxJQUFJLENBQUMsQ0FBQ0MsR0FBbUJDLElBQXNCRCxFQUFFRSxXQUFXLEdBQUdELEVBQUVDLFdBQVcsRUFBRUMsR0FBRyxDQUFDLENBQUNDLEtBQXFCQyxRQUFtQjt3QkFBQyxHQUFHRCxHQUFHO3dCQUFFRSxTQUFTRCxRQUFRO29CQUFDO2dCQUM1UCxPQUFPO29CQUFFLEdBQUd2QixLQUFLO29CQUFFYixpQkFBaUJ5QztnQkFBYTtZQUNuRDtRQUNBLEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUc1QixLQUFLO2dCQUFFWixjQUFjO29CQUFDeUIsT0FBT0csT0FBTzt1QkFBS2hCLE1BQU1aLFlBQVk7aUJBQUM7WUFBQztRQUMzRSxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHWSxLQUFLO2dCQUFFWixjQUFjLEVBQUU7WUFBQztRQUN0QyxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHWSxLQUFLO2dCQUFFWCxhQUFhO29CQUFFLEdBQUdXLE1BQU1YLFdBQVc7b0JBQUUsR0FBR3dCLE9BQU9HLE9BQU87Z0JBQUM7WUFBRTtRQUM5RSxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHaEIsS0FBSztnQkFBRVYsb0JBQW9CdUIsT0FBT0csT0FBTztZQUFDO1FBQ3hELG9DQUFvQztRQUNwQyxLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHaEIsS0FBSztnQkFDUlIsZ0JBQWdCcUIsT0FBT0csT0FBTyxDQUFDekcsT0FBTyxLQUFLdUgsWUFBWWpCLE9BQU9HLE9BQU8sQ0FBQ3pHLE9BQU8sR0FBR3lGLE1BQU1SLGNBQWM7Z0JBQ3BHQyxnQkFBZ0JvQixPQUFPRyxPQUFPLENBQUN4RyxPQUFPLEtBQUtzSCxZQUFZakIsT0FBT0csT0FBTyxDQUFDeEcsT0FBTyxHQUFHd0YsTUFBTVAsY0FBYztnQkFDcEdDLG1CQUFtQm1CLE9BQU9HLE9BQU8sQ0FBQ2hFLFVBQVUsS0FBSzhFLFlBQVlqQixPQUFPRyxPQUFPLENBQUNoRSxVQUFVLEdBQUdnRCxNQUFNTixpQkFBaUI7WUFDbEg7UUFDRixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHTSxLQUFLO2dCQUNSUixnQkFBZ0JxQixPQUFPRyxPQUFPLENBQUN6RyxPQUFPO2dCQUN0Q2tGLGdCQUFnQm9CLE9BQU9HLE9BQU8sQ0FBQ3hHLE9BQU87Z0JBQ3RDa0YsbUJBQW1CbUIsT0FBT0csT0FBTyxDQUFDaEUsVUFBVSxLQUFLOEUsWUFBWWpCLE9BQU9HLE9BQU8sQ0FBQ2hFLFVBQVUsR0FBR2dELE1BQU1OLGlCQUFpQjtZQUNsSDtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdNLEtBQUs7Z0JBQ1JOLG1CQUFtQm1CLE9BQU9HLE9BQU87WUFDbkM7UUFDRixLQUFLO1lBQ0gsTUFBTWUsaUJBQWlCO2dCQUFFLEdBQUcvQixNQUFNMUYsTUFBTTtZQUFDO1lBQ3pDLE9BQU87Z0JBQ0wsR0FBRzRFLG1CQUFtQjtnQkFDdEI1RSxRQUFReUg7Z0JBQ1IxQyxhQUFhO29CQUFFLEdBQUdXLE1BQU1YLFdBQVc7Z0JBQUM7Z0JBQ3BDQyxvQkFBb0JqRiw0QkFBNEIwSDtnQkFDaEQsbUVBQW1FO2dCQUNuRXZDLGdCQUFnQlEsTUFBTVIsY0FBYztnQkFDcENDLGdCQUFnQk8sTUFBTVAsY0FBYztnQkFDcENDLG1CQUFtQk0sTUFBTU4saUJBQWlCO1lBQzVDO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQUUsR0FBR00sS0FBSztnQkFBRUwsZUFBZWtCLE9BQU9HLE9BQU87WUFBQztRQUNuRCxLQUFLO1lBQ0gsb0RBQW9EO1lBQ3BELE9BQU87Z0JBQ0wsR0FBR2hCLEtBQUs7Z0JBQ1JULGlCQUFpQjtZQUVuQjtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUdTLEtBQUs7Z0JBQUVULGlCQUFpQjtZQUFVO1FBQ2hELEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUdTLEtBQUs7Z0JBQUVULGlCQUFpQjtZQUFVO1FBQ2hELEtBQUs7WUFDSCx3RUFBd0U7WUFDeEVLLDhCQUE4Qm9DLEtBQUs7WUFDbkMsNkRBQTZEO1lBQzdELE1BQU1DLGlCQUFpQmhJLGdFQUFjQSxDQUFDaUksV0FBVztZQUNqREQsZUFBZUUsbUJBQW1CO1lBQ2xDLE9BQU87Z0JBQ0wsR0FBR25DLEtBQUs7Z0JBQ1JULGlCQUFpQjtnQkFDakJKLGlCQUFpQixFQUFFO2dCQUNuQkMsY0FBYyxFQUFFO1lBQ2xCO1FBQ0YsS0FBSztZQUNILE9BQU87Z0JBQUUsR0FBR1ksS0FBSztnQkFBRWIsaUJBQWlCMEIsT0FBT0csT0FBTztZQUFDO1FBQ3JELEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUc5QixtQkFBbUI7Z0JBQ3RCNUUsUUFBUTBGLE1BQU0xRixNQUFNO2dCQUNwQnFGLGVBQWVLLE1BQU1MLGFBQWE7Z0JBQ2xDSixpQkFBaUI7Z0JBQ2pCRCxvQkFBb0I7Z0JBQ3BCLG1FQUFtRTtnQkFDbkVFLGdCQUFnQlEsTUFBTVIsY0FBYztnQkFDcENDLGdCQUFnQk8sTUFBTVAsY0FBYztnQkFDcENDLG1CQUFtQk0sTUFBTU4saUJBQWlCO1lBQzVDO1FBQ0Y7WUFDRSxPQUFPTTtJQUNYO0FBQ0Y7QUFpQkEsTUFBTW9DLCtCQUFpQmhKLG9EQUFhQSxDQUFpQzBJO0FBRXJFLDBDQUEwQztBQUVuQyxNQUFNTyxrQkFBa0I7UUFBQyxFQUFFQyxRQUFRLEVBQTJCOztJQUNuRSx1REFBdUQ7SUFDdkQsTUFBTUMsa0JBQWtCO1FBQ3RCLG9EQUFvRDtRQUNwRCxJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsWUFBWSxJQUFJQyxnQkFBZ0JDLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTtZQUM1RCxNQUFNQyxlQUFlTCxVQUFVTSxHQUFHLENBQUMsa0JBQWtCO1lBRXJELElBQUlELGNBQWM7Z0JBQ2hCdkgsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLHNGQUFzRjtnQkFDdEYsT0FBTzJEO1lBQ1Q7UUFDRjtRQUVBLDZFQUE2RTtRQUM3RSxNQUFNK0MsaUJBQWlCaEksZ0VBQWNBLENBQUNpSSxXQUFXO1FBQ2pELE1BQU1hLG1CQUFtQmQsZUFBZWUsbUJBQW1CO1FBRTNELDREQUE0RDtRQUM1RCxJQUFJLENBQUNELGtCQUFrQjtZQUNyQnpILFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU8yRDtRQUNUO1FBRUEsd0ZBQXdGO1FBQ3hGLE1BQU0rRCxpQkFBaUJoQixlQUFlaUIsV0FBVyxDQUFDSDtRQUNsRCxJQUFJRSxnQkFBZ0I7WUFDbEIzSCxRQUFRQyxHQUFHLENBQUMsbURBQW1EO2dCQUM3RDRILFdBQVdKO2dCQUNYSyxNQUFNSCxlQUFlRyxJQUFJO2dCQUN6QkMsVUFBVUosZUFBZUksUUFBUTtnQkFDakM5SSxTQUFTMEksZUFBZXpELGNBQWM7Z0JBQ3RDaEYsU0FBU3lJLGVBQWV4RCxjQUFjO2dCQUN0Q3pDLFlBQVlpRyxlQUFldkQsaUJBQWlCO2dCQUM1Q1AsaUJBQWlCOEQsZUFBZTlELGVBQWUsQ0FBQ21FLE1BQU07Z0JBQ3REbEUsY0FBYzZELGVBQWU3RCxZQUFZLENBQUNrRSxNQUFNO1lBQ2xEO1lBRUEsb0VBQW9FO1lBQ3BFLElBQUlMLGVBQWVJLFFBQVEsRUFBRTtnQkFDM0IvSCxRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLE9BQU87Z0JBQ0wsR0FBRzJELG1CQUFtQjtnQkFDdEI1RSxRQUFRMkksZUFBZTNJLE1BQU07Z0JBQzdCNkUsaUJBQWlCOEQsZUFBZTlELGVBQWU7Z0JBQy9DQyxjQUFjNkQsZUFBZTdELFlBQVk7Z0JBQ3pDRSxvQkFBb0IyRCxlQUFlM0Qsa0JBQWtCO2dCQUNyREUsZ0JBQWdCeUQsZUFBZXpELGNBQWM7Z0JBQzdDQyxnQkFBZ0J3RCxlQUFleEQsY0FBYztnQkFDN0NDLG1CQUFtQnVELGVBQWV2RCxpQkFBaUI7Z0JBQ25ESCxpQkFBa0IwRCxlQUFlSSxRQUFRLEdBQUcsWUFBWTtZQUMxRDtRQUNGO1FBRUEsb0RBQW9EO1FBQ3BELE1BQU03QyxhQUFhRDtRQUNuQixJQUFJQyxZQUFZO1lBQ2QsT0FBTztnQkFBRSxHQUFHdEIsbUJBQW1CO2dCQUFFLEdBQUdzQixVQUFVO1lBQUM7UUFDakQ7UUFDQSxPQUFPdEI7SUFDVDtJQUVBLE1BQU0sQ0FBQ2MsT0FBT3VELFNBQVMsR0FBR2pLLGlEQUFVQSxDQUFDc0gsZ0JBQWdCMkI7SUFDckQsTUFBTSxFQUFFaUIsS0FBSyxFQUFFLEdBQUd6SiwwREFBUUE7SUFFMUIsZ0ZBQWdGO0lBQ2hGUixnREFBU0E7cUNBQUM7WUFDUixJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLE1BQU1pSixZQUFZLElBQUlDLGdCQUFnQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO2dCQUM1RCxNQUFNQyxlQUFlTCxVQUFVTSxHQUFHLENBQUMsa0JBQWtCO2dCQUVyRCxJQUFJRCxjQUFjO29CQUNoQiw2Q0FBNkM7b0JBQzdDLE1BQU1ZLFNBQVNmLE9BQU9DLFFBQVEsQ0FBQ2UsUUFBUTtvQkFDdkNoQixPQUFPaUIsT0FBTyxDQUFDQyxZQUFZLENBQUMsQ0FBQyxHQUFHLElBQUlIO2dCQUN0QztZQUNGO1FBQ0Y7b0NBQUcsRUFBRSxHQUFHLHlCQUF5QjtJQUNqQyxNQUFNSSxXQUFXcEssNkNBQU1BLENBQTBCO0lBQ2pELGdFQUFnRTtJQUVoRSw4RkFBOEY7SUFDOUYsTUFBTXFLLDJCQUEyQnRLLGtEQUFXQTtpRUFBQyxlQUFPdUs7Z0JBQWlCQywyRUFBbUIsT0FBT0MsOEVBQXFCO1lBQ2xILElBQUk7Z0JBQ0YsTUFBTUMsZ0JBQWdCL0QsYUFBYU0sT0FBTyxDQUFDO2dCQUMzQyxNQUFNMEQsaUJBQWlCaEUsYUFBYU0sT0FBTyxDQUFDO2dCQUU1QyxJQUFJLENBQUN5RCxpQkFBaUIsQ0FBQ0MsZ0JBQWdCO29CQUNyQzdJLFFBQVFDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRjtnQkFFQSxrREFBa0Q7Z0JBQ2xELE1BQU02SSxpQkFBaUJsSyxnRUFBY0EsQ0FBQ2dJLFdBQVc7Z0JBQ2pELElBQUksQ0FBQ2tDLGVBQWVDLFNBQVMsR0FBR0MsUUFBUSxFQUFFO29CQUN4QyxvREFBb0Q7b0JBQ3BELElBQUk1RywwQkFBMEI0RixNQUFNLEdBQUczRixnQkFBZ0I7d0JBQ3JERCwwQkFBMEI2RyxJQUFJLENBQUM7NEJBQzdCUjs0QkFDQTdELFdBQVdyQyxLQUFLRCxHQUFHOzRCQUNuQm9HO3dCQUNGO3dCQUNBMUksUUFBUUMsR0FBRyxDQUFDLGdFQUFnRW1DLDBCQUEwQjRGLE1BQU0sRUFBRTtvQkFDaEgsT0FBTzt3QkFDTGhJLFFBQVFHLElBQUksQ0FBQzt3QkFDYmlDLDBCQUEwQjhHLEtBQUssSUFBSSxnQkFBZ0I7d0JBQ25EOUcsMEJBQTBCNkcsSUFBSSxDQUFDOzRCQUFFUjs0QkFBUzdELFdBQVdyQyxLQUFLRCxHQUFHOzRCQUFJb0c7d0JBQVE7b0JBQzNFO29CQUNBO2dCQUNGO2dCQUVBLDBDQUEwQztnQkFDMUMsTUFBTVMsYUFBYSxJQUFJQztnQkFDdkIsTUFBTUMsWUFBWUM7dUZBQVcsSUFBTUgsV0FBV0ksS0FBSztzRkFBSSxRQUFRLG9CQUFvQjtnQkFFbkYsTUFBTTlKLFdBQVcsTUFBTUMsTUFBTSwrQkFBNkMsT0FBZGtKLGVBQWMsaUJBQWU7b0JBQ3ZGWSxRQUFRO29CQUNSQyxTQUFTO3dCQUFFLGdCQUFnQjtvQkFBbUI7b0JBQzlDQyxNQUFNM0UsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQjJFLFNBQVNkO3dCQUNUZSxNQUFNbkI7d0JBQ05vQixZQUFZO29CQUNkO29CQUNBQyxRQUFRWCxXQUFXVyxNQUFNO2dCQUMzQjtnQkFFQUMsYUFBYVY7Z0JBRWIsSUFBSSxDQUFDNUosU0FBU0UsRUFBRSxFQUFFO29CQUNoQkssUUFBUUUsS0FBSyxDQUFDLHlDQUF5Q1QsU0FBU3VLLFVBQVU7b0JBRTFFLDBDQUEwQztvQkFDMUMsSUFBSSxDQUFDdEIsV0FBV0MsYUFBYSxHQUFHO3dCQUM5QjNJLFFBQVFDLEdBQUcsQ0FBQyx3REFBNkQsT0FBZjBJLGFBQWEsR0FBRTt3QkFDekVXO3FGQUFXO2dDQUNUZCx5QkFBeUJDLFNBQVNDLFNBQVNDLGFBQWE7NEJBQzFEO29GQUFHLE9BQVFBLENBQUFBLGFBQWEsS0FBSywrQkFBK0I7d0JBQzVEO29CQUNGO29CQUVBLDBEQUEwRDtvQkFDMUQsSUFBSSxDQUFDRCxXQUFXQyxjQUFjLEdBQUc7d0JBQy9CM0ksUUFBUUUsS0FBSyxDQUFDO29CQUNoQjtnQkFDRixPQUFPO29CQUNMRixRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRixFQUFFLE9BQU9DLE9BQU87Z0JBQ2RGLFFBQVFFLEtBQUssQ0FBQyx3Q0FBd0NBO2dCQUV0RCxrRkFBa0Y7Z0JBQ2xGLElBQUksQ0FBQ3dJLFdBQVdDLGFBQWEsR0FBRztvQkFDOUIzSSxRQUFRQyxHQUFHLENBQUMsNEVBQWlGLE9BQWYwSSxhQUFhLEdBQUU7b0JBQzdGVztpRkFBVzs0QkFDVGQseUJBQXlCQyxTQUFTQyxTQUFTQyxhQUFhO3dCQUMxRDtnRkFBRyxRQUFTQSxDQUFBQSxhQUFhLEtBQUssOENBQThDO29CQUM1RTtnQkFDRjtnQkFFQSxJQUFJLENBQUNELFdBQVdDLGNBQWMsR0FBRztvQkFDL0IzSSxRQUFRRSxLQUFLLENBQUM7Z0JBQ2hCO1lBQ0Y7UUFDRjtnRUFBRyxFQUFFO0lBRUwsd0NBQXdDO0lBQ3hDLE1BQU0rSixnQ0FBZ0MvTCxrREFBV0E7c0VBQUMsT0FBT2dNLFdBQW1CQztZQUMxRSxNQUFNdkYsWUFBWSxJQUFJckMsT0FBTzZILGNBQWM7WUFDM0MsTUFBTTNCLFVBQVUsOEJBQ0QsNkJBQTZCLE9BQVZ5QixXQUFVLFFBQzdCLGdDQUFtQyxPQUFiQyxjQUFhLFFBQ25DLGtCQUE0QixPQUFWdkYsV0FBVSxRQUM1Qiw0QkFBMENGLE9BQXhCQSxNQUFNMUYsTUFBTSxDQUFDQyxPQUFPLEVBQUMsS0FBMkJ5RixPQUF4QkEsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDLEtBQTRCLE9BQXpCd0YsTUFBTTFGLE1BQU0sQ0FBQ29FLFdBQVc7WUFFekcsTUFBTW9GLHlCQUF5QkMsU0FBUyxPQUFPLHFDQUFxQztRQUN0RjtxRUFBRztRQUFDL0QsTUFBTTFGLE1BQU07UUFBRXdKO0tBQXlCO0lBRTNDLE1BQU02Qiw2QkFBNkJuTSxrREFBV0E7bUVBQUMsT0FBT3VELFFBQWdCNkksZ0JBQXdCQztZQUM1RixNQUFNM0YsWUFBWSxJQUFJckMsT0FBTzZILGNBQWM7WUFDM0MsTUFBTTNCLFVBQVUsOENBQ0QsaUNBQThCLE9BQVBoSCxRQUFPLFFBQzlCLHdDQUEyREEsT0FBN0I2SSxlQUFlcEgsT0FBTyxDQUFDLElBQUcsS0FBVSxPQUFQekIsUUFBTyxRQUNsRSw2QkFBMERBLE9BQTdCOEksZUFBZXJILE9BQU8sQ0FBQyxJQUFHLEtBQVUsT0FBUHpCLFFBQU8sUUFDakUsaUNBQXVFQSxPQUFoRCxDQUFDOEksaUJBQWlCRCxjQUFhLEVBQUdwSCxPQUFPLENBQUMsSUFBRyxLQUFVLE9BQVB6QixRQUFPLFFBQzlFLGtCQUE0QixPQUFWbUQsV0FBVSxRQUM1Qiw0QkFBMENGLE9BQXhCQSxNQUFNMUYsTUFBTSxDQUFDQyxPQUFPLEVBQUMsS0FBMkJ5RixPQUF4QkEsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDLEtBQTRCLE9BQXpCd0YsTUFBTTFGLE1BQU0sQ0FBQ29FLFdBQVc7WUFFekcsTUFBTW9GLHlCQUF5QkM7UUFDakM7a0VBQUc7UUFBQy9ELE1BQU0xRixNQUFNO1FBQUV3SjtLQUF5QjtJQUUzQyxNQUFNZ0MsMkJBQTJCdE0sa0RBQVdBO2lFQUFDLE9BQU91TSxTQUFpQkM7WUFDbkUsTUFBTTlGLFlBQVksSUFBSXJDLE9BQU82SCxjQUFjO1lBQzNDLE1BQU0zQixVQUFVLDRDQUNELDRCQUEwQixPQUFSZ0MsU0FBUSxRQUMxQixtQkFBZ0MsT0FBYkMsY0FBYSxRQUNoQyxrQkFBNEIsT0FBVjlGLFdBQVUsUUFDNUIsNEJBQTBDRixPQUF4QkEsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTyxFQUFDLEtBQTJCeUYsT0FBeEJBLE1BQU0xRixNQUFNLENBQUNFLE9BQU8sRUFBQyxLQUE0QixPQUF6QndGLE1BQU0xRixNQUFNLENBQUNvRSxXQUFXO1lBRXpHLE1BQU1vRix5QkFBeUJDO1FBQ2pDO2dFQUFHO1FBQUMvRCxNQUFNMUYsTUFBTTtRQUFFd0o7S0FBeUI7SUFFM0MsdURBQXVEO0lBQ3ZELE1BQU1tQyxtQkFBbUJ6TSxrREFBV0E7eURBQUM7WUFDbkMsSUFBSTtnQkFDRixxREFBcUQ7Z0JBQ3JELElBQUksQ0FBQ3dHLE1BQU0xRixNQUFNLENBQUNDLE9BQU8sSUFBSSxDQUFDeUYsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTyxFQUFFO29CQUNsRCtJLFNBQVM7d0JBQUV6QyxNQUFNO3dCQUFvQkUsU0FBUztvQkFBRTtvQkFDaEQ7Z0JBQ0Y7Z0JBRUEsSUFBSTVGO2dCQUVKLGdGQUFnRjtnQkFDaEYsSUFBSTRFLE1BQU0xRixNQUFNLENBQUNvRSxXQUFXLEtBQUssa0JBQWtCO29CQUNqRCxvQ0FBb0M7b0JBQ3BDLElBQUk7d0JBQ0YsTUFBTW5DLGtCQUFrQixNQUFNQyxvQkFBb0J3RCxNQUFNMUYsTUFBTSxDQUFDQyxPQUFPO3dCQUN0RSxNQUFNa0Msa0JBQWtCLE1BQU1ELG9CQUFvQndELE1BQU0xRixNQUFNLENBQUNFLE9BQU87d0JBQ3RFWSxRQUFRbUIsa0JBQWtCRTt3QkFDMUJuQixRQUFRQyxHQUFHLENBQUMsb0ZBQzhCZ0IsT0FBcEN5RCxNQUFNMUYsTUFBTSxDQUFDQyxPQUFPLEVBQUMsaUJBQ3JCeUYsT0FEb0N6RCxnQkFBZ0JtSixjQUFjLElBQUcsb0JBQ2pDakosT0FBcEN1RCxNQUFNMUYsTUFBTSxDQUFDRSxPQUFPLEVBQUMsaUJBQ1B3RixPQURzQnZELGdCQUFnQmlKLGNBQWMsSUFBRyxrQ0FDL0IxRixPQUF4QkEsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTyxFQUFDLEtBQTZCYSxPQUExQjRFLE1BQU0xRixNQUFNLENBQUNFLE9BQU8sRUFBQyxPQUM5QytCLE9BRG1EbkIsTUFBTW9ELE9BQU8sQ0FBQyxJQUFHLGlDQUMvQy9CLE9BQXJCRixpQkFBZ0IsVUFBMEJuQixPQUFyQnFCLGlCQUFnQixPQUFzQixPQUFqQnJCLE1BQU1vRCxPQUFPLENBQUM7b0JBQzdFLEVBQUUsT0FBT2hELE9BQU87d0JBQ2QsbUNBQW1DO3dCQUNuQyxNQUFNZSxrQkFBa0JjLFlBQVkyQyxNQUFNMUYsTUFBTSxDQUFDQyxPQUFPO3dCQUN4RCxNQUFNa0Msa0JBQWtCWSxZQUFZMkMsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTzt3QkFDeERZLFFBQVFtQixrQkFBa0JFO3dCQUMxQm5CLFFBQVFDLEdBQUcsQ0FBQyxtRkFDOEJnQixPQUFwQ3lELE1BQU0xRixNQUFNLENBQUNDLE9BQU8sRUFBQyxpQkFDckJ5RixPQURvQ3pELGdCQUFnQm1KLGNBQWMsSUFBRyxvQkFDakNqSixPQUFwQ3VELE1BQU0xRixNQUFNLENBQUNFLE9BQU8sRUFBQyxpQkFDUHdGLE9BRHNCdkQsZ0JBQWdCaUosY0FBYyxJQUFHLGtDQUMvQjFGLE9BQXhCQSxNQUFNMUYsTUFBTSxDQUFDQyxPQUFPLEVBQUMsS0FBNkJhLE9BQTFCNEUsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDLE9BQXNCLE9BQWpCWSxNQUFNb0QsT0FBTyxDQUFDO29CQUN0RjtnQkFDRixPQUFPO29CQUNMLDJEQUEyRDtvQkFDM0RwRCxRQUFRLE1BQU1WLHNCQUFzQnNGLE1BQU0xRixNQUFNO2dCQUNsRDtnQkFFQWlKLFNBQVM7b0JBQUV6QyxNQUFNO29CQUFvQkUsU0FBUzVGO2dCQUFNO1lBQ3RELEVBQUUsT0FBT0ksT0FBTztnQkFDZEYsUUFBUUUsS0FBSyxDQUFDLGlDQUFpQ0E7Z0JBQy9Dc0sseUJBQXlCLGFBQWEsaUNBQXVDLE9BQU50SyxRQUFTMEssS0FBSyxDQUFDNUssUUFBUUUsS0FBSztZQUNyRztRQUNGO3dEQUFHO1FBQUN3RSxNQUFNMUYsTUFBTTtRQUFFaUo7S0FBUztJQUUzQix1RUFBdUU7SUFDdkVoSyxnREFBU0E7cUNBQUM7WUFDUixnQkFBZ0I7WUFDaEIwTTtZQUVBLHdFQUF3RTtZQUN4RSxNQUFNRSx3QkFBd0JDO21FQUFZO29CQUN4QyxNQUFNaEMsaUJBQWlCbEssZ0VBQWNBLENBQUNnSSxXQUFXO29CQUNqRCxJQUFJa0MsZUFBZUMsU0FBUyxHQUFHQyxRQUFRLEVBQUU7d0JBQ3ZDLElBQUk7NEJBQ0YsK0RBQStEOzRCQUMvRCxNQUFNMkI7d0JBQ1IsRUFBRSxPQUFPekssT0FBTzs0QkFDZEYsUUFBUUcsSUFBSSxDQUFDLHdDQUF3Q0Q7NEJBQ3JELG1EQUFtRDs0QkFDbkQsTUFBTStKLDhCQUE4QixzQkFBc0Isb0NBQTBDLE9BQU4vSjt3QkFDaEc7b0JBQ0Y7Z0JBQ0Y7a0VBQUcsT0FBTyxzQ0FBc0M7WUFFaEQ7NkNBQU87b0JBQ0w2SyxjQUFjRjtnQkFDaEI7O1FBQ0Y7b0NBQUc7UUFBQ0Y7UUFBa0JWO0tBQThCO0lBRXBELHNEQUFzRDtJQUN0RGhNLGdEQUFTQTtxQ0FBQztZQUNSLElBQUksSUFBNkIsRUFBRTtnQkFDakNzSyxTQUFTeUMsT0FBTyxHQUFHLElBQUlDO2dCQUV2QixnREFBZ0Q7Z0JBQ2hELE1BQU1DOzZEQUFjO3dCQUNsQixJQUFJM0MsU0FBU3lDLE9BQU8sRUFBRTs0QkFDcEIscURBQXFEOzRCQUNyRHpDLFNBQVN5QyxPQUFPLENBQUNHLE1BQU0sR0FBRzs0QkFDMUI1QyxTQUFTeUMsT0FBTyxDQUFDSSxJQUFJLEdBQUdDLElBQUk7eUVBQUM7b0NBQzNCLElBQUk5QyxTQUFTeUMsT0FBTyxFQUFFO3dDQUNwQnpDLFNBQVN5QyxPQUFPLENBQUNHLE1BQU0sR0FBRztvQ0FDNUI7b0NBQ0FuTCxRQUFRQyxHQUFHLENBQUM7Z0NBQ2Q7d0VBQUcySyxLQUFLO3lFQUFDO2dDQUNQLDhCQUE4QjtnQ0FDaEM7O3dCQUNGO3dCQUNBLDhDQUE4Qzt3QkFDOUNVLFNBQVNDLG1CQUFtQixDQUFDLFNBQVNMO3dCQUN0Q0ksU0FBU0MsbUJBQW1CLENBQUMsV0FBV0w7b0JBQzFDOztnQkFFQSxvQ0FBb0M7Z0JBQ3BDSSxTQUFTRSxnQkFBZ0IsQ0FBQyxTQUFTTjtnQkFDbkNJLFNBQVNFLGdCQUFnQixDQUFDLFdBQVdOO2dCQUVyQztpREFBTzt3QkFDTEksU0FBU0MsbUJBQW1CLENBQUMsU0FBU0w7d0JBQ3RDSSxTQUFTQyxtQkFBbUIsQ0FBQyxXQUFXTDtvQkFDMUM7O1lBQ0Y7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsTUFBTU8sWUFBWXZOLGtEQUFXQTtrREFBQyxDQUFDd047WUFDN0Isd0VBQXdFO1lBQ3hFLE1BQU0vRSxpQkFBaUJoSSxnRUFBY0EsQ0FBQ2lJLFdBQVc7WUFDakQsTUFBTWEsbUJBQW1CZCxlQUFlZSxtQkFBbUI7WUFDM0QsSUFBSWlFLGdCQUFnQmpILE1BQU1YLFdBQVcsRUFBRSxtQkFBbUI7WUFFMUQsSUFBSTBELGtCQUFrQjtnQkFDcEIsTUFBTUUsaUJBQWlCaEIsZUFBZWlCLFdBQVcsQ0FBQ0g7Z0JBQ2xELElBQUlFLGtCQUFrQkEsZUFBZWdFLGFBQWEsRUFBRTtvQkFDbERBLGdCQUFnQmhFLGVBQWVnRSxhQUFhO2dCQUM5QztZQUNGO1lBRUEsSUFBSUEsY0FBY0Msa0JBQWtCLElBQUlyRCxTQUFTeUMsT0FBTyxFQUFFO2dCQUN4RCxJQUFJYTtnQkFDSixJQUFJSCxhQUFhLHlCQUF5QkMsY0FBY0cscUJBQXFCLEVBQUU7b0JBQzdFRCxZQUFZRixjQUFjSSxtQkFBbUI7Z0JBQy9DLE9BQU8sSUFBSUwsYUFBYSxnQkFBZ0JDLGNBQWNLLFlBQVksRUFBRTtvQkFDbEVILFlBQVlGLGNBQWNNLFVBQVU7Z0JBQ3RDO2dCQUVBLElBQUlKLFdBQVc7b0JBQ2IsSUFBSTt3QkFDRix5Q0FBeUM7d0JBQ3pDdEQsU0FBU3lDLE9BQU8sQ0FBQ2tCLEtBQUs7d0JBQ3RCM0QsU0FBU3lDLE9BQU8sQ0FBQ21CLFdBQVcsR0FBRzt3QkFFL0Isb0NBQW9DO3dCQUNwQzVELFNBQVN5QyxPQUFPLENBQUNvQixHQUFHLEdBQUdQO3dCQUN2QnRELFNBQVN5QyxPQUFPLENBQUNxQixJQUFJLElBQUksNEJBQTRCO3dCQUVyRCxzQ0FBc0M7d0JBQ3RDOUQsU0FBU3lDLE9BQU8sQ0FBQ3NCLE9BQU87c0VBQUcsQ0FBQ0M7Z0NBQzFCdk0sUUFBUUcsSUFBSSxDQUFDLCtCQUF5QyxPQUFWMEwsV0FBVSxNQUFJVTtnQ0FDMUQscUJBQXFCO2dDQUNyQixJQUFJVixjQUFjLHdCQUF3QnRELFNBQVN5QyxPQUFPLEVBQUU7b0NBQzFEekMsU0FBU3lDLE9BQU8sQ0FBQ29CLEdBQUcsR0FBRztvQ0FDdkI3RCxTQUFTeUMsT0FBTyxDQUFDcUIsSUFBSTtnQ0FDdkI7NEJBQ0Y7O3dCQUVBLGlEQUFpRDt3QkFDakQsTUFBTUcsY0FBY2pFLFNBQVN5QyxPQUFPLENBQUNJLElBQUk7d0JBRXpDLElBQUlvQixnQkFBZ0JoRyxXQUFXOzRCQUM3QmdHLFlBQVluQixJQUFJOzBFQUFDO29DQUNmLG1EQUFtRDtvQ0FDbkQvQjtrRkFBVzs0Q0FDVCxJQUFJZixTQUFTeUMsT0FBTyxJQUFJLENBQUN6QyxTQUFTeUMsT0FBTyxDQUFDeUIsTUFBTSxFQUFFO2dEQUNoRGxFLFNBQVN5QyxPQUFPLENBQUNrQixLQUFLO2dEQUN0QjNELFNBQVN5QyxPQUFPLENBQUNtQixXQUFXLEdBQUcsR0FBRyxzQkFBc0I7NENBQzFEO3dDQUNGO2lGQUFHLE9BQU8sWUFBWTtnQ0FDeEI7eUVBQUd2QixLQUFLOzBFQUFDOEIsQ0FBQUE7b0NBQ1Asb0RBQW9EO29DQUNwRCxNQUFNdkMsZUFBZXVDLElBQUlqRSxPQUFPLElBQUlrRSxPQUFPRDtvQ0FFM0MsSUFBSXZDLGFBQWF5QyxRQUFRLENBQUMsNEJBQTRCekMsYUFBYXlDLFFBQVEsQ0FBQyxhQUFhO3dDQUN2RjVNLFFBQVFDLEdBQUcsQ0FBQztvQ0FDWix5REFBeUQ7b0NBQzNELE9BQU8sSUFBSWtLLGFBQWF5QyxRQUFRLENBQUMsa0JBQWtCekMsYUFBYXlDLFFBQVEsQ0FBQyxxQkFBcUI7b0NBQzVGLDRDQUE0QztvQ0FDOUMsT0FBTzt3Q0FDTDVNLFFBQVFHLElBQUksQ0FBQyw2QkFBNkJnSztvQ0FDNUM7Z0NBQ0Y7O3dCQUNGO29CQUNGLEVBQUUsT0FBT2pLLE9BQU87d0JBQ2RGLFFBQVFHLElBQUksQ0FBQyw2QkFBdUMsT0FBVjBMLFdBQVUsTUFBSTNMO29CQUMxRDtnQkFDRjtZQUNGO1FBQ0Y7aURBQUc7UUFBQ3dFLE1BQU1YLFdBQVc7S0FBQztJQUV0QiwwREFBMEQ7SUFDMUQ5RixnREFBU0E7cUNBQUM7UUFDUiwwREFBMEQ7UUFDMUQseURBQXlEO1FBQ3pELDBEQUEwRDtRQUMxRCxrRkFBa0Y7UUFDbEYsNkJBQTZCO1FBQzdCLDJGQUEyRjtRQUMzRiw2Q0FBNkM7UUFDL0M7b0NBQUc7UUFBQ3lHLE1BQU0xRixNQUFNLENBQUNDLE9BQU87UUFBRXlGLE1BQU0xRixNQUFNLENBQUNFLE9BQU87S0FBQyxHQUFHLGtEQUFrRDtJQUVwRyxNQUFNMk4sa0JBQWtCM08sa0RBQVdBO3dEQUFDLENBQUM0TztZQUNuQyxJQUFJLENBQUNBLFVBQVUsQ0FBQ0MsTUFBTUMsT0FBTyxDQUFDRixTQUFTO1lBRXZDLG1FQUFtRTtZQUNuRSxNQUFNRyxlQUFlO21CQUFJSDthQUFPLENBQUN2RyxNQUFNOzZFQUFDLENBQUN6RyxRQUFrQixDQUFDb04sTUFBTXBOLFVBQVVBLFFBQVE7NEVBQUc2RixJQUFJOzZFQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDOztZQUUxRyxNQUFNTSxVQUE0QjhHLGFBQWFsSCxHQUFHO3dFQUFDLENBQUNqRyxPQUFlbUc7b0JBQy9ELE1BQU1rSCxjQUFjekksTUFBTWIsZUFBZSxDQUFDdUosSUFBSTs0RkFBQyxDQUFDQyxJQUFzQkEsRUFBRXZILFdBQVcsS0FBS2hHOztvQkFDeEYsSUFBSXFOLGFBQWE7d0JBQ2YsMkRBQTJEO3dCQUMzRCxPQUFPOzRCQUFFLEdBQUdBLFdBQVc7NEJBQUVqSCxTQUFTRCxRQUFRO3dCQUFFO29CQUM5QztvQkFFQSxPQUFPO3dCQUNMSSxJQUFJOUgsZ0RBQU1BO3dCQUNWMkgsU0FBU0QsUUFBUTt3QkFDakJxSCxRQUFRO3dCQUNSQyxZQUFZO3dCQUNaQyxZQUFZOUksTUFBTTFGLE1BQU0sQ0FBQ3FFLE9BQU87d0JBQ2hDeUMsYUFBYWhHO29CQUNmO2dCQUNGOztZQUNGbUksU0FBUztnQkFBRXpDLE1BQU07Z0JBQXlCRSxTQUFTUztZQUFRO1FBQzdEO3VEQUFHO1FBQUN6QixNQUFNYixlQUFlO1FBQUVhLE1BQU0xRixNQUFNLENBQUNxRSxPQUFPO1FBQUU0RTtLQUFTO0lBRTFELDhEQUE4RDtJQUM5RGhLLGdEQUFTQTtxQ0FBQztZQUNSLDZCQUE2QjtZQUM3QixNQUFNNkssaUJBQWlCbEssZ0VBQWNBLENBQUNnSSxXQUFXO1lBQ2pELE1BQU1vQyxXQUFXRixlQUFlQyxTQUFTLEdBQUdDLFFBQVE7WUFFcEQscURBQXFEO1lBQ3JELElBQUl0RSxNQUFNVCxlQUFlLEtBQUssYUFDMUJTLE1BQU1iLGVBQWUsQ0FBQ21FLE1BQU0sS0FBSyxLQUNqQ3RELE1BQU1WLGtCQUFrQixJQUFJLEtBQzVCLENBQUNnRixVQUFVO2dCQUViLElBQUksQ0FBQ0EsWUFBWXRFLE1BQU1ULGVBQWUsS0FBSyxXQUFXO29CQUNwRGpFLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtnQkFDQTtZQUNGO1lBRUEsd0VBQXdFO1lBQ3hFLE1BQU0sRUFBRWpCLE1BQU0sRUFBRWdGLGtCQUFrQixFQUFFSCxlQUFlLEVBQUVLLGNBQWMsRUFBRUMsY0FBYyxFQUFFLEdBQUdPO1lBQ3hGLE1BQU0rSSxxQkFBcUI7bUJBQUk1SjthQUFnQixDQUFDOEIsSUFBSTtnRUFBQyxDQUFDQyxHQUFtQkMsSUFBc0JELEVBQUVFLFdBQVcsR0FBR0QsRUFBRUMsV0FBVzs7WUFFNUgsK0RBQStEO1lBQy9ELElBQUk0SCx3QkFBd0J4SjtZQUM1QixJQUFJeUosd0JBQXdCeEo7WUFDNUIsSUFBSXlKLGVBQWU7WUFFbkI1TixRQUFRQyxHQUFHLENBQUMsMkNBQTZFd04sT0FBNUN6SixtQkFBbUJkLE9BQU8sQ0FBQyxJQUFHLGdCQUF1RHlLLE9BQXpDRixtQkFBbUJ6RixNQUFNLEVBQUMsaUJBQXdDaEosT0FBekIyTyx1QkFBc0IsS0FBa0IsT0FBZjNPLE9BQU9FLE9BQU87WUFFekssa0NBQWtDO1lBQ2xDLE1BQU0yTyxpQkFBaUJKLG1CQUFtQmxILE1BQU07NERBQUNQLENBQUFBO29CQUMvQyxNQUFNOEgsY0FBYyxLQUFNQyxHQUFHLENBQUMvSixxQkFBcUJnQyxJQUFJRixXQUFXLElBQUk5QixxQkFBc0I7b0JBQzVGLE9BQU84SixlQUFlOU8sT0FBT3dFLGVBQWU7Z0JBQzlDOztZQUVBLElBQUlxSyxlQUFlN0YsTUFBTSxHQUFHLEdBQUc7Z0JBQzdCaEksUUFBUUMsR0FBRyxDQUFDLHNDQUFnRCxPQUF2QmpCLE9BQU93RSxlQUFlLEVBQUMsUUFBTXFLLGVBQWU5SCxHQUFHO2lEQUFDQyxDQUFBQSxNQUFPLFdBQTJCQSxPQUFoQkEsSUFBSUUsT0FBTyxFQUFDLE1BQWUsT0FBWEYsSUFBSXNILE1BQU0sRUFBQzs7WUFDcEk7WUFFQSw0REFBNEQ7WUFDNUQsSUFBSyxJQUFJVSxJQUFJLEdBQUdBLElBQUlQLG1CQUFtQnpGLE1BQU0sRUFBRWdHLElBQUs7Z0JBQ2xELE1BQU1DLFlBQVlSLGtCQUFrQixDQUFDTyxFQUFFO2dCQUN2QyxNQUFNRSxtQkFBbUJuTCxLQUFLZ0wsR0FBRyxDQUFDL0oscUJBQXFCaUssVUFBVW5JLFdBQVcsSUFBSTlCLHFCQUFxQjtnQkFFckcsbUVBQW1FO2dCQUNuRSxJQUFJa0ssb0JBQW9CbFAsT0FBT3dFLGVBQWUsRUFBRTtvQkFFOUMsSUFBSXhFLE9BQU9vRSxXQUFXLEtBQUssY0FBYzt3QkFDdkMsaURBQWlEO3dCQUNqRCxJQUFJNkssVUFBVVgsTUFBTSxLQUFLLFFBQVE7NEJBQy9CLHFDQUFxQzs0QkFDckMsTUFBTWEsY0FBY0YsVUFBVVQsVUFBVTs0QkFFeEMsSUFBSUcseUJBQXlCUSxhQUFhO2dDQUN4QyxNQUFNQyxzQkFBc0JELGNBQWNuSztnQ0FDMUMsTUFBTXFLLGFBQTZCO29DQUNqQyxHQUFHSixTQUFTO29DQUNaWCxRQUFRO29DQUNSQyxZQUFZVSxVQUFVVixVQUFVLEdBQUc7b0NBQ25DQyxZQUFZeE8sT0FBT3FFLE9BQU8sR0FBR04sS0FBS3VMLEdBQUcsQ0FBQ3RQLE9BQU9zRSxVQUFVLEVBQUUySyxVQUFVVixVQUFVLEdBQUc7b0NBQ2hGZ0IsbUJBQW1CSDtvQ0FDbkJJLHFCQUFxQkw7b0NBQ3JCTSxZQUFZTDtvQ0FDWk0sWUFBWSxDQUFDUDtnQ0FDZjtnQ0FDQWxHLFNBQVM7b0NBQUV6QyxNQUFNO29DQUEyQkUsU0FBUzJJO2dDQUFXO2dDQUNoRXBHLFNBQVM7b0NBQUV6QyxNQUFNO29DQUFtQkUsU0FBUzt3Q0FBRXpHLFNBQVN5Tyx3QkFBd0JVO3dDQUFxQmxQLFNBQVN5Tyx3QkFBd0JRO29DQUFZO2dDQUFFO2dDQUNwSmxHLFNBQVM7b0NBQ1B6QyxNQUFNO29DQUNORSxTQUFTO3dDQUNQVyxJQUFJOUgsZ0RBQU1BO3dDQUNWcUcsV0FBV3JDLEtBQUtELEdBQUc7d0NBQ25CcU0sTUFBTSxHQUFxQjNQLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBa0IsT0FBZkQsT0FBT0UsT0FBTzt3Q0FDekNELFNBQVNELE9BQU9DLE9BQU87d0NBQ3ZCMlAsV0FBVzt3Q0FDWEMsZUFBZVQ7d0NBQ2ZVLFVBQVU5Szt3Q0FDVitLLGNBQWNaO3dDQUNkek4sUUFBUXNEO3dDQUNSZ0wsZUFBZWhRLE9BQU9DLE9BQU8sSUFBSTt3Q0FDakNnUSxlQUFlalEsT0FBT0UsT0FBTyxJQUFJO29DQUNuQztnQ0FDRjtnQ0FDQWMsUUFBUUMsR0FBRyxDQUFDLGtCQUE4Q21PLE9BQTVCSCxVQUFVL0gsT0FBTyxFQUFDLFlBQTRDbEgsT0FBbENvUCxvQkFBb0JsTCxPQUFPLENBQUMsSUFBRyxLQUF5QmMsT0FBdEJoRixPQUFPQyxPQUFPLEVBQUMsU0FBcUMsT0FBOUIrRSxtQkFBbUJkLE9BQU8sQ0FBQztnQ0FDN0lnRixNQUFNO29DQUFFZ0gsT0FBTztvQ0FBZ0JDLGFBQWEsV0FBaUNmLE9BQXRCSCxVQUFVL0gsT0FBTyxFQUFDLE1BQXNDbEgsT0FBbENvUCxvQkFBb0JsTCxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmbEUsT0FBT0MsT0FBTztvQ0FBSW1RLFVBQVU7Z0NBQUs7Z0NBQ2hKM0QsVUFBVTtnQ0FFVixxQ0FBcUM7Z0NBQ3JDakQseUJBQ0UsdUNBQ0EseUJBQWlDLE9BQWxCeUYsVUFBVS9ILE9BQU8sRUFBQyxRQUNqQyx3QkFBZ0RsSCxPQUFsQ29QLG9CQUFvQmxMLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZsRSxPQUFPQyxPQUFPLEVBQUMsUUFDL0Qsd0JBQTRDLE9BQTlCK0UsbUJBQW1CZCxPQUFPLENBQUMsSUFBRyxRQUM1Qyx1QkFBdUNsRSxPQUExQm1QLFlBQVlqTCxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmbEUsT0FBT0UsT0FBTyxFQUFDLFFBQ3JEO2dDQUdIME87Z0NBRUEsMkRBQTJEO2dDQUMzREQseUJBQXlCUTtnQ0FDekJULHlCQUF5QlU7NEJBQzNCLE9BQU87Z0NBQ0wsb0RBQW9EO2dDQUNwRHBPLFFBQVFDLEdBQUcsQ0FBQyxrQkFBNkMwTixPQUEzQjNPLE9BQU9FLE9BQU8sRUFBQyxjQUFrRGlQLE9BQXRDUixzQkFBc0J6SyxPQUFPLENBQUMsSUFBRyxPQUE0QixPQUF2QmlMLFlBQVlqTCxPQUFPLENBQUM7Z0NBQ25IbUgsMkJBQTJCckwsT0FBT0UsT0FBTyxFQUFFeU8sdUJBQXVCUSxhQUFhdkQsS0FBSyxDQUFDNUssUUFBUUUsS0FBSzs0QkFDcEc7d0JBQ0Y7d0JBRUEsZ0dBQWdHO3dCQUNoRyxNQUFNbVAsaUJBQWlCcEIsVUFBVS9ILE9BQU87d0JBQ3hDLE1BQU1vSixjQUFjN0IsbUJBQW1CTCxJQUFJO3FFQUFDcEgsQ0FBQUEsTUFBT0EsSUFBSUUsT0FBTyxLQUFLbUosaUJBQWlCOzt3QkFFcEYsSUFBSUMsZUFBZUEsWUFBWWhDLE1BQU0sS0FBSyxVQUFVZ0MsWUFBWWYsaUJBQWlCLElBQUllLFlBQVlkLG1CQUFtQixFQUFFOzRCQUNwSCxNQUFNZSxzQkFBc0JELFlBQVlmLGlCQUFpQjs0QkFDekQsTUFBTWlCLGtCQUFrQkQsc0JBQXNCdkw7NEJBQzlDLE1BQU15TCxpQkFBaUJELGtCQUFrQkYsWUFBWWQsbUJBQW1COzRCQUV4RSxpRUFBaUU7NEJBQ2pFLE1BQU1rQix3QkFBd0IxTCxxQkFBcUIsSUFBSSxpQkFBa0JoRixPQUFPeUUseUJBQXlCLEdBQUcsTUFBT08scUJBQXFCOzRCQUV4SSxNQUFNMkwscUJBQXFDO2dDQUN6QyxHQUFHTCxXQUFXO2dDQUNkaEMsUUFBUTtnQ0FDUmlCLG1CQUFtQi9IO2dDQUNuQmdJLHFCQUFxQmhJO2dDQUNyQmdILFlBQVl4TyxPQUFPcUUsT0FBTyxHQUFHTixLQUFLdUwsR0FBRyxDQUFDdFAsT0FBT3NFLFVBQVUsRUFBRWdNLFlBQVkvQixVQUFVO2dDQUMvRWtCLFlBQVksQ0FBQ2M7Z0NBQ2JiLFlBQVljOzRCQUNkOzRCQUNBdkgsU0FBUztnQ0FBRXpDLE1BQU07Z0NBQTJCRSxTQUFTaUs7NEJBQW1COzRCQUN4RTFILFNBQVM7Z0NBQUV6QyxNQUFNO2dDQUFtQkUsU0FBUztvQ0FBRXpHLFNBQVN5Tyx3QkFBd0I2QjtvQ0FBcUJyUSxTQUFTeU8sd0JBQXdCNkI7Z0NBQWdCOzRCQUFFOzRCQUN4SnZILFNBQVM7Z0NBQ1B6QyxNQUFNO2dDQUNORSxTQUFTO29DQUNQVyxJQUFJOUgsZ0RBQU1BO29DQUNWcUcsV0FBV3JDLEtBQUtELEdBQUc7b0NBQ25CcU0sTUFBTSxHQUFxQjNQLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBa0IsT0FBZkQsT0FBT0UsT0FBTztvQ0FDekNELFNBQVNELE9BQU9DLE9BQU87b0NBQ3ZCMlAsV0FBVztvQ0FDWEMsZUFBZVU7b0NBQ2ZULFVBQVU5SztvQ0FDVitLLGNBQWNTO29DQUNkOU8sUUFBUXNEO29DQUNSZ0wsZUFBZWhRLE9BQU9DLE9BQU8sSUFBSTtvQ0FDakNnUSxlQUFlalEsT0FBT0UsT0FBTyxJQUFJO29DQUNqQzBRLDJCQUEyQkg7b0NBQzNCSSwyQkFBMkJIO2dDQUM3Qjs0QkFDRjs0QkFDQTFQLFFBQVFDLEdBQUcsQ0FBQyxtQkFBOENzUCxPQUEzQkYsaUJBQWlCLEdBQUUsVUFBMENyUSxPQUFsQ3VRLG9CQUFvQnJNLE9BQU8sQ0FBQyxJQUFHLEtBQStCdU0sT0FBNUJ6USxPQUFPQyxPQUFPLEVBQUMsZUFBdUMsT0FBMUJ3USxlQUFldk0sT0FBTyxDQUFDOzRCQUMvSWdGLE1BQU07Z0NBQUVnSCxPQUFPO2dDQUFpQkMsYUFBYSxXQUEwQ00sT0FBL0JKLGlCQUFpQixHQUFFLGNBQXNDLE9BQTFCSSxlQUFldk0sT0FBTyxDQUFDO2dDQUFNa00sVUFBVTs0QkFBSzs0QkFDbkkzRCxVQUFVOzRCQUVWLHNDQUFzQzs0QkFDdEMsTUFBTXFFLGNBQWNMLGlCQUFpQixJQUFJLE9BQU9BLGlCQUFpQixJQUFJLE9BQU87NEJBQzVFakgseUJBQ0Usd0NBQ0EseUJBQWtDLE9BQW5CNkcsaUJBQWlCLEdBQUUsUUFDbEMsd0JBQWdEclEsT0FBbEN1USxvQkFBb0JyTSxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmbEUsT0FBT0MsT0FBTyxFQUFDLFFBQy9ELHdCQUE0QyxPQUE5QitFLG1CQUFtQmQsT0FBTyxDQUFDLElBQUcsUUFDNUMsMkJBQStDbEUsT0FBOUJ3USxnQkFBZ0J0TSxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmbEUsT0FBT0UsT0FBTyxFQUFDLFFBQzlELEdBQTJCdVEsT0FBeEJLLGFBQVksY0FBeUM5USxPQUE3QnlRLGVBQWV2TSxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmbEUsT0FBT0UsT0FBTyxFQUFDLFFBQ3RFOzRCQUdIME87NEJBRUEsMkRBQTJEOzRCQUMzREYseUJBQXlCNkI7NEJBQ3pCNUIseUJBQXlCNkI7d0JBQzNCO29CQUNGLE9BQU8sSUFBSXhRLE9BQU9vRSxXQUFXLEtBQUssa0JBQWtCO3dCQUNsRCx3RUFBd0U7d0JBQ3hFLElBQUk2SyxVQUFVWCxNQUFNLEtBQUssUUFBUTs0QkFDL0IsdUVBQXVFOzRCQUN2RSxNQUFNeUMscUJBQXFCOUIsVUFBVVQsVUFBVSxFQUFFLHlDQUF5Qzs0QkFFMUYsSUFBSUcseUJBQXlCb0Msb0JBQW9CO2dDQUMvQywrQ0FBK0M7Z0NBQy9DLDhEQUE4RDtnQ0FDOUQsTUFBTTVPLGtCQUFrQlksWUFBWS9DLE9BQU9FLE9BQU8sSUFBSTtnQ0FDdEQsTUFBTThDLHFCQUFxQkQsWUFBWS9DLE9BQU8yRSxtQkFBbUIsSUFBSTtnQ0FDckUsTUFBTXFNLHlCQUF5QjdPLGtCQUFrQmE7Z0NBQ2pELE1BQU1pTyxxQkFBcUJGLHFCQUFxQkM7Z0NBRWhELHNDQUFzQztnQ0FDdEMsNkVBQTZFO2dDQUM3RSxNQUFNL08sa0JBQWtCYyxZQUFZL0MsT0FBT0MsT0FBTyxJQUFJO2dDQUN0RCwwREFBMEQ7Z0NBQzFELE1BQU1pUix5QkFBeUJqUCxrQkFBbUJ5RCxDQUFBQSxNQUFNVixrQkFBa0IsR0FBSS9DLENBQUFBLGtCQUFrQkUsZUFBYyxDQUFDO2dDQUMvRyxNQUFNZ1AseUJBQXlCRCx5QkFBeUJsTztnQ0FDeEQsTUFBTW9PLGdCQUFnQkgscUJBQXFCRTtnQ0FFM0MseURBQXlEO2dDQUN6RCxNQUFNRSxXQUFXcEMsVUFBVVYsVUFBVSxHQUFHO2dDQUN4QyxNQUFNK0MsV0FBV3RSLE9BQU9xRSxPQUFPLEdBQUdOLEtBQUt1TCxHQUFHLENBQUN0UCxPQUFPc0UsVUFBVSxFQUFFK007Z0NBRTlELE1BQU1oQyxhQUE2QjtvQ0FDakMsR0FBR0osU0FBUztvQ0FDWlgsUUFBUTtvQ0FDUkMsWUFBWThDO29DQUNaN0MsWUFBWThDO29DQUNaL0IsbUJBQW1CNkI7b0NBQ25CNUIscUJBQXFCdUI7b0NBQ3JCdEIsWUFBWTJCO29DQUNaMUIsWUFBWSxDQUFDcUI7Z0NBQ2Y7Z0NBRUEvUCxRQUFRQyxHQUFHLENBQUUsa0VBQXVEO29DQUNsRWlHLFNBQVMrSCxVQUFVL0gsT0FBTztvQ0FDMUI2SixvQkFBb0JBO29DQUNwQkssZUFBZUE7b0NBQ2Y1QixxQkFBcUJ1QjtvQ0FDckJNLFVBQVVBO29DQUNWRix3QkFBd0JBO29DQUN4QkQsd0JBQXdCQTtvQ0FDeEJsTSxvQkFBb0JVLE1BQU1WLGtCQUFrQjtnQ0FDOUM7Z0NBRUFpRSxTQUFTO29DQUFFekMsTUFBTTtvQ0FBMkJFLFNBQVMySTtnQ0FBVztnQ0FDaEVwRyxTQUFTO29DQUFFekMsTUFBTTtvQ0FBbUJFLFNBQVM7d0NBQUV6RyxTQUFTeU8sd0JBQXdCMEM7d0NBQWVsUixTQUFTeU8sd0JBQXdCb0M7b0NBQW1CO2dDQUFFO2dDQUVySiw0REFBNEQ7Z0NBQzVELGdFQUFnRTtnQ0FDaEUsTUFBTVEsbUJBQW1CeE8sWUFBWS9DLE9BQU9FLE9BQU8sSUFBSTZDLFlBQVkvQyxPQUFPMkUsbUJBQW1CO2dDQUM3RixNQUFNNk0seUJBQXlCVCxxQkFBc0JDLENBQUFBLHlCQUF5Qk8sZ0JBQWU7Z0NBQzdGLE1BQU1FLHlCQUF5QkQseUJBQXlCTDtnQ0FFeERuUSxRQUFRQyxHQUFHLENBQUUsZ0RBQXFDO29DQUNoRGYsU0FBU0YsT0FBT0UsT0FBTztvQ0FDdkJ3UixZQUFZWDtvQ0FDWlksY0FBY1g7b0NBQ2RuTixXQUFXME47b0NBQ1hDLHdCQUF3QkE7b0NBQ3hCQyx3QkFBd0JBO2dDQUMxQjtnQ0FFQXhJLFNBQVM7b0NBQ1B6QyxNQUFNO29DQUNORSxTQUFTO3dDQUNQVyxJQUFJOUgsZ0RBQU1BO3dDQUNWcUcsV0FBV3JDLEtBQUtELEdBQUc7d0NBQ25CcU0sTUFBTSxHQUFxQjNQLE9BQWxCQSxPQUFPRSxPQUFPLEVBQUMsS0FBOEIsT0FBM0JGLE9BQU8yRSxtQkFBbUI7d0NBQ3JEMUUsU0FBU0QsT0FBT0UsT0FBTzt3Q0FDdkIwUCxXQUFXO3dDQUNYQyxlQUFla0I7d0NBQ2ZqQixVQUFVa0I7d0NBQ1ZqQixjQUFja0I7d0NBQ2R2UCxRQUFRc1A7d0NBQ1JoQixlQUFlaFEsT0FBT0UsT0FBTyxJQUFJO3dDQUNqQytQLGVBQWVqUSxPQUFPMkUsbUJBQW1CLElBQUk7d0NBQzdDaU0sMkJBQTJCWTt3Q0FDM0JYLDJCQUEyQlk7b0NBQzdCO2dDQUNGO2dDQUVBeEksU0FBUztvQ0FDUHpDLE1BQU07b0NBQ05FLFNBQVM7d0NBQ1BXLElBQUk5SCxnREFBTUE7d0NBQ1ZxRyxXQUFXckMsS0FBS0QsR0FBRzt3Q0FDbkJxTSxNQUFNLEdBQXFCM1AsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUE4QixPQUEzQkQsT0FBTzJFLG1CQUFtQjt3Q0FDckQxRSxTQUFTRCxPQUFPQyxPQUFPO3dDQUN2QjJQLFdBQVc7d0NBQ1hDLGVBQWV1Qjt3Q0FDZnRCLFVBQVVxQjt3Q0FDVnBCLGNBQWNrQjt3Q0FDZHZQLFFBQVF5UDt3Q0FDUm5CLGVBQWVoUSxPQUFPQyxPQUFPLElBQUk7d0NBQ2pDZ1EsZUFBZWpRLE9BQU8yRSxtQkFBbUIsSUFBSTtvQ0FDL0M7Z0NBQ0Y7Z0NBRUEzRCxRQUFRQyxHQUFHLENBQUMsNkJBQWlFOFAsT0FBcEM5QixVQUFVL0gsT0FBTyxFQUFDLG9CQUF3Q2xILE9BQXRCK1Esb0JBQW1CLEtBQXVCRSxPQUFwQmpSLE9BQU9FLE9BQU8sRUFBQyxPQUFzQ0YsT0FBakNpUixtQkFBbUIvTSxPQUFPLENBQUMsSUFBRyxLQUFrRGtOLE9BQS9DcFIsT0FBTzJFLG1CQUFtQixFQUFDLHNCQUFnRDNFLE9BQTVCb1IsY0FBY2xOLE9BQU8sQ0FBQyxJQUFHLEtBQThCK0ssT0FBM0JqUCxPQUFPQyxPQUFPLEVBQUMsY0FBc0NvUixPQUExQnBDLFVBQVVWLFVBQVUsRUFBQyxPQUFjLE9BQVQ4QztnQ0FDeFJuSSxNQUFNO29DQUFFZ0gsT0FBTztvQ0FBNkJDLGFBQWEsV0FBaUNpQixPQUF0Qm5DLFVBQVUvSCxPQUFPLEVBQUMsTUFBZ0NsSCxPQUE1Qm9SLGNBQWNsTixPQUFPLENBQUMsSUFBRyxLQUF5QmxFLE9BQXRCQSxPQUFPQyxPQUFPLEVBQUMsU0FBa0MsT0FBM0JELE9BQU8yRSxtQkFBbUI7b0NBQUl5TCxVQUFVO2dDQUFLO2dDQUN6TDNELFVBQVU7Z0NBRVYsZ0RBQWdEO2dDQUNoRGpELHlCQUNFLHlEQUNBLHlCQUFpQyxPQUFsQnlGLFVBQVUvSCxPQUFPLEVBQUMsUUFDakMsNkJBQW9EbEgsT0FBakMrUSxtQkFBbUI3TSxPQUFPLENBQUMsSUFBRyxLQUF1QitNLE9BQXBCalIsT0FBT0UsT0FBTyxFQUFDLE9BQXNDRixPQUFqQ2lSLG1CQUFtQi9NLE9BQU8sQ0FBQyxJQUFHLEtBQThCLE9BQTNCbEUsT0FBTzJFLG1CQUFtQixFQUFDLFFBQ3BJLCtCQUFpRDNFLE9BQTVCb1IsY0FBY2xOLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZsRSxPQUFPQyxPQUFPLEVBQUMsUUFDaEUsdUJBQXVDb1IsT0FBMUJwQyxVQUFVVixVQUFVLEVBQUMsT0FBYyxPQUFUOEMsVUFBUyxRQUMvQztnQ0FHSHpDO2dDQUVBLDJEQUEyRDtnQ0FDM0RELHlCQUF5Qm9DO2dDQUN6QnJDLHlCQUF5QjBDOzRCQUMzQixPQUFPO2dDQUNMLG9EQUFvRDtnQ0FDcERwUSxRQUFRQyxHQUFHLENBQUMsa0JBQWlFME4sT0FBL0MzTyxPQUFPRSxPQUFPLEVBQUMsa0NBQXNFNlEsT0FBdENwQyxzQkFBc0J6SyxPQUFPLENBQUMsSUFBRyxPQUFtQyxPQUE5QjZNLG1CQUFtQjdNLE9BQU8sQ0FBQztnQ0FDOUltSCwyQkFBMkJyTCxPQUFPRSxPQUFPLEVBQUV5Tyx1QkFBdUJvQyxvQkFBb0JuRixLQUFLLENBQUM1SyxRQUFRRSxLQUFLOzRCQUMzRzt3QkFDRjt3QkFFQSxnR0FBZ0c7d0JBQ2hHLE1BQU1tUCxpQkFBaUJwQixVQUFVL0gsT0FBTzt3QkFDeEMsTUFBTW9KLGNBQWM3QixtQkFBbUJMLElBQUk7cUVBQUNwSCxDQUFBQSxNQUFPQSxJQUFJRSxPQUFPLEtBQUttSixpQkFBaUI7O3dCQUVwRnJQLFFBQVFDLEdBQUcsQ0FBRSwyQ0FBZ0M7NEJBQzNDb1AsZ0JBQWdCQTs0QkFDaEJ1QixrQkFBa0IsQ0FBQyxDQUFDdEI7NEJBQ3BCdUIsaUJBQWlCLEVBQUV2Qix3QkFBQUEsa0NBQUFBLFlBQWFoQyxNQUFNOzRCQUN0Q3dELHNCQUFzQixFQUFFeEIsd0JBQUFBLGtDQUFBQSxZQUFhZixpQkFBaUI7NEJBQ3REd0MsdUJBQXVCLEVBQUV6Qix3QkFBQUEsa0NBQUFBLFlBQWFkLG1CQUFtQjs0QkFDekR3QyxnQkFBZ0IsQ0FBQyxDQUFFMUIsQ0FBQUEsZUFBZUEsWUFBWWhDLE1BQU0sS0FBSyxVQUFVZ0MsWUFBWWYsaUJBQWlCLElBQUllLFlBQVlkLG1CQUFtQjt3QkFDckk7d0JBRUEsSUFBSWMsZUFBZUEsWUFBWWhDLE1BQU0sS0FBSyxVQUFVZ0MsWUFBWWYsaUJBQWlCLElBQUllLFlBQVlkLG1CQUFtQixFQUFFOzRCQUNwSCw0RkFBNEY7NEJBQzVGLE1BQU1lLHNCQUFzQkQsWUFBWWYsaUJBQWlCOzRCQUV6RCwrQ0FBK0M7NEJBQy9DLHlEQUF5RDs0QkFDekQsTUFBTXROLGtCQUFrQmMsWUFBWS9DLE9BQU9DLE9BQU8sSUFBSTs0QkFDdEQsTUFBTWtDLGtCQUFrQlksWUFBWS9DLE9BQU9FLE9BQU8sSUFBSTs0QkFDdEQsTUFBTThDLHFCQUFxQkQsWUFBWS9DLE9BQU8yRSxtQkFBbUIsSUFBSTs0QkFFckUsMERBQTBEOzRCQUMxRCxNQUFNdU0seUJBQXlCalAsa0JBQW1CeUQsQ0FBQUEsTUFBTVYsa0JBQWtCLEdBQUkvQyxDQUFBQSxrQkFBa0JFLGVBQWMsQ0FBQzs0QkFDL0csTUFBTWdQLHlCQUF5QkQseUJBQXlCbE87NEJBQ3hELE1BQU1pUCx1QkFBdUIxQixzQkFBc0JZOzRCQUVuRCxzQ0FBc0M7NEJBQ3RDLE1BQU1ILHlCQUF5QjdPLGtCQUFrQmE7NEJBQ2pELE1BQU1rUCxvQkFBb0JELHVCQUF1QmpCOzRCQUVqRCx1Q0FBdUM7NEJBQ3ZDLDBFQUEwRTs0QkFDMUUsTUFBTW1CLGVBQWU3QixZQUFZZCxtQkFBbUIsSUFBSTs0QkFDeEQsTUFBTTRDLDBCQUEwQkYsb0JBQW9CQzs0QkFFcEQsZ0ZBQWdGOzRCQUNoRixtRkFBbUY7NEJBQ25GLE1BQU16Qix3QkFBd0JTLHlCQUF5QixJQUFJaUIsMEJBQTBCakIseUJBQXlCOzRCQUU5Ryw4RUFBOEU7NEJBQzlFLElBQUlpQiw0QkFBNEIsR0FBRztnQ0FDakNwUixRQUFRRyxJQUFJLENBQUMseU5BR3dDLE9BQXpCdUUsTUFBTVYsa0JBQWtCOzRCQUN0RDs0QkFFQWhFLFFBQVFDLEdBQUcsQ0FBQyxnR0FFbUJrUixPQURIN0IsWUFBWXBKLE9BQU8sRUFBQywrQ0FFdEJnTCxPQURLQyxjQUFhLDBDQUVkQyxPQURKRixtQkFBa0IsOENBRVpmLE9BREZpQix5QkFBd0IsZ0RBRXhCMUIsT0FERVMsd0JBQXVCLDhDQUVsQ2lCLE9BRFMxQix1QkFBc0IscUNBRWpDd0IsT0FERUUsMEJBQTBCLElBQUksUUFBUSxNQUFLLG1DQUN0QkQsT0FBdkJELG1CQUFrQixPQUF1QkUsT0FBbEJELGNBQWEsT0FBNkIsT0FBeEJDOzRCQUU1RCw2REFBNkQ7NEJBQzdELE1BQU16QixxQkFBcUM7Z0NBQ3pDLEdBQUdMLFdBQVc7Z0NBQ2RoQyxRQUFRO2dDQUNSLGtEQUFrRDtnQ0FDbERpQixtQkFBbUIvSDtnQ0FDbkJnSSxxQkFBcUJoSTtnQ0FDckJnSCxZQUFZeE8sT0FBT3FFLE9BQU8sR0FBR04sS0FBS3VMLEdBQUcsQ0FBQ3RQLE9BQU9zRSxVQUFVLEVBQUVnTSxZQUFZL0IsVUFBVTtnQ0FDL0VrQixZQUFZO2dDQUNaQyxZQUFZOzRCQUNkOzRCQUVBekcsU0FBUztnQ0FBRXpDLE1BQU07Z0NBQTJCRSxTQUFTaUs7NEJBQW1COzRCQUN4RTFILFNBQVM7Z0NBQUV6QyxNQUFNO2dDQUFtQkUsU0FBUztvQ0FBRXpHLFNBQVN5Tyx3QkFBd0I2QjtvQ0FBcUJyUSxTQUFTeU8sd0JBQXdCdUQ7Z0NBQWtCOzRCQUFFOzRCQUUxSixnRUFBZ0U7NEJBQ2hFLDRFQUE0RTs0QkFDNUVsUixRQUFRQyxHQUFHLENBQUUsc0VBQTJEO2dDQUN0RTJQLDJCQUEyQndCO2dDQUMzQnZCLDJCQUEyQkg7Z0NBQzNCZixNQUFNLEdBQXFCM1AsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUE4QixPQUEzQkQsT0FBTzJFLG1CQUFtQjtnQ0FDckRpTCxXQUFXO2dDQUNYQyxlQUFlVTtnQ0FDZlQsVUFBVXFCO2dDQUNWcEIsY0FBY2tDO2dDQUNkdlEsUUFBUXlQO2dDQUNSbkIsZUFBZWhRLE9BQU9DLE9BQU87Z0NBQzdCZ1EsZUFBZWpRLE9BQU8yRSxtQkFBbUI7NEJBQzNDOzRCQUVBc0UsU0FBUztnQ0FDUHpDLE1BQU07Z0NBQ05FLFNBQVM7b0NBQ1BXLElBQUk5SCxnREFBTUE7b0NBQ1ZxRyxXQUFXckMsS0FBS0QsR0FBRztvQ0FDbkJxTSxNQUFNLEdBQXFCM1AsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUE4QixPQUEzQkQsT0FBTzJFLG1CQUFtQjtvQ0FDckQxRSxTQUFTRCxPQUFPQyxPQUFPO29DQUN2QjJQLFdBQVc7b0NBQ1hDLGVBQWVVO29DQUNmVCxVQUFVcUI7b0NBQ1ZwQixjQUFja0M7b0NBQ2R2USxRQUFReVA7b0NBQ1JuQixlQUFlaFEsT0FBT0MsT0FBTyxJQUFJO29DQUNqQ2dRLGVBQWVqUSxPQUFPMkUsbUJBQW1CLElBQUk7b0NBQzdDaU0sMkJBQTJCd0I7b0NBQzNCdkIsMkJBQTJCSDtnQ0FDN0I7NEJBQ0Y7NEJBRUEsK0VBQStFOzRCQUMvRXpILFNBQVM7Z0NBQ1B6QyxNQUFNO2dDQUNORSxTQUFTO29DQUNQVyxJQUFJOUgsZ0RBQU1BO29DQUNWcUcsV0FBV3JDLEtBQUtELEdBQUc7b0NBQ25CcU0sTUFBTSxHQUFxQjNQLE9BQWxCQSxPQUFPRSxPQUFPLEVBQUMsS0FBOEIsT0FBM0JGLE9BQU8yRSxtQkFBbUI7b0NBQ3JEMUUsU0FBU0QsT0FBT0UsT0FBTztvQ0FDdkIwUCxXQUFXO29DQUNYQyxlQUFlcUM7b0NBQ2ZwQyxVQUFVa0I7b0NBQ1ZqQixjQUFja0M7b0NBQ2R2USxRQUFRc1A7b0NBQ1JoQixlQUFlaFEsT0FBT0UsT0FBTyxJQUFJO29DQUNqQytQLGVBQWVqUSxPQUFPMkUsbUJBQW1CLElBQUk7Z0NBQy9DOzRCQUNGOzRCQUVBM0QsUUFBUUMsR0FBRyxDQUFDLDhCQUFtRXNQLE9BQXJDRixpQkFBaUIsR0FBRSxvQkFBb0RyUSxPQUFsQ3VRLG9CQUFvQnJNLE9BQU8sQ0FBQyxJQUFHLEtBQXVCK04sT0FBcEJqUyxPQUFPQyxPQUFPLEVBQUMsT0FBd0NELE9BQW5DaVMscUJBQXFCL04sT0FBTyxDQUFDLElBQUcsS0FBa0RnTyxPQUEvQ2xTLE9BQU8yRSxtQkFBbUIsRUFBQyxzQkFBb0QzRSxPQUFoQ2tTLGtCQUFrQmhPLE9BQU8sQ0FBQyxJQUFHLEtBQStCa08sT0FBNUJwUyxPQUFPRSxPQUFPLEVBQUMsZUFBbURGLE9BQXRDb1Msd0JBQXdCbE8sT0FBTyxDQUFDLElBQUcsS0FBOEJvTSxPQUEzQnRRLE9BQU9FLE9BQU8sRUFBQyxjQUFtQyxPQUF2Qm9RLFlBQVkvQixVQUFVLEVBQUM7NEJBQzNXckYsTUFBTTtnQ0FBRWdILE9BQU87Z0NBQThCQyxhQUFhLFdBQXlDaUMsT0FBOUIvQixpQkFBaUIsR0FBRSxhQUFpRHJRLE9BQXRDb1Msd0JBQXdCbE8sT0FBTyxDQUFDLElBQUcsS0FBeUJsRSxPQUF0QkEsT0FBT0UsT0FBTyxFQUFDLFNBQWtDLE9BQTNCRixPQUFPMkUsbUJBQW1CO2dDQUFJeUwsVUFBVTs0QkFBSzs0QkFDNU0zRCxVQUFVOzRCQUVWLGlEQUFpRDs0QkFDakQsTUFBTXFFLGNBQWNzQiwwQkFBMEIsSUFBSSxPQUFPQSwwQkFBMEIsSUFBSSxPQUFPOzRCQUM5RjVJLHlCQUNFLDBEQUNBLHlCQUFrQyxPQUFuQjZHLGlCQUFpQixHQUFFLFFBQ2xDLDZCQUFxRHJRLE9BQWxDdVEsb0JBQW9Cck0sT0FBTyxDQUFDLElBQUcsS0FBdUIrTixPQUFwQmpTLE9BQU9DLE9BQU8sRUFBQyxPQUF3Q0QsT0FBbkNpUyxxQkFBcUIvTixPQUFPLENBQUMsSUFBRyxLQUE4QixPQUEzQmxFLE9BQU8yRSxtQkFBbUIsRUFBQyxRQUN2SSwrQkFBcUQzRSxPQUFoQ2tTLGtCQUFrQmhPLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZsRSxPQUFPRSxPQUFPLEVBQUMsUUFDcEUsR0FBMEJrUyxPQUF2QnRCLGFBQVksYUFBaUQ5USxPQUF0Q29TLHdCQUF3QmxPLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWZsRSxPQUFPRSxPQUFPLEVBQUMsUUFDL0UsdUJBQW9DLE9BQXZCb1EsWUFBWS9CLFVBQVUsRUFBQyxvQkFDbkM7NEJBR0hLOzRCQUVBLDJEQUEyRDs0QkFDM0RGLHlCQUF5QjZCOzRCQUN6QjVCLHlCQUF5QnVEO3dCQUMzQjtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsSUFBSXRELGVBQWUsR0FBRztnQkFDcEI1TixRQUFRQyxHQUFHLENBQUMsZ0NBQThEK0QsT0FBeEM0SixjQUFhLDZCQUF5RCxPQUE5QjVKLG1CQUFtQmQsT0FBTyxDQUFDO1lBQ3ZHO1FBRUY7b0NBQUc7UUFBQ3dCLE1BQU1ULGVBQWU7UUFBRVMsTUFBTVYsa0JBQWtCO1FBQUVVLE1BQU1iLGVBQWU7UUFBRWEsTUFBTTFGLE1BQU07UUFBRTBGLE1BQU1SLGNBQWM7UUFBRVEsTUFBTVAsY0FBYztRQUFFTyxNQUFNTixpQkFBaUI7UUFBRTZEO1FBQVVDO1FBQU91RDtRQUFXakQ7S0FBeUI7SUFHcE4sTUFBTTZJLG1CQUFtQm5ULGtEQUFXQTt5REFBQztZQUNuQyxJQUFJLENBQUN3RyxNQUFNYixlQUFlLElBQUksQ0FBQ2tKLE1BQU1DLE9BQU8sQ0FBQ3RJLE1BQU1iLGVBQWUsR0FBRztnQkFDbkUsT0FBTyxFQUFFO1lBQ1g7WUFFQSxPQUFPYSxNQUFNYixlQUFlLENBQUNrQyxHQUFHO2lFQUFDLENBQUNDO29CQUNoQyxNQUFNMkssZUFBZWpNLE1BQU1WLGtCQUFrQixJQUFJO29CQUNqRCxNQUFNOEIsY0FBY0UsSUFBSUYsV0FBVyxJQUFJO29CQUN2QyxNQUFNd0wseUJBQXlCWCxnQkFBZ0I3SyxjQUMzQyxDQUFDLGVBQWdCQSxjQUFlLEtBQUssTUFDckM7b0JBRUosSUFBSXlMO29CQUNKLElBQUlDO29CQUVKLElBQUl4TCxJQUFJc0gsTUFBTSxLQUFLLFVBQVV0SCxJQUFJdUksaUJBQWlCLElBQUl2SSxJQUFJd0ksbUJBQW1CLEVBQUU7d0JBQzdFLE1BQU1pRCxpQ0FBaUMsZUFBZ0J6TCxJQUFJdUksaUJBQWlCLEdBQUl2SSxJQUFJd0ksbUJBQW1CO3dCQUN2R2dELGdCQUFnQixpQ0FBa0M5TSxNQUFNMUYsTUFBTSxDQUFDMEUseUJBQXlCLEdBQUk7d0JBQzVGLElBQUlpTixlQUFlLEdBQUc7NEJBQ3BCWSxnQkFBZ0IsaUNBQWtDN00sTUFBTTFGLE1BQU0sQ0FBQ3lFLHlCQUF5QixHQUFHLE1BQU9rTjt3QkFDcEc7b0JBQ0Y7b0JBRUEsT0FBTzt3QkFDTCxHQUFHM0ssR0FBRzt3QkFDTjJLO3dCQUNBZSxpQkFBaUI1TCxjQUFjNks7d0JBQy9CZ0Isd0JBQXdCaEIsZUFBZSxJQUFJLENBQUU3SyxjQUFjNkssWUFBVyxJQUFLQSxlQUFnQixNQUFNO3dCQUNqR2lCLHdCQUF3QixNQUFPNVMsTUFBTSxDQUFDeUUseUJBQXlCLEdBQUcsTUFBT3VDLElBQUl3SCxVQUFVLEdBQUkxSCxDQUFBQSxlQUFlO3dCQUMxRytMLHdCQUF3QixNQUFPN1MsTUFBTSxDQUFDMEUseUJBQXlCLEdBQUcsTUFBT3NDLElBQUl3SCxVQUFVO3dCQUN2RjhEO3dCQUNBQzt3QkFDQUM7b0JBQ0Y7Z0JBQ0Y7Z0VBQUc3TCxJQUFJO2lFQUFDLENBQUNDLEdBQW9CQyxJQUF1QkEsRUFBRUMsV0FBVyxHQUFHRixFQUFFRSxXQUFXOztRQUNuRjt3REFBRztRQUFDcEIsTUFBTWIsZUFBZTtRQUFFYSxNQUFNVixrQkFBa0I7UUFBRVUsTUFBTTFGLE1BQU0sQ0FBQ3lFLHlCQUF5QjtRQUFFaUIsTUFBTTFGLE1BQU0sQ0FBQzBFLHlCQUF5QjtRQUFFZ0IsTUFBTTFGLE1BQU0sQ0FBQ3FFLE9BQU87UUFBRXFCLE1BQU0xRixNQUFNLENBQUNzRSxVQUFVO0tBQUM7SUFHbkwsZ0NBQWdDO0lBQ2hDLE1BQU13TyxzQkFBc0I1VCxrREFBV0E7NERBQUMsT0FBT2M7WUFDN0MsSUFBSTtvQkFrQktTO2dCQWpCUCxNQUFNc1MsYUFBYTtvQkFDakJqSyxNQUFNLEdBQXFCOUksT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUFxQkQsT0FBbEJBLE9BQU9FLE9BQU8sRUFBQyxLQUFzQixPQUFuQkYsT0FBT29FLFdBQVc7b0JBQy9EQSxhQUFhcEUsT0FBT29FLFdBQVc7b0JBQy9CbkUsU0FBU0QsT0FBT0MsT0FBTztvQkFDdkJDLFNBQVNGLE9BQU9FLE9BQU87b0JBQ3ZCbUUsU0FBU3JFLE9BQU9xRSxPQUFPO29CQUN2QkMsWUFBWXRFLE9BQU9zRSxVQUFVO29CQUM3QkMsV0FBV3ZFLE9BQU91RSxTQUFTO29CQUMzQkMsaUJBQWlCeEUsT0FBT3dFLGVBQWU7b0JBQ3ZDQywyQkFBMkJ6RSxPQUFPeUUseUJBQXlCO29CQUMzREMsMkJBQTJCMUUsT0FBTzBFLHlCQUF5QjtvQkFDM0RDLHFCQUFxQjNFLE9BQU8yRSxtQkFBbUI7b0JBQy9DcU8sY0FBY3ROLE1BQU1iLGVBQWUsQ0FBQ2tDLEdBQUc7NEVBQUNDLENBQUFBLE1BQU9BLElBQUlGLFdBQVc7O2dCQUNoRTtnQkFFQSxNQUFNckcsV0FBVyxNQUFNZixnREFBVUEsQ0FBQ3VULFVBQVUsQ0FBQ0Y7Z0JBQzdDL1IsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QlI7Z0JBQzFDLE9BQU9BLEVBQUFBLG1CQUFBQSxTQUFTVCxNQUFNLGNBQWZTLHVDQUFBQSxpQkFBaUI0RyxFQUFFLEtBQUk7WUFDaEMsRUFBRSxPQUFPbkcsT0FBTztnQkFDZEYsUUFBUUUsS0FBSyxDQUFDLHVDQUF1Q0E7Z0JBQ3JEZ0ksTUFBTTtvQkFDSmdILE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2IrQyxTQUFTO29CQUNUOUMsVUFBVTtnQkFDWjtnQkFDQSxPQUFPO1lBQ1Q7UUFDRjsyREFBRztRQUFDMUssTUFBTWIsZUFBZTtRQUFFcUU7S0FBTTtJQUVqQyxNQUFNaUssa0JBQWtCalUsa0RBQVdBO3dEQUFDLE9BQU9rVTtZQUN6QyxJQUFJO2dCQUNGLE1BQU0zUyxXQUFXLE1BQU1mLGdEQUFVQSxDQUFDMlQsUUFBUSxDQUFDRDtnQkFDM0NwUyxRQUFRQyxHQUFHLENBQUMsNkJBQTZCUjtnQkFDekN5SSxNQUFNO29CQUNKZ0gsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsVUFBVTtnQkFDWjtnQkFDQSxPQUFPO1lBQ1QsRUFBRSxPQUFPbFAsT0FBTztnQkFDZEYsUUFBUUUsS0FBSyxDQUFDLHFDQUFxQ0E7Z0JBQ25EZ0ksTUFBTTtvQkFDSmdILE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2IrQyxTQUFTO29CQUNUOUMsVUFBVTtnQkFDWjtnQkFDQSxPQUFPO1lBQ1Q7UUFDRjt1REFBRztRQUFDbEg7S0FBTTtJQUVWLE1BQU1vSyxpQkFBaUJwVSxrREFBV0E7dURBQUMsT0FBT2tVO1lBQ3hDLElBQUk7Z0JBQ0YsTUFBTTNTLFdBQVcsTUFBTWYsZ0RBQVVBLENBQUM2VCxPQUFPLENBQUNIO2dCQUMxQ3BTLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJSO2dCQUN6Q3lJLE1BQU07b0JBQ0pnSCxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxVQUFVO2dCQUNaO2dCQUNBLE9BQU87WUFDVCxFQUFFLE9BQU9sUCxPQUFPO2dCQUNkRixRQUFRRSxLQUFLLENBQUMsb0NBQW9DQTtnQkFDbERnSSxNQUFNO29CQUNKZ0gsT0FBTztvQkFDUEMsYUFBYTtvQkFDYitDLFNBQVM7b0JBQ1Q5QyxVQUFVO2dCQUNaO2dCQUNBLE9BQU87WUFDVDtRQUNGO3NEQUFHO1FBQUNsSDtLQUFNO0lBRVYsTUFBTXNLLHFCQUFxQnRVLGtEQUFXQTsyREFBQztZQUNyQyxNQUFNdVUsU0FBU0MsdUJBQStCO1lBQzlDLElBQUksQ0FBQ0QsUUFBUTtnQkFDWHpTLFFBQVFFLEtBQUssQ0FBQztnQkFDZCtILFNBQVM7b0JBQUV6QyxNQUFNO29CQUFzQkUsU0FBUztnQkFBVTtnQkFDMUQ7WUFDRjtZQUNBLElBQUk7Z0JBQ0YsTUFBTW1OLGlCQUFpQixNQUFNblQsTUFBTSxHQUFVLE9BQVArUyxRQUFPO2dCQUM3QyxJQUFJLENBQUNJLGVBQWVsVCxFQUFFLEVBQUU7b0JBQ3RCLDJEQUEyRDtvQkFDM0RLLFFBQVFFLEtBQUssQ0FBQyw0Q0FBcUUyUyxPQUF6QkEsZUFBZXZGLE1BQU0sRUFBQyxLQUE2QixPQUExQnVGLGVBQWU3SSxVQUFVO29CQUM1RyxNQUFNOEksZUFBZSxNQUFNRCxlQUFlakosSUFBSSxHQUFHZ0IsS0FBSzsyRUFBQyxJQUFNOztvQkFDN0Q1SyxRQUFRRSxLQUFLLENBQUMsdUNBQXVDNFM7Z0JBQ3ZEO2dCQUNBN0ssU0FBUztvQkFBRXpDLE1BQU07b0JBQXNCRSxTQUFTbU4sZUFBZWxULEVBQUUsR0FBRyxXQUFXO2dCQUFVO1lBQzNGLEVBQUUsT0FBT08sT0FBWTtnQkFDbkIrSCxTQUFTO29CQUFFekMsTUFBTTtvQkFBc0JFLFNBQVM7Z0JBQVU7Z0JBQzFEMUYsUUFBUUUsS0FBSyxDQUFDLHFEQUFxREE7Z0JBQ25FLElBQUlBLE1BQU02UyxLQUFLLEVBQUU7b0JBQ2YvUyxRQUFRRSxLQUFLLENBQUMsc0JBQXNCQSxNQUFNNlMsS0FBSztnQkFDakQ7Z0JBQ0EsbUVBQW1FO2dCQUNuRS9TLFFBQVFFLEtBQUssQ0FBQywrQkFBK0IsR0FBVSxPQUFQdVMsUUFBTztZQUN6RDtRQUNGOzBEQUFHO1FBQUN4SztLQUFTO0lBRWIsa0RBQWtEO0lBQ2xEaEssZ0RBQVNBO3FDQUFDO1lBQ1Isd0NBQXdDO1lBQ3hDdVU7UUFDRjtvQ0FBRztRQUFDQTtLQUFtQjtJQUV2QixpREFBaUQ7SUFDakR2VSxnREFBU0E7cUNBQUM7WUFDUndHLHdCQUF3QkM7UUFDMUI7b0NBQUc7UUFBQ0E7S0FBTTtJQUdWLDREQUE0RDtJQUM1RHpHLGdEQUFTQTtxQ0FBQztZQUNSLElBQUl5RyxNQUFNVCxlQUFlLEtBQUssYUFBYTtnQkFDekNqRSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osb0RBQW9EO2dCQUNwRGdJLFNBQVM7b0JBQUV6QyxNQUFNO2dCQUF5QjtnQkFDMUN4RixRQUFRQyxHQUFHLENBQUM7WUFDZDtRQUNGO29DQUFHO1FBQUN5RSxNQUFNVCxlQUFlO1FBQUVnRTtLQUFTO0lBRXBDLDRDQUE0QztJQUM1Q2hLLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU0wSSxpQkFBaUJoSSxnRUFBY0EsQ0FBQ2lJLFdBQVc7WUFDakQsTUFBTWEsbUJBQW1CZCxlQUFlZSxtQkFBbUI7WUFFM0QsSUFBSUQsa0JBQWtCO2dCQUNwQixJQUFJL0MsTUFBTVQsZUFBZSxLQUFLLFdBQVc7b0JBQ3ZDLDhDQUE4QztvQkFDOUMwQyxlQUFlcU0sbUJBQW1CLENBQUN2TDtvQkFDbkN6SCxRQUFRQyxHQUFHLENBQUMsMkNBQTJDd0g7Z0JBQ3pELE9BQU8sSUFBSS9DLE1BQU1ULGVBQWUsS0FBSyxXQUFXO29CQUM5QyxxQ0FBcUM7b0JBQ3JDMEMsZUFBZXNNLGtCQUFrQixDQUFDeEw7b0JBQ2xDekgsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q3dIO2dCQUMxRDtZQUNGO1FBQ0Y7b0NBQUc7UUFBQy9DLE1BQU1ULGVBQWU7S0FBQztJQUUxQiwyRUFBMkU7SUFDM0VoRyxnREFBU0E7cUNBQUM7WUFDUixNQUFNMEksaUJBQWlCaEksZ0VBQWNBLENBQUNpSSxXQUFXO1lBRWpELHdFQUF3RTtZQUN4RSxJQUFJbEMsTUFBTVQsZUFBZSxLQUFLLGFBQWEsQ0FBQzBDLGVBQWVlLG1CQUFtQixJQUFJO2dCQUNoRiwwRUFBMEU7Z0JBQzFFLGdEQUFnRDtnQkFDaEQsSUFBSWhELE1BQU0xRixNQUFNLENBQUNDLE9BQU8sSUFBSXlGLE1BQU0xRixNQUFNLENBQUNFLE9BQU8sSUFBSXdGLE1BQU1iLGVBQWUsQ0FBQ21FLE1BQU0sR0FBRyxHQUFHO29CQUNwRnJCLGVBQWV1TSw0QkFBNEIsQ0FBQ3hPLE1BQU0xRixNQUFNLEVBQ3JEcU0sSUFBSTtxREFBQyxDQUFDeEQ7NEJBQ0xsQixlQUFld00saUJBQWlCLENBQUN0TDs0QkFDakM3SCxRQUFRQyxHQUFHLENBQUMsNENBQTRDNEg7d0JBQzFEO29EQUNDK0MsS0FBSztxREFBQyxDQUFDMUs7NEJBQ05GLFFBQVFFLEtBQUssQ0FBQyxvQ0FBb0NBO3dCQUNwRDs7Z0JBQ0o7WUFDRjtZQUVBLDhDQUE4QztZQUM5QyxNQUFNdUgsbUJBQW1CZCxlQUFlZSxtQkFBbUI7WUFDM0QsSUFBSUQsa0JBQWtCO2dCQUNwQixJQUFJL0MsTUFBTVQsZUFBZSxLQUFLLFdBQVc7b0JBQ3ZDLGlEQUFpRDtvQkFDakQwQyxlQUFlcU0sbUJBQW1CLENBQUN2TDtvQkFDbkN6SCxRQUFRQyxHQUFHLENBQUMsNENBQTRDd0g7Z0JBQzFELE9BQU8sSUFBSS9DLE1BQU1ULGVBQWUsS0FBSyxXQUFXO29CQUM5Qyx1Q0FBdUM7b0JBQ3ZDMEMsZUFBZXNNLGtCQUFrQixDQUFDeEw7b0JBQ2xDekgsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q3dIO2dCQUMxRDtZQUNGO1FBQ0Y7b0NBQUc7UUFBQy9DLE1BQU1ULGVBQWU7UUFBRVMsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTztRQUFFeUYsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTztLQUFDO0lBRXRFLG1EQUFtRDtJQUNuRGpCLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU0wSSxpQkFBaUJoSSxnRUFBY0EsQ0FBQ2lJLFdBQVc7WUFDakQsTUFBTWEsbUJBQW1CZCxlQUFlZSxtQkFBbUI7WUFFM0QsSUFBSUQsa0JBQWtCO2dCQUNwQixNQUFNRSxpQkFBaUJoQixlQUFlaUIsV0FBVyxDQUFDSDtnQkFFbEQsNERBQTREO2dCQUM1RCxJQUFJRSxrQkFDQ0EsQ0FBQUEsZUFBZTNJLE1BQU0sQ0FBQ0MsT0FBTyxLQUFLeUYsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTyxJQUN0RDBJLGVBQWUzSSxNQUFNLENBQUNFLE9BQU8sS0FBS3dGLE1BQU0xRixNQUFNLENBQUNFLE9BQU8sR0FBRztvQkFFNURjLFFBQVFDLEdBQUcsQ0FBQztvQkFFWiwyREFBMkQ7b0JBQzNELElBQUl5RSxNQUFNVCxlQUFlLEtBQUssYUFDMUJTLE1BQU1iLGVBQWUsQ0FBQ21FLE1BQU0sR0FBRyxLQUMvQnRELE1BQU1aLFlBQVksQ0FBQ2tFLE1BQU0sR0FBRyxHQUFHO3dCQUVqQywrQkFBK0I7d0JBQy9CLElBQUl0RCxNQUFNVCxlQUFlLEtBQUssV0FBVzs0QkFDdkNxTyxlQUFlN0s7d0JBQ2pCO3dCQUVBLHNDQUFzQzt3QkFDdEMsTUFBTTdDLFlBQVksSUFBSXJDLE9BQU82SCxjQUFjLENBQUMsU0FBUzs0QkFDbkRnSixPQUFPOzRCQUNQQyxLQUFLOzRCQUNMQyxNQUFNOzRCQUNOQyxRQUFROzRCQUNSQyxRQUFRO3dCQUNWO3dCQUNBLE1BQU1DLFlBQVksR0FBcUM3TyxPQUFsQytDLGVBQWVHLElBQUksRUFBQyxnQkFBd0IsT0FBVmxELFdBQVU7d0JBRWpFK0IsZUFBZStNLGdCQUFnQixDQUFDRCxXQUFXOUwsZUFBZTNJLE1BQU0sRUFDN0RxTSxJQUFJO3lEQUFDLE9BQU9zSTtnQ0FDWCxNQUFNaE4sZUFBZWlOLFdBQVcsQ0FDOUJELGdCQUNBaE0sZUFBZTNJLE1BQU0sRUFDckIwRixNQUFNYixlQUFlLEVBQ3JCYSxNQUFNWixZQUFZLEVBQ2xCWSxNQUFNVixrQkFBa0IsRUFDeEJVLE1BQU1SLGNBQWMsRUFDcEJRLE1BQU1QLGNBQWMsRUFDcEJPLE1BQU1OLGlCQUFpQixFQUN2QjtnQ0FFRnBFLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJ3VDs0QkFDdkM7O29CQUNKO29CQUVBLDRCQUE0QjtvQkFDNUJ4TCxTQUFTO3dCQUFFekMsTUFBTTtvQkFBdUI7b0JBRXhDLDhFQUE4RTtvQkFDOUUsSUFBSWQsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTyxJQUFJeUYsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTyxJQUFJd0YsTUFBTVQsZUFBZSxLQUFLLFdBQVc7d0JBQ3ZGLE1BQU00UCxrQkFBa0I7NEJBQ3RCNVUsU0FBU3lGLE1BQU1SLGNBQWM7NEJBQzdCaEYsU0FBU3dGLE1BQU1QLGNBQWM7NEJBQzdCekMsWUFBWWdELE1BQU1OLGlCQUFpQjt3QkFDckM7d0JBQ0F1QyxlQUFldU0sNEJBQTRCLENBQUN4TyxNQUFNMUYsTUFBTSxFQUFFd0gsV0FBV3FOLGlCQUNsRXhJLElBQUk7eURBQUMsQ0FBQ3lJO2dDQUNMbk4sZUFBZXdNLGlCQUFpQixDQUFDVztnQ0FDakM5VCxRQUFRQyxHQUFHLENBQUMsMkNBQTJDeUUsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTyxFQUFFLEtBQUt5RixNQUFNMUYsTUFBTSxDQUFDRSxPQUFPLEVBQUUsa0JBQWtCMlU7Z0NBRTFIM0wsTUFBTTtvQ0FDSmdILE9BQU87b0NBQ1BDLGFBQWEsdURBQStFekssT0FBeEJBLE1BQU0xRixNQUFNLENBQUNDLE9BQU8sRUFBQyxLQUF3QixPQUFyQnlGLE1BQU0xRixNQUFNLENBQUNFLE9BQU87b0NBQ2hIa1EsVUFBVTtnQ0FDWjs0QkFDRjs7b0JBQ0osT0FBTzt3QkFDTCxtRkFBbUY7d0JBQ25GekksZUFBZUUsbUJBQW1CO3dCQUNsQzdHLFFBQVFDLEdBQUcsQ0FBQztvQkFDZDtnQkFDRjtZQUNGO1FBQ0Y7b0NBQUc7UUFBQ3lFLE1BQU0xRixNQUFNLENBQUNDLE9BQU87UUFBRXlGLE1BQU0xRixNQUFNLENBQUNFLE9BQU87S0FBQztJQUUvQyw4Q0FBOEM7SUFDOUNqQixnREFBU0E7cUNBQUM7WUFDUixNQUFNNkssaUJBQWlCbEssZ0VBQWNBLENBQUNnSSxXQUFXO1lBQ2pELE1BQU1tTixrQkFBa0JsVixpRUFBZUEsQ0FBQytILFdBQVc7WUFDbkQsTUFBTW9OLGdCQUFnQmxWLCtEQUFhQSxDQUFDOEgsV0FBVztZQUMvQyxNQUFNRCxpQkFBaUJoSSxnRUFBY0EsQ0FBQ2lJLFdBQVc7WUFFakQsaUNBQWlDO1lBQ2pDLE1BQU1xTixxQkFBcUJuTCxlQUFlb0wsV0FBVztnRUFBQyxDQUFDbEwsVUFBVW1MO29CQUMvRG5VLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEQsT0FBaEMrSSxXQUFXLFdBQVc7b0JBRWhFLG1EQUFtRDtvQkFDbkQsSUFBSSxDQUFDQSxZQUFZLENBQUNtTCxXQUFXO3dCQUMzQiwrQkFBK0I7d0JBQy9CLElBQUl6UCxNQUFNVCxlQUFlLEtBQUssV0FBVzs0QkFDdkNqRSxRQUFRQyxHQUFHLENBQUM7NEJBQ1pnSSxTQUFTO2dDQUFFekMsTUFBTTs0QkFBa0I7NEJBRW5DLDRCQUE0Qjs0QkFDNUIsTUFBTW1CLGlCQUFpQmhJLGdFQUFjQSxDQUFDaUksV0FBVzs0QkFDakQsTUFBTWEsbUJBQW1CZCxlQUFlZSxtQkFBbUI7NEJBQzNELElBQUlELGtCQUFrQjtnQ0FDcEIsTUFBTUUsaUJBQWlCaEIsZUFBZWlCLFdBQVcsQ0FBQ0g7Z0NBQ2xELElBQUlFLGdCQUFnQjtvQ0FDbEIsZ0NBQWdDO29DQUNoQyxNQUFNL0MsWUFBWSxJQUFJckMsT0FBTzZILGNBQWMsQ0FBQyxTQUFTO3dDQUNuRGdKLE9BQU87d0NBQ1BDLEtBQUs7d0NBQ0xDLE1BQU07d0NBQ05DLFFBQVE7d0NBQ1JDLFFBQVE7b0NBQ1Y7b0NBQ0EsTUFBTVksY0FBYyxHQUEwQ3hQLE9BQXZDK0MsZUFBZUcsSUFBSSxFQUFDLHFCQUE2QixPQUFWbEQsV0FBVTtvQ0FFeEUrQixlQUFlK00sZ0JBQWdCLENBQUNVLGFBQWF6TSxlQUFlM0ksTUFBTSxFQUMvRHFNLElBQUk7d0ZBQUMsT0FBT2dKOzRDQUNYLE1BQU0xTixlQUFlaU4sV0FBVyxDQUM5QlMsaUJBQ0EzUCxNQUFNMUYsTUFBTSxFQUNaMEYsTUFBTWIsZUFBZSxFQUNyQmEsTUFBTVosWUFBWSxFQUNsQlksTUFBTVYsa0JBQWtCLEVBQ3hCVSxNQUFNUixjQUFjLEVBQ3BCUSxNQUFNUCxjQUFjLEVBQ3BCTyxNQUFNTixpQkFBaUIsRUFDdkI7NENBRUZwRSxRQUFRQyxHQUFHLENBQUMsc0NBQXNDbVU7d0NBQ3BEOztnQ0FDSjs0QkFDRjt3QkFDRjt3QkFFQSx1REFBdUQ7d0JBQ3ZEbkssOEJBQ0UseUJBQ0Esb0lBQ0FXLEtBQUssQ0FBQzVLLFFBQVFFLEtBQUs7d0JBRXJCZ0ksTUFBTTs0QkFDSmdILE9BQU87NEJBQ1BDLGFBQWE7NEJBQ2IrQyxTQUFTOzRCQUNUOUMsVUFBVTt3QkFDWjtvQkFDRixPQUFPLElBQUlwRyxZQUFZLENBQUNtTCxXQUFXO3dCQUNqQyxrREFBa0Q7d0JBQ2xERyxxQ0FBcUMxSixLQUFLLENBQUM1SyxRQUFRRSxLQUFLO3dCQUV4RCx3RkFBd0Y7d0JBQ3hGK0osOEJBQ0UsdUJBQ0EsbUtBQ0FXLEtBQUssQ0FBQzVLLFFBQVFFLEtBQUs7d0JBRXJCZ0ksTUFBTTs0QkFDSmdILE9BQU87NEJBQ1BDLGFBQWE7NEJBQ2JDLFVBQVU7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsMkJBQTJCO1lBQzNCLE1BQU1tRixvQkFBb0JQLGNBQWNFLFdBQVc7K0RBQUMsQ0FBQ007b0JBQ25ELE1BQU1DLFNBQVNELE9BQU9FLGNBQWMsR0FBRyxPQUFPO29CQUM5QyxJQUFJRCxTQUFTLEtBQUs7d0JBQ2hCelUsUUFBUUcsSUFBSSxDQUFDLG1DQUEyQyxPQUFsQnNVLE9BQU92UixPQUFPLENBQUMsSUFBRzt3QkFDeERnRixNQUFNOzRCQUNKZ0gsT0FBTzs0QkFDUEMsYUFBYSx5QkFBMkMsT0FBbEJzRixPQUFPdlIsT0FBTyxDQUFDLElBQUc7NEJBQ3hEZ1AsU0FBUzs0QkFDVDlDLFVBQVU7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsbUJBQW1CO1lBQ25CLE1BQU11RjswREFBZTtvQkFDbkIsSUFBSTt3QkFDRix1REFBdUQ7d0JBQ3ZELE1BQU1sTixtQkFBbUJkLGVBQWVlLG1CQUFtQjt3QkFDM0QsSUFBSUQsa0JBQWtCOzRCQUNwQixNQUFNZCxlQUFlaU4sV0FBVyxDQUM5Qm5NLGtCQUNBL0MsTUFBTTFGLE1BQU0sRUFDWjBGLE1BQU1iLGVBQWUsRUFDckJhLE1BQU1aLFlBQVksRUFDbEJZLE1BQU1WLGtCQUFrQixFQUN4QlUsTUFBTVIsY0FBYyxFQUNwQlEsTUFBTVAsY0FBYyxFQUNwQk8sTUFBTU4saUJBQWlCLEVBQ3ZCTSxNQUFNVCxlQUFlLEtBQUs7d0JBRTlCO3dCQUVBLHNDQUFzQzt3QkFDdENRLHdCQUF3QkM7b0JBQzFCLEVBQUUsT0FBT3hFLE9BQU87d0JBQ2RGLFFBQVFFLEtBQUssQ0FBQyxxQkFBcUJBO29CQUNyQztnQkFDRjs7WUFFQTZULGdCQUFnQmEsTUFBTSxDQUFDRCxjQUFjLFFBQVEsNkJBQTZCO1lBRTFFLHFFQUFxRTtZQUNyRSxNQUFNRTtnRUFBcUIsQ0FBQ0M7b0JBQzFCLGlDQUFpQztvQkFDakNIO29CQUVBLGtDQUFrQztvQkFDbEMsSUFBSWpRLE1BQU1ULGVBQWUsS0FBSyxXQUFXO3dCQUN2QyxNQUFNd0UsVUFBVTt3QkFDaEJxTSxNQUFNQyxXQUFXLEdBQUd0TTt3QkFDcEIsT0FBT0E7b0JBQ1Q7Z0JBQ0Y7O1lBRUFyQixPQUFPb0UsZ0JBQWdCLENBQUMsZ0JBQWdCcUo7WUFFeEMsbUJBQW1CO1lBQ25COzZDQUFPO29CQUNMWjtvQkFDQU07b0JBQ0FSLGdCQUFnQmlCLE9BQU87b0JBQ3ZCNU4sT0FBT21FLG1CQUFtQixDQUFDLGdCQUFnQnNKO2dCQUM3Qzs7UUFDRjtvQ0FBRztRQUFDblE7UUFBT3dEO0tBQU07SUFFakIscUNBQXFDO0lBQ3JDakssZ0RBQVNBO3FDQUFDO1lBQ1IsTUFBTThWLGtCQUFrQmxWLGlFQUFlQSxDQUFDK0gsV0FBVztZQUNuRG1OLGdCQUFnQmtCLE9BQU87UUFDekI7b0NBQUc7UUFBQ3ZRLE1BQU1ULGVBQWU7S0FBQztJQUUxQiwrQkFBK0I7SUFDL0IsTUFBTWlSLHFCQUFxQmhYLGtEQUFXQTsyREFBQztZQUNyQyxJQUFJO2dCQUNGLE1BQU15SSxpQkFBaUJoSSxnRUFBY0EsQ0FBQ2lJLFdBQVc7Z0JBQ2pELE1BQU1hLG1CQUFtQmQsZUFBZWUsbUJBQW1CO2dCQUUzRCxJQUFJLENBQUNELGtCQUFrQjtvQkFDckIsK0VBQStFO29CQUMvRSxJQUFJL0MsTUFBTTFGLE1BQU0sQ0FBQ0MsT0FBTyxJQUFJeUYsTUFBTTFGLE1BQU0sQ0FBQ0UsT0FBTyxJQUMzQ3dGLENBQUFBLE1BQU1ULGVBQWUsS0FBSyxhQUFhUyxNQUFNYixlQUFlLENBQUNtRSxNQUFNLEdBQUcsS0FBS3RELE1BQU1aLFlBQVksQ0FBQ2tFLE1BQU0sR0FBRyxJQUFJO3dCQUM5RyxNQUFNNkwsa0JBQWtCOzRCQUN0QjVVLFNBQVN5RixNQUFNUixjQUFjOzRCQUM3QmhGLFNBQVN3RixNQUFNUCxjQUFjOzRCQUM3QnpDLFlBQVlnRCxNQUFNTixpQkFBaUI7d0JBQ3JDO3dCQUNBLE1BQU15RCxZQUFZLE1BQU1sQixlQUFldU0sNEJBQTRCLENBQUN4TyxNQUFNMUYsTUFBTSxFQUFFd0gsV0FBV3FOO3dCQUM3RmxOLGVBQWV3TSxpQkFBaUIsQ0FBQ3RMO3dCQUNqQyxNQUFNbEIsZUFBZWlOLFdBQVcsQ0FDOUIvTCxXQUNBbkQsTUFBTTFGLE1BQU0sRUFDWjBGLE1BQU1iLGVBQWUsRUFDckJhLE1BQU1aLFlBQVksRUFDbEJZLE1BQU1WLGtCQUFrQixFQUN4QlUsTUFBTVIsY0FBYyxFQUNwQlEsTUFBTVAsY0FBYyxFQUNwQk8sTUFBTU4saUJBQWlCLEVBQ3ZCTSxNQUFNVCxlQUFlLEtBQUs7d0JBRTVCLE9BQU87b0JBQ1Q7b0JBQ0FqRSxRQUFRQyxHQUFHLENBQUM7b0JBQ1osT0FBTztnQkFDVDtnQkFFQSx3QkFBd0I7Z0JBQ3hCLE9BQU8sTUFBTTBHLGVBQWVpTixXQUFXLENBQ3JDbk0sa0JBQ0EvQyxNQUFNMUYsTUFBTSxFQUNaMEYsTUFBTWIsZUFBZSxFQUNyQmEsTUFBTVosWUFBWSxFQUNsQlksTUFBTVYsa0JBQWtCLEVBQ3hCVSxNQUFNUixjQUFjLEVBQ3BCUSxNQUFNUCxjQUFjLEVBQ3BCTyxNQUFNTixpQkFBaUIsRUFDdkJNLE1BQU1ULGVBQWUsS0FBSztZQUU5QixFQUFFLE9BQU8vRCxPQUFPO2dCQUNkRixRQUFRRSxLQUFLLENBQUMsbUNBQW1DQTtnQkFDakQsT0FBTztZQUNUO1FBQ0Y7MERBQUc7UUFBQ3dFO0tBQU07SUFFVixnQkFBZ0I7SUFDaEIsTUFBTXlRLGVBQW1DO1FBQ3ZDLEdBQUd6USxLQUFLO1FBQ1J1RDtRQUNBNEU7UUFDQXdFO1FBQ0FtQjtRQUNBN0g7UUFDQXdIO1FBQ0FHO1FBQ0FSO1FBQ0FvRDtRQUNBN1EsZUFBZUssTUFBTUwsYUFBYTtRQUNsQ0osaUJBQWlCUyxNQUFNVCxlQUFlO1FBQ3RDbVIsYUFBYTFRLE1BQU1ULGVBQWUsS0FBSztJQUN6QztJQUVBLHFCQUNFLDhEQUFDNkMsZUFBZXVPLFFBQVE7UUFBQ0MsT0FBT0g7a0JBQzdCbk87Ozs7OztBQUdQLEVBQUU7R0E5M0NXRDs7UUFrRU90SSxzREFBUUE7OztLQWxFZnNJO0FBZzRDYix5Q0FBeUM7QUFDbEMsTUFBTXdPLG9CQUFvQjs7SUFDL0IsTUFBTUMsVUFBVXpYLGlEQUFVQSxDQUFDK0k7SUFDM0IsSUFBSTBPLFlBQVloUCxXQUFXO1FBQ3pCLE1BQU0sSUFBSWlQLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUU7SUFOV0QiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxUcmFkaW5nQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgdHlwZSB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VSZWR1Y2VyLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgdHlwZSB7IFRyYWRpbmdDb25maWcsIFRhcmdldFByaWNlUm93LCBPcmRlckhpc3RvcnlFbnRyeSwgQXBwU2V0dGluZ3MsIE9yZGVyU3RhdHVzLCBEaXNwbGF5T3JkZXJSb3cgfSBmcm9tICdAL2xpYi90eXBlcyc7XG5pbXBvcnQgeyBERUZBVUxUX0FQUF9TRVRUSU5HUywgQVZBSUxBQkxFX0NSWVBUT1MsIEFWQUlMQUJMRV9RVU9URVNfU0lNUExFLCBBVkFJTEFCTEVfU1RBQkxFQ09JTlMgfSBmcm9tICdAL2xpYi90eXBlcyc7XG5pbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJztcblxuLy8gRGVmaW5lIGxvY2FsbHkgdG8gYXZvaWQgaW1wb3J0IGlzc3Vlc1xuY29uc3QgREVGQVVMVF9RVU9URV9DVVJSRU5DSUVTID0gW1wiVVNEVFwiLCBcIlVTRENcIiwgXCJCVENcIl07XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiO1xuaW1wb3J0IHsgdHJhZGluZ0FwaSB9IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgeyBTZXNzaW9uTWFuYWdlciB9IGZyb20gJ0AvbGliL3Nlc3Npb24tbWFuYWdlcic7XG5pbXBvcnQgeyBOZXR3b3JrTW9uaXRvciwgQXV0b1NhdmVNYW5hZ2VyLCBNZW1vcnlNb25pdG9yIH0gZnJvbSAnQC9saWIvbmV0d29yay1tb25pdG9yJztcblxuLy8gRGVmaW5lIEJvdCBTeXN0ZW0gU3RhdHVzXG5leHBvcnQgdHlwZSBCb3RTeXN0ZW1TdGF0dXMgPSAnU3RvcHBlZCcgfCAnV2FybWluZ1VwJyB8ICdSdW5uaW5nJztcblxuLy8gVXBkYXRlIFRhcmdldFByaWNlUm93IHR5cGVcbi8vIChBc3N1bWluZyBUYXJnZXRQcmljZVJvdyBpcyBkZWZpbmVkIGluIEAvbGliL3R5cGVzLCB0aGlzIGlzIGNvbmNlcHR1YWwpXG4vLyBleHBvcnQgaW50ZXJmYWNlIFRhcmdldFByaWNlUm93IHtcbi8vICAgLy8gLi4uIGV4aXN0aW5nIGZpZWxkc1xuLy8gICBsYXN0QWN0aW9uVGltZXN0YW1wPzogbnVtYmVyO1xuLy8gfVxuXG4vLyBEZWZpbmUgYWN0aW9uIHR5cGVzXG50eXBlIEFjdGlvbiA9XG4gIHwgeyB0eXBlOiAnU0VUX0NPTkZJRyc7IHBheWxvYWQ6IFBhcnRpYWw8VHJhZGluZ0NvbmZpZz4gfVxuICB8IHsgdHlwZTogJ1NFVF9UQVJHRVRfUFJJQ0VfUk9XUyc7IHBheWxvYWQ6IFRhcmdldFByaWNlUm93W10gfVxuICB8IHsgdHlwZTogJ0FERF9UQVJHRVRfUFJJQ0VfUk9XJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3cgfVxuICB8IHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3cgfVxuICB8IHsgdHlwZTogJ1JFTU9WRV9UQVJHRVRfUFJJQ0VfUk9XJzsgcGF5bG9hZDogc3RyaW5nIH1cbiAgfCB7IHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWSc7IHBheWxvYWQ6IE9yZGVySGlzdG9yeUVudHJ5IH1cbiAgfCB7IHR5cGU6ICdVUERBVEVfT1JERVJfSElTVE9SWV9FTlRSWSc7IHBheWxvYWQ6IE9yZGVySGlzdG9yeUVudHJ5IH1cbiAgfCB7IHR5cGU6ICdSRU1PVkVfT1JERVJfSElTVE9SWV9FTlRSWSc7IHBheWxvYWQ6IHN0cmluZyB9XG4gIHwgeyB0eXBlOiAnU0VUX0FQUF9TRVRUSU5HUyc7IHBheWxvYWQ6IFBhcnRpYWw8QXBwU2V0dGluZ3M+IH1cbiAgfCB7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnOyBwYXlsb2FkOiAnb25saW5lJyB8ICdvZmZsaW5lJyB9XG4gIHwgeyB0eXBlOiAnU0VUX01BUktFVF9QUklDRSc7IHBheWxvYWQ6IG51bWJlciB9XG4gIHwgeyB0eXBlOiAnU0VUX0JBTEFOQ0VTJzsgcGF5bG9hZDogeyBjcnlwdG8xOiBudW1iZXI7IGNyeXB0bzI6IG51bWJlciB9IH1cbiAgfCB7IHR5cGU6ICdTWVNURU1fU1RBUlRfQk9UX0lOSVRJQVRFJyB9XG4gIHwgeyB0eXBlOiAnU1lTVEVNX0NPTVBMRVRFX1dBUk1VUCcgfVxuICB8IHsgdHlwZTogJ1NZU1RFTV9TVE9QX0JPVCcgfVxuICB8IHsgdHlwZTogJ1NFVF9UQVJHRVRfUFJJQ0VfUk9XUyc7IHBheWxvYWQ6IFRhcmdldFByaWNlUm93W10gfTtcblxuLy8gRGVmaW5lIHN0YXRlIHR5cGVcbmludGVyZmFjZSBUcmFkaW5nU3RhdGUge1xuICBjb25maWc6IFRyYWRpbmdDb25maWc7XG4gIHRhcmdldFByaWNlUm93czogVGFyZ2V0UHJpY2VSb3dbXTtcbiAgb3JkZXJIaXN0b3J5OiBPcmRlckhpc3RvcnlFbnRyeVtdO1xuICBhcHBTZXR0aW5nczogQXBwU2V0dGluZ3M7XG4gIGJhY2tlbmRTdGF0dXM6ICdvbmxpbmUnIHwgJ29mZmxpbmUnIHwgJ3Vua25vd24nO1xuICBjdXJyZW50TWFya2V0UHJpY2U6IG51bWJlcjtcbiAgY3J5cHRvMUJhbGFuY2U6IG51bWJlcjtcbiAgY3J5cHRvMkJhbGFuY2U6IG51bWJlcjtcbiAgLy8gaXNCb3RBY3RpdmU6IGJvb2xlYW47IC8vIFRvIGJlIHJlcGxhY2VkIGJ5IGJvdFN5c3RlbVN0YXR1c1xuICBib3RTeXN0ZW1TdGF0dXM6IEJvdFN5c3RlbVN0YXR1czsgLy8gTmV3IHN0YXRlIGZvciBib3QgbGlmZWN5Y2xlXG4gIHN0YWJsZWNvaW5CYWxhbmNlOiBudW1iZXI7IC8vIEZvciBTdGFibGVjb2luIFN3YXAgbW9kZVxufVxuXG4vLyBBZGQgdGhpcyBmdW5jdGlvbiB0byBjYWxjdWxhdGUgdGhlIGluaXRpYWwgbWFya2V0IHByaWNlXG5jb25zdCBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UgPSAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogbnVtYmVyID0+IHtcbiAgLy8gUmV0dXJuIDAgaWYgZWl0aGVyIGNyeXB0byBpcyBub3Qgc2VsZWN0ZWRcbiAgaWYgKCFjb25maWcuY3J5cHRvMSB8fCAhY29uZmlnLmNyeXB0bzIpIHtcbiAgICByZXR1cm4gMDtcbiAgfVxuICAvLyBEZWZhdWx0IGZhbGxiYWNrIHZhbHVlIGZvciB2YWxpZCBwYWlyc1xuICByZXR1cm4gY2FsY3VsYXRlRmFsbGJhY2tNYXJrZXRQcmljZShjb25maWcpO1xufTtcblxuLy8gRW5oYW5jZWQgQVBJIGZ1bmN0aW9uIHRvIGdldCBtYXJrZXQgcHJpY2UgZm9yIGFueSB0cmFkaW5nIHBhaXJcbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgcmVhbC10aW1lIG1hcmtldCBwcmljZSBmcm9tIEJpbmFuY2UgQVBJXG5jb25zdCBnZXRNYXJrZXRQcmljZUZyb21BUEkgPSBhc3luYyAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBSZXR1cm4gMCBpZiBlaXRoZXIgY3J5cHRvIGlzIG5vdCBzZWxlY3RlZFxuICAgIGlmICghY29uZmlnLmNyeXB0bzEgfHwgIWNvbmZpZy5jcnlwdG8yKSB7XG4gICAgICByZXR1cm4gMDtcbiAgICB9XG5cbiAgICAvLyBQcmltYXJ5OiBUcnkgQmluYW5jZSBBUEkgd2l0aCBtdWx0aXBsZSBzeW1ib2wgZm9ybWF0c1xuICAgIGNvbnN0IHN5bWJvbDEgPSBgJHtjb25maWcuY3J5cHRvMX0ke2NvbmZpZy5jcnlwdG8yfWAudG9VcHBlckNhc2UoKTtcbiAgICBjb25zdCBzeW1ib2wyID0gYCR7Y29uZmlnLmNyeXB0bzF9VVNEVGAudG9VcHBlckNhc2UoKTsgLy8gRm9yIGNyb3NzLWNhbGN1bGF0aW9uXG4gICAgY29uc3Qgc3ltYm9sMyA9IGAke2NvbmZpZy5jcnlwdG8yfVVTRFRgLnRvVXBwZXJDYXNlKCk7IC8vIEZvciBjcm9zcy1jYWxjdWxhdGlvblxuXG4gICAgLy8gVHJ5IGRpcmVjdCBwYWlyIGZpcnN0XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHBzOi8vYXBpLmJpbmFuY2UuY29tL2FwaS92My90aWNrZXIvcHJpY2U/c3ltYm9sPSR7c3ltYm9sMX1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCBwcmljZSA9IHBhcnNlRmxvYXQoZGF0YS5wcmljZSk7XG4gICAgICAgIGlmIChwcmljZSA+IDApIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIERpcmVjdCBwcmljZSBmcm9tIEJpbmFuY2U6ICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ID0gJHtwcmljZX1gKTtcbiAgICAgICAgICByZXR1cm4gcHJpY2U7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKGBEaXJlY3QgcGFpciAke3N5bWJvbDF9IG5vdCBmb3VuZCBvbiBCaW5hbmNlLCB0cnlpbmcgY3Jvc3MtY2FsY3VsYXRpb24uLi5gKTtcbiAgICB9XG5cbiAgICAvLyBUcnkgY3Jvc3MtY2FsY3VsYXRpb24gdmlhIFVTRFQgaWYgZGlyZWN0IHBhaXIgbm90IGF2YWlsYWJsZVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBbcmVzcG9uc2UxLCByZXNwb25zZTJdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBmZXRjaChgaHR0cHM6Ly9hcGkuYmluYW5jZS5jb20vYXBpL3YzL3RpY2tlci9wcmljZT9zeW1ib2w9JHtzeW1ib2wyfWApLFxuICAgICAgICBmZXRjaChgaHR0cHM6Ly9hcGkuYmluYW5jZS5jb20vYXBpL3YzL3RpY2tlci9wcmljZT9zeW1ib2w9JHtzeW1ib2wzfWApXG4gICAgICBdKTtcblxuICAgICAgaWYgKHJlc3BvbnNlMS5vayAmJiByZXNwb25zZTIub2spIHtcbiAgICAgICAgY29uc3QgW2RhdGExLCBkYXRhMl0gPSBhd2FpdCBQcm9taXNlLmFsbChbcmVzcG9uc2UxLmpzb24oKSwgcmVzcG9uc2UyLmpzb24oKV0pO1xuICAgICAgICBjb25zdCBwcmljZTEgPSBwYXJzZUZsb2F0KGRhdGExLnByaWNlKTsgLy8gY3J5cHRvMS9VU0RUXG4gICAgICAgIGNvbnN0IHByaWNlMiA9IHBhcnNlRmxvYXQoZGF0YTIucHJpY2UpOyAvLyBjcnlwdG8yL1VTRFRcblxuICAgICAgICBpZiAocHJpY2UxID4gMCAmJiBwcmljZTIgPiAwKSB7XG4gICAgICAgICAgY29uc3QgY3Jvc3NQcmljZSA9IHByaWNlMSAvIHByaWNlMjsgLy8gY3J5cHRvMS9jcnlwdG8yXG4gICAgICAgICAgY29uc29sZS5sb2coYOKchSBDcm9zcy1jYWxjdWxhdGVkIHByaWNlIGZyb20gQmluYW5jZTogJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn0gPSAke2Nyb3NzUHJpY2V9ICgke3ByaWNlMX0vJHtwcmljZTJ9KWApO1xuICAgICAgICAgIHJldHVybiBjcm9zc1ByaWNlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignQmluYW5jZSBjcm9zcy1jYWxjdWxhdGlvbiBmYWlsZWQsIHRyeWluZyBDb2luR2Vja28uLi4nLCBlcnJvcik7XG4gICAgfVxuXG4gICAgLy8gRmFsbGJhY2sgdG8gQ29pbkdlY2tvIEFQSSBmb3IgYnJvYWRlciBwYWlyIHN1cHBvcnRcbiAgICB0cnkge1xuICAgICAgY29uc3QgY3J5cHRvMUlkID0gZ2V0Q29pbkdlY2tvSWQoY29uZmlnLmNyeXB0bzEpO1xuICAgICAgY29uc3QgY3J5cHRvMklkID0gZ2V0Q29pbkdlY2tvSWQoY29uZmlnLmNyeXB0bzIpO1xuXG4gICAgICBpZiAoY3J5cHRvMUlkICYmIGNyeXB0bzJJZCkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS5jb2luZ2Vja28uY29tL2FwaS92My9zaW1wbGUvcHJpY2U/aWRzPSR7Y3J5cHRvMUlkfSZ2c19jdXJyZW5jaWVzPSR7Y3J5cHRvMklkfWApO1xuICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgIGNvbnN0IHByaWNlID0gZGF0YVtjcnlwdG8xSWRdPy5bY3J5cHRvMklkXTtcbiAgICAgICAgICBpZiAocHJpY2UgPiAwKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFByaWNlIGZldGNoZWQgZnJvbSBDb2luR2Vja286ICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ID0gJHtwcmljZX1gKTtcbiAgICAgICAgICAgIHJldHVybiBwcmljZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChnZWNrb0Vycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ0NvaW5HZWNrbyBBUEkgZmFpbGVkLCB1c2luZyBVU0QgY2FsY3VsYXRpb24uLi4nLCBnZWNrb0Vycm9yKTtcbiAgICB9XG5cbiAgICAvLyBGaW5hbCBmYWxsYmFjayAtIGNhbGN1bGF0ZSBmcm9tIHJlYWwtdGltZSBVU0QgcHJpY2VzXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNyeXB0bzFVU0RQcmljZSA9IGF3YWl0IGdldFJlYWxUaW1lVVNEUHJpY2UoY29uZmlnLmNyeXB0bzEpO1xuICAgICAgY29uc3QgY3J5cHRvMlVTRFByaWNlID0gYXdhaXQgZ2V0UmVhbFRpbWVVU0RQcmljZShjb25maWcuY3J5cHRvMik7XG4gICAgICBjb25zdCBjYWxjdWxhdGVkUHJpY2UgPSBjcnlwdG8xVVNEUHJpY2UgLyBjcnlwdG8yVVNEUHJpY2U7XG4gICAgICBjb25zb2xlLmxvZyhg8J+TiiBDYWxjdWxhdGVkIHByaWNlIGZyb20gcmVhbC10aW1lIFVTRCByYXRlczogJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn0gPSAke2NhbGN1bGF0ZWRQcmljZX1gKTtcbiAgICAgIHJldHVybiBjYWxjdWxhdGVkUHJpY2U7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignUmVhbC10aW1lIFVTRCBwcmljZSBjYWxjdWxhdGlvbiBmYWlsZWQsIHVzaW5nIHN0YXRpYyBmYWxsYmFjay4uLicsIGVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBGaW5hbCBmYWxsYmFjayB0byBzdGF0aWMgY2FsY3VsYXRpb25cbiAgICBjb25zdCBtb2NrUHJpY2UgPSBjYWxjdWxhdGVGYWxsYmFja01hcmtldFByaWNlKGNvbmZpZyk7XG4gICAgY29uc29sZS5sb2coYOKaoO+4jyBVc2luZyBzdGF0aWMgZmFsbGJhY2sgcHJpY2U6ICR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ID0gJHttb2NrUHJpY2V9YCk7XG4gICAgcmV0dXJuIG1vY2tQcmljZTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1hcmtldCBwcmljZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIGNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UoY29uZmlnKTtcbiAgfVxufTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIG1hcCBjcnlwdG8gc3ltYm9scyB0byBDb2luR2Vja28gSURzXG5jb25zdCBnZXRDb2luR2Vja29JZCA9IChzeW1ib2w6IHN0cmluZyk6IHN0cmluZyB8IG51bGwgPT4ge1xuICBjb25zdCBtYXBwaW5nOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgICdCVEMnOiAnYml0Y29pbicsXG4gICAgJ0VUSCc6ICdldGhlcmV1bScsXG4gICAgJ1NPTCc6ICdzb2xhbmEnLFxuICAgICdBREEnOiAnY2FyZGFubycsXG4gICAgJ0RPVCc6ICdwb2xrYWRvdCcsXG4gICAgJ01BVElDJzogJ21hdGljLW5ldHdvcmsnLFxuICAgICdBVkFYJzogJ2F2YWxhbmNoZS0yJyxcbiAgICAnTElOSyc6ICdjaGFpbmxpbmsnLFxuICAgICdVTkknOiAndW5pc3dhcCcsXG4gICAgJ1VTRFQnOiAndGV0aGVyJyxcbiAgICAnVVNEQyc6ICd1c2QtY29pbicsXG4gICAgJ0JVU0QnOiAnYmluYW5jZS11c2QnLFxuICAgICdEQUknOiAnZGFpJ1xuICB9O1xuICByZXR1cm4gbWFwcGluZ1tzeW1ib2wudG9VcHBlckNhc2UoKV0gfHwgbnVsbDtcbn07XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgc3RhYmxlY29pbiBleGNoYW5nZSByYXRlcyBmb3IgcmVhbCBtYXJrZXQgZGF0YVxuY29uc3QgZ2V0U3RhYmxlY29pbkV4Y2hhbmdlUmF0ZSA9IGFzeW5jIChjcnlwdG86IHN0cmluZywgc3RhYmxlY29pbjogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+ID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBGb3Igc3RhYmxlY29pbi10by1zdGFibGVjb2luLCBhc3N1bWUgMToxIHJhdGVcbiAgICBpZiAoZ2V0Q29pbkdlY2tvSWQoY3J5cHRvKSAmJiBnZXRDb2luR2Vja29JZChzdGFibGVjb2luKSkge1xuICAgICAgY29uc3QgY3J5cHRvSWQgPSBnZXRDb2luR2Vja29JZChjcnlwdG8pO1xuICAgICAgY29uc3Qgc3RhYmxlY29pbklkID0gZ2V0Q29pbkdlY2tvSWQoc3RhYmxlY29pbik7XG5cbiAgICAgIGlmIChjcnlwdG9JZCA9PT0gc3RhYmxlY29pbklkKSByZXR1cm4gMS4wOyAvLyBTYW1lIGN1cnJlbmN5XG5cbiAgICAgIC8vIEdldCByZWFsIGV4Y2hhbmdlIHJhdGUgZnJvbSBDb2luR2Vja29cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHBzOi8vYXBpLmNvaW5nZWNrby5jb20vYXBpL3YzL3NpbXBsZS9wcmljZT9pZHM9JHtjcnlwdG9JZH0mdnNfY3VycmVuY2llcz0ke3N0YWJsZWNvaW5JZH1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCByYXRlID0gY3J5cHRvSWQgJiYgc3RhYmxlY29pbklkID8gZGF0YVtjcnlwdG9JZF0/LltzdGFibGVjb2luSWRdIDogbnVsbDtcbiAgICAgICAgaWYgKHJhdGUgPiAwKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCfk4ogU3RhYmxlY29pbiByYXRlOiAke2NyeXB0b30vJHtzdGFibGVjb2lufSA9ICR7cmF0ZX1gKTtcbiAgICAgICAgICByZXR1cm4gcmF0ZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrOiBjYWxjdWxhdGUgdmlhIFVTRCBwcmljZXNcbiAgICBjb25zdCBjcnlwdG9VU0RQcmljZSA9IGdldFVTRFByaWNlKGNyeXB0byk7XG4gICAgY29uc3Qgc3RhYmxlY29pblVTRFByaWNlID0gZ2V0VVNEUHJpY2Uoc3RhYmxlY29pbik7XG4gICAgY29uc3QgcmF0ZSA9IGNyeXB0b1VTRFByaWNlIC8gc3RhYmxlY29pblVTRFByaWNlO1xuXG4gICAgY29uc29sZS5sb2coYPCfk4ogRmFsbGJhY2sgc3RhYmxlY29pbiByYXRlOiAke2NyeXB0b30vJHtzdGFibGVjb2lufSA9ICR7cmF0ZX0gKHZpYSBVU0QpYCk7XG4gICAgcmV0dXJuIHJhdGU7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdGFibGVjb2luIGV4Y2hhbmdlIHJhdGU6JywgZXJyb3IpO1xuICAgIC8vIEZpbmFsIGZhbGxiYWNrXG4gICAgY29uc3QgY3J5cHRvVVNEUHJpY2UgPSBnZXRVU0RQcmljZShjcnlwdG8pO1xuICAgIGNvbnN0IHN0YWJsZWNvaW5VU0RQcmljZSA9IGdldFVTRFByaWNlKHN0YWJsZWNvaW4pO1xuICAgIHJldHVybiBjcnlwdG9VU0RQcmljZSAvIHN0YWJsZWNvaW5VU0RQcmljZTtcbiAgfVxufTtcblxuLy8gUmVhbC10aW1lIHByaWNlIGNhY2hlIC0gc2hvcnRlciBkdXJhdGlvbiBmb3IgbW9yZSBmcmVxdWVudCB1cGRhdGVzXG5sZXQgcHJpY2VDYWNoZTogeyBba2V5OiBzdHJpbmddOiBudW1iZXIgfSA9IHt9O1xubGV0IGxhc3RQcmljZVVwZGF0ZSA9IDA7XG5jb25zdCBQUklDRV9DQUNIRV9EVVJBVElPTiA9IDUwMDA7IC8vIDUgc2Vjb25kcyBjYWNoZSBmb3IgcmVhbC10aW1lIHVwZGF0ZXNcblxuLy8gVGVsZWdyYW0gbm90aWZpY2F0aW9uIHF1ZXVlIGZvciBvZmZsaW5lIHBlcmlvZHNcbmxldCB0ZWxlZ3JhbU5vdGlmaWNhdGlvblF1ZXVlOiBBcnJheTx7IG1lc3NhZ2U6IHN0cmluZzsgdGltZXN0YW1wOiBudW1iZXI7IGlzRXJyb3I6IGJvb2xlYW4gfT4gPSBbXTtcbmNvbnN0IE1BWF9RVUVVRV9TSVpFID0gMTA7IC8vIExpbWl0IHF1ZXVlIHNpemUgdG8gcHJldmVudCBtZW1vcnkgaXNzdWVzXG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgcmVhbC10aW1lIFVTRCBwcmljZSBmcm9tIEFQSSB3aXRoIGZhbGxiYWNrIChhc3luYyB2ZXJzaW9uKVxuY29uc3QgZ2V0UmVhbFRpbWVVU0RQcmljZSA9IGFzeW5jIChjcnlwdG86IHN0cmluZyk6IFByb21pc2U8bnVtYmVyPiA9PiB7XG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIGNvbnN0IGNhY2hlS2V5ID0gY3J5cHRvLnRvVXBwZXJDYXNlKCk7XG5cbiAgLy8gUmV0dXJuIGNhY2hlZCBwcmljZSBpZiBzdGlsbCB2YWxpZFxuICBpZiAocHJpY2VDYWNoZVtjYWNoZUtleV0gJiYgKG5vdyAtIGxhc3RQcmljZVVwZGF0ZSkgPCBQUklDRV9DQUNIRV9EVVJBVElPTikge1xuICAgIHJldHVybiBwcmljZUNhY2hlW2NhY2hlS2V5XTtcbiAgfVxuXG4gIHRyeSB7XG4gICAgLy8gVHJ5IHRvIGZldGNoIHJlYWwtdGltZSBwcmljZSBmcm9tIENvaW5HZWNrbyBBUElcbiAgICBjb25zdCBjb2luR2Vja29JZCA9IGdldENvaW5HZWNrb0lkKGNyeXB0byk7XG4gICAgaWYgKGNvaW5HZWNrb0lkKSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS5jb2luZ2Vja28uY29tL2FwaS92My9zaW1wbGUvcHJpY2U/aWRzPSR7Y29pbkdlY2tvSWR9JnZzX2N1cnJlbmNpZXM9dXNkYCk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgY29uc3QgcHJpY2UgPSBkYXRhW2NvaW5HZWNrb0lkXT8udXNkO1xuICAgICAgICBpZiAocHJpY2UgJiYgcHJpY2UgPiAwKSB7XG4gICAgICAgICAgcHJpY2VDYWNoZVtjYWNoZUtleV0gPSBwcmljZTtcbiAgICAgICAgICBsYXN0UHJpY2VVcGRhdGUgPSBub3c7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCfk4ogUmVhbC10aW1lIHByaWNlIGZldGNoZWQgZm9yICR7Y3J5cHRvfTogJCR7cHJpY2V9YCk7XG4gICAgICAgICAgcmV0dXJuIHByaWNlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUud2Fybihg4pqg77iPIEZhaWxlZCB0byBmZXRjaCByZWFsLXRpbWUgcHJpY2UgZm9yICR7Y3J5cHRvfSwgdXNpbmcgZmFsbGJhY2s6YCwgZXJyb3IpO1xuICB9XG5cbiAgLy8gRmFsbGJhY2sgdG8gc3RhdGljIHByaWNlcyBpZiBBUEkgZmFpbHNcbiAgcmV0dXJuIGdldFVTRFByaWNlKGNyeXB0byk7XG59O1xuXG4vLyBTeW5jaHJvbm91cyBoZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IFVTRCBwcmljZSAodXNlcyBjYWNoZWQgdmFsdWVzIG9yIHN0YXRpYyBmYWxsYmFjaylcbmNvbnN0IGdldFVTRFByaWNlID0gKGNyeXB0bzogc3RyaW5nKTogbnVtYmVyID0+IHtcbiAgY29uc3QgY2FjaGVLZXkgPSBjcnlwdG8udG9VcHBlckNhc2UoKTtcblxuICAvLyBSZXR1cm4gY2FjaGVkIHByaWNlIGlmIGF2YWlsYWJsZVxuICBpZiAocHJpY2VDYWNoZVtjYWNoZUtleV0pIHtcbiAgICByZXR1cm4gcHJpY2VDYWNoZVtjYWNoZUtleV07XG4gIH1cblxuICAvLyBGYWxsYmFjayB0byBzdGF0aWMgcHJpY2VzXG4gIHJldHVybiBnZXRTdGF0aWNVU0RQcmljZShjcnlwdG8pO1xufTtcblxuLy8gU3RhdGljIGZhbGxiYWNrIHByaWNlcyAodXNlZCB3aGVuIEFQSSBpcyB1bmF2YWlsYWJsZSlcbmNvbnN0IGdldFN0YXRpY1VTRFByaWNlID0gKGNyeXB0bzogc3RyaW5nKTogbnVtYmVyID0+IHtcbiAgY29uc3QgdXNkUHJpY2VzOiB7IFtrZXk6IHN0cmluZ106IG51bWJlciB9ID0ge1xuICAgIC8vIE1ham9yIGNyeXB0b2N1cnJlbmNpZXMgLSBVcGRhdGVkIHRvIGN1cnJlbnQgbWFya2V0IHByaWNlc1xuICAgICdCVEMnOiAxMDYwMDAsICAvLyBVcGRhdGVkIHRvIG1hdGNoIHlvdXIgZXhhbXBsZVxuICAgICdFVEgnOiAyNTAwLCAgICAvLyBVcGRhdGVkIHRvIG1hdGNoIHlvdXIgZXhhbXBsZVxuICAgICdTT0wnOiAxODAsICAgICAvLyBVcGRhdGVkIHRvIGN1cnJlbnQgcHJpY2VcbiAgICAnQURBJzogMC44NSwgICAvLyBVcGRhdGVkXG4gICAgJ0RPR0UnOiAwLjMyLCAgLy8gVXBkYXRlZFxuICAgICdMSU5LJzogMjIsICAgIC8vIFVwZGF0ZWRcbiAgICAnTUFUSUMnOiAwLjQyLCAvLyBVcGRhdGVkXG4gICAgJ0RPVCc6IDYuNSwgICAgLy8gVXBkYXRlZFxuICAgICdBVkFYJzogMzgsICAgIC8vIFVwZGF0ZWRcbiAgICAnU0hJQic6IDAuMDAwMDIyLCAvLyBVcGRhdGVkXG4gICAgJ1hSUCc6IDIuMSwgICAgLy8gVXBkYXRlZFxuICAgICdMVEMnOiA5NSwgICAgIC8vIFVwZGF0ZWRcbiAgICAnQkNIJzogNDIwLCAgICAvLyBVcGRhdGVkXG5cbiAgICAvLyBEZUZpIHRva2Vuc1xuICAgICdVTkknOiAxNSxcbiAgICAnQUFWRSc6IDE4MCxcbiAgICAnTUtSJzogMTgwMCxcbiAgICAnU05YJzogMy41LFxuICAgICdDT01QJzogODUsXG4gICAgJ1lGSSc6IDg1MDAsXG4gICAgJ1NVU0hJJzogMi4xLFxuICAgICcxSU5DSCc6IDAuNjUsXG4gICAgJ0NSVic6IDAuODUsXG4gICAgJ1VNQSc6IDMuMixcblxuICAgIC8vIExheWVyIDEgYmxvY2tjaGFpbnNcbiAgICAnQVRPTSc6IDEyLFxuICAgICdORUFSJzogNi41LFxuICAgICdBTEdPJzogMC4zNSxcbiAgICAnSUNQJzogMTQsXG4gICAgJ0hCQVInOiAwLjI4LFxuICAgICdBUFQnOiAxMi41LFxuICAgICdUT04nOiA1LjgsXG4gICAgJ0ZUTSc6IDAuOTUsXG4gICAgJ09ORSc6IDAuMDI1LFxuXG4gICAgLy8gT3RoZXIgcG9wdWxhciB0b2tlbnNcbiAgICAnRklMJzogOC41LFxuICAgICdUUlgnOiAwLjI1LFxuICAgICdFVEMnOiAzNSxcbiAgICAnVkVUJzogMC4wNTUsXG4gICAgJ1FOVCc6IDEyNSxcbiAgICAnTERPJzogMi44LFxuICAgICdDUk8nOiAwLjE4LFxuICAgICdMVU5DJzogMC4wMDAxNSxcblxuICAgIC8vIEdhbWluZyAmIE1ldGF2ZXJzZVxuICAgICdNQU5BJzogMC44NSxcbiAgICAnU0FORCc6IDAuNzUsXG4gICAgJ0FYUyc6IDguNSxcbiAgICAnRU5KJzogMC40NSxcbiAgICAnQ0haJzogMC4xMixcblxuICAgIC8vIEluZnJhc3RydWN0dXJlICYgVXRpbGl0eVxuICAgICdUSEVUQSc6IDIuMSxcbiAgICAnRkxPVyc6IDEuMixcbiAgICAnWFRaJzogMS44LFxuICAgICdFT1MnOiAxLjEsXG4gICAgJ0dSVCc6IDAuMjgsXG4gICAgJ0JBVCc6IDAuMzUsXG5cbiAgICAvLyBQcml2YWN5IGNvaW5zXG4gICAgJ1pFQyc6IDQ1LFxuICAgICdEQVNIJzogMzUsXG5cbiAgICAvLyBERVggdG9rZW5zXG4gICAgJ0xSQyc6IDAuNDUsXG4gICAgJ1pSWCc6IDAuNjUsXG4gICAgJ0tOQyc6IDAuODUsXG5cbiAgICAvLyBPdGhlciB0b2tlbnNcbiAgICAnUkVOJzogMC4xNSxcbiAgICAnQkFORCc6IDIuNSxcbiAgICAnU1RPUkonOiAwLjg1LFxuICAgICdOTVInOiAyNSxcbiAgICAnQU5UJzogOC41LFxuICAgICdCTlQnOiAwLjk1LFxuICAgICdNTE4nOiAzNSxcbiAgICAnUkVQJzogMTUsXG5cbiAgICAvLyBTbWFsbGVyIGNhcCB0b2tlbnNcbiAgICAnSU9UWCc6IDAuMDY1LFxuICAgICdaSUwnOiAwLjA0NSxcbiAgICAnSUNYJzogMC4zNSxcbiAgICAnUVRVTSc6IDQuNSxcbiAgICAnT05UJzogMC40NSxcbiAgICAnV0FWRVMnOiAzLjIsXG4gICAgJ0xTSyc6IDEuOCxcbiAgICAnTkFOTyc6IDEuNSxcbiAgICAnU0MnOiAwLjAwOCxcbiAgICAnREdCJzogMC4wMjUsXG4gICAgJ1JWTic6IDAuMDM1LFxuICAgICdCVFQnOiAwLjAwMDAwMTUsXG4gICAgJ1dJTic6IDAuMDAwMTUsXG4gICAgJ0hPVCc6IDAuMDAzNSxcbiAgICAnREVOVCc6IDAuMDAxOCxcbiAgICAnTlBYUyc6IDAuMDAwODUsXG4gICAgJ0ZVTic6IDAuMDA4NSxcbiAgICAnQ0VMUic6IDAuMDI1LFxuXG4gICAgLy8gU3RhYmxlY29pbnNcbiAgICAnVVNEVCc6IDEuMCxcbiAgICAnVVNEQyc6IDEuMCxcbiAgICAnRkRVU0QnOiAxLjAsXG4gICAgJ0JVU0QnOiAxLjAsXG4gICAgJ0RBSSc6IDEuMFxuICB9O1xuICByZXR1cm4gdXNkUHJpY2VzW2NyeXB0by50b1VwcGVyQ2FzZSgpXSB8fCAxMDA7XG59O1xuXG4vLyBFbmhhbmNlZCBmYWxsYmFjayBmdW5jdGlvbiBmb3IgbWFya2V0IHByaWNlIGNhbGN1bGF0aW9uIHN1cHBvcnRpbmcgYWxsIHRyYWRpbmcgcGFpcnNcbmNvbnN0IGNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UgPSAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogbnVtYmVyID0+IHtcbiAgY29uc3QgY3J5cHRvMVVTRFByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzEpO1xuICBjb25zdCBjcnlwdG8yVVNEUHJpY2UgPSBnZXRVU0RQcmljZShjb25maWcuY3J5cHRvMik7XG5cbiAgLy8gQ2FsY3VsYXRlIHRoZSByYXRpbzogaG93IG1hbnkgdW5pdHMgb2YgY3J5cHRvMiA9IDEgdW5pdCBvZiBjcnlwdG8xXG4gIGNvbnN0IGJhc2VQcmljZSA9IGNyeXB0bzFVU0RQcmljZSAvIGNyeXB0bzJVU0RQcmljZTtcblxuICAvLyBBZGQgc21hbGwgcmFuZG9tIGZsdWN0dWF0aW9uXG4gIGNvbnN0IGZsdWN0dWF0aW9uID0gKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMC4wMjsgLy8gwrExJVxuICBjb25zdCBmaW5hbFByaWNlID0gYmFzZVByaWNlICogKDEgKyBmbHVjdHVhdGlvbik7XG5cbiAgY29uc29sZS5sb2coYPCfk4ogRmFsbGJhY2sgcHJpY2UgY2FsY3VsYXRpb246ICR7Y29uZmlnLmNyeXB0bzF9ICgkJHtjcnlwdG8xVVNEUHJpY2V9KSAvICR7Y29uZmlnLmNyeXB0bzJ9ICgkJHtjcnlwdG8yVVNEUHJpY2V9KSA9ICR7ZmluYWxQcmljZS50b0ZpeGVkKDYpfWApO1xuXG4gIHJldHVybiBmaW5hbFByaWNlO1xufTtcblxudHlwZSBUcmFkaW5nQWN0aW9uID1cbiAgfCB7IHR5cGU6ICdTRVRfQ09ORklHJzsgcGF5bG9hZDogUGFydGlhbDxUcmFkaW5nQ29uZmlnPiB9XG4gIHwgeyB0eXBlOiAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3dbXSB9XG4gIHwgeyB0eXBlOiAnQUREX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBUYXJnZXRQcmljZVJvdyB9XG4gIHwgeyB0eXBlOiAnVVBEQVRFX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBUYXJnZXRQcmljZVJvdyB9XG4gIHwgeyB0eXBlOiAnUkVNT1ZFX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBzdHJpbmcgfVxuICB8IHsgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJzsgcGF5bG9hZDogT3JkZXJIaXN0b3J5RW50cnkgfVxuICB8IHsgdHlwZTogJ1NFVF9BUFBfU0VUVElOR1MnOyBwYXlsb2FkOiBQYXJ0aWFsPEFwcFNldHRpbmdzPiB9XG4gIHwgeyB0eXBlOiAnU0VUX01BUktFVF9QUklDRSc7IHBheWxvYWQ6IG51bWJlciB9XG4gIC8vIHwgeyB0eXBlOiAnU0VUX0JPVF9TVEFUVVMnOyBwYXlsb2FkOiBib29sZWFuIH0gLy8gUmVtb3ZlZFxuICB8IHsgdHlwZTogJ1VQREFURV9CQUxBTkNFUyc7IHBheWxvYWQ6IHsgY3J5cHRvMT86IG51bWJlcjsgY3J5cHRvMj86IG51bWJlcjsgc3RhYmxlY29pbj86IG51bWJlciB9IH1cbiAgfCB7IHR5cGU6ICdTRVRfQkFMQU5DRVMnOyBwYXlsb2FkOiB7IGNyeXB0bzE6IG51bWJlcjsgY3J5cHRvMjogbnVtYmVyOyBzdGFibGVjb2luPzogbnVtYmVyIH0gfVxuICB8IHsgdHlwZTogJ1VQREFURV9TVEFCTEVDT0lOX0JBTEFOQ0UnOyBwYXlsb2FkOiBudW1iZXIgfVxuICB8IHsgdHlwZTogJ1JFU0VUX1NFU1NJT04nIH1cbiAgfCB7IHR5cGU6ICdDTEVBUl9PUkRFUl9ISVNUT1JZJyB9XG4gIHwgeyB0eXBlOiAnU0VUX0JBQ0tFTkRfU1RBVFVTJzsgcGF5bG9hZDogJ29ubGluZScgfCAnb2ZmbGluZScgfCAndW5rbm93bicgfVxuICB8IHsgdHlwZTogJ1NZU1RFTV9TVEFSVF9CT1RfSU5JVElBVEUnIH1cbiAgfCB7IHR5cGU6ICdTWVNURU1fQ09NUExFVEVfV0FSTVVQJyB9XG4gIHwgeyB0eXBlOiAnU1lTVEVNX1NUT1BfQk9UJyB9XG4gIHwgeyB0eXBlOiAnU1lTVEVNX1JFU0VUX0JPVCcgfVxuICB8IHsgdHlwZTogJ1NFVF9UQVJHRVRfUFJJQ0VfUk9XUyc7IHBheWxvYWQ6IFRhcmdldFByaWNlUm93W10gfVxuICB8IHsgdHlwZTogJ1JFU0VUX0ZPUl9ORVdfQ1JZUFRPJyB9O1xuXG5jb25zdCBpbml0aWFsQmFzZUNvbmZpZzogVHJhZGluZ0NvbmZpZyA9IHtcbiAgdHJhZGluZ01vZGU6IFwiU2ltcGxlU3BvdFwiLFxuICBjcnlwdG8xOiBcIlwiLCAvLyBTdGFydCB3aXRoIGVtcHR5IGNyeXB0bzFcbiAgY3J5cHRvMjogXCJcIiwgLy8gU3RhcnQgd2l0aCBlbXB0eSBjcnlwdG8yXG4gIGJhc2VCaWQ6IDEwMCxcbiAgbXVsdGlwbGllcjogMS4wMDUsXG4gIG51bURpZ2l0czogNCxcbiAgc2xpcHBhZ2VQZXJjZW50OiAwLjIsIC8vIERlZmF1bHQgMC4yJSBzbGlwcGFnZSByYW5nZVxuICBpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50OiA1MCxcbiAgaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudDogNTAsXG4gIHByZWZlcnJlZFN0YWJsZWNvaW46IEFWQUlMQUJMRV9TVEFCTEVDT0lOU1swXSxcbn07XG5cbmNvbnN0IGluaXRpYWxUcmFkaW5nU3RhdGU6IFRyYWRpbmdTdGF0ZSA9IHtcbiAgY29uZmlnOiBpbml0aWFsQmFzZUNvbmZpZyxcbiAgdGFyZ2V0UHJpY2VSb3dzOiBbXSxcbiAgb3JkZXJIaXN0b3J5OiBbXSxcbiAgYXBwU2V0dGluZ3M6IERFRkFVTFRfQVBQX1NFVFRJTkdTLFxuICBjdXJyZW50TWFya2V0UHJpY2U6IGNhbGN1bGF0ZUluaXRpYWxNYXJrZXRQcmljZShpbml0aWFsQmFzZUNvbmZpZyksXG4gIC8vIGlzQm90QWN0aXZlOiBmYWxzZSwgLy8gUmVtb3ZlXG4gIGJvdFN5c3RlbVN0YXR1czogJ1N0b3BwZWQnLCAvLyBJbml0aWFsaXplIGFzIFN0b3BwZWRcbiAgY3J5cHRvMUJhbGFuY2U6IDEwLCAvLyBNb2NrIGluaXRpYWwgYmFsYW5jZVxuICBjcnlwdG8yQmFsYW5jZTogMTAwMDAwLCAvLyBJbmNyZWFzZWQgYmFsYW5jZSBmb3IgQlRDL1VTRFQgdHJhZGluZyAod2FzIDEwMDAwKVxuICBzdGFibGVjb2luQmFsYW5jZTogMCxcbiAgYmFja2VuZFN0YXR1czogJ3Vua25vd24nLFxufTtcblxuY29uc3QgbGFzdEFjdGlvblRpbWVzdGFtcFBlckNvdW50ZXIgPSBuZXcgTWFwPG51bWJlciwgbnVtYmVyPigpO1xuXG4vLyBMb2NhbFN0b3JhZ2UgcGVyc2lzdGVuY2UgZnVuY3Rpb25zXG5jb25zdCBTVE9SQUdFX0tFWSA9ICdwbHV0b190cmFkaW5nX3N0YXRlJztcblxuY29uc3Qgc2F2ZVN0YXRlVG9Mb2NhbFN0b3JhZ2UgPSAoc3RhdGU6IFRyYWRpbmdTdGF0ZSkgPT4ge1xuICB0cnkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3Qgc3RhdGVUb1NhdmUgPSB7XG4gICAgICAgIGNvbmZpZzogc3RhdGUuY29uZmlnLFxuICAgICAgICB0YXJnZXRQcmljZVJvd3M6IHN0YXRlLnRhcmdldFByaWNlUm93cyxcbiAgICAgICAgb3JkZXJIaXN0b3J5OiBzdGF0ZS5vcmRlckhpc3RvcnksXG4gICAgICAgIGFwcFNldHRpbmdzOiBzdGF0ZS5hcHBTZXR0aW5ncyxcbiAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgIGNyeXB0bzFCYWxhbmNlOiBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgY3J5cHRvMkJhbGFuY2U6IHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgIGJvdFN5c3RlbVN0YXR1czogc3RhdGUuYm90U3lzdGVtU3RhdHVzLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH07XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTVE9SQUdFX0tFWSwgSlNPTi5zdHJpbmdpZnkoc3RhdGVUb1NhdmUpKTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNhdmUgc3RhdGUgdG8gbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcbiAgfVxufTtcblxuY29uc3QgbG9hZFN0YXRlRnJvbUxvY2FsU3RvcmFnZSA9ICgpOiBQYXJ0aWFsPFRyYWRpbmdTdGF0ZT4gfCBudWxsID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IHNhdmVkU3RhdGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTVE9SQUdFX0tFWSk7XG4gICAgICBpZiAoc2F2ZWRTdGF0ZSkge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHNhdmVkU3RhdGUpO1xuICAgICAgICAvLyBDaGVjayBpZiBzdGF0ZSBpcyBub3QgdG9vIG9sZCAoMjQgaG91cnMpXG4gICAgICAgIGlmIChwYXJzZWQudGltZXN0YW1wICYmIChEYXRlLm5vdygpIC0gcGFyc2VkLnRpbWVzdGFtcCkgPCAyNCAqIDYwICogNjAgKiAxMDAwKSB7XG4gICAgICAgICAgcmV0dXJuIHBhcnNlZDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZTonLCBlcnJvcik7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59O1xuXG5jb25zdCB0cmFkaW5nUmVkdWNlciA9IChzdGF0ZTogVHJhZGluZ1N0YXRlLCBhY3Rpb246IFRyYWRpbmdBY3Rpb24pOiBUcmFkaW5nU3RhdGUgPT4ge1xuICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgY2FzZSAnU0VUX0NPTkZJRyc6XG4gICAgICBjb25zdCBuZXdDb25maWcgPSB7IC4uLnN0YXRlLmNvbmZpZywgLi4uYWN0aW9uLnBheWxvYWQgfTtcbiAgICAgIC8vIElmIHRyYWRpbmcgcGFpciBjaGFuZ2VzLCByZXNldCBtYXJrZXQgcHJpY2UgKGl0IHdpbGwgYmUgcmUtY2FsY3VsYXRlZCBieSBlZmZlY3QpXG4gICAgICBpZiAoYWN0aW9uLnBheWxvYWQuY3J5cHRvMSB8fCBhY3Rpb24ucGF5bG9hZC5jcnlwdG8yKSB7XG4gICAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBjb25maWc6IG5ld0NvbmZpZywgY3VycmVudE1hcmtldFByaWNlOiBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UobmV3Q29uZmlnKSB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGNvbmZpZzogbmV3Q29uZmlnIH07XG4gICAgY2FzZSAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCB0YXJnZXRQcmljZVJvd3M6IGFjdGlvbi5wYXlsb2FkLnNvcnQoKGE6IFRhcmdldFByaWNlUm93LCBiOiBUYXJnZXRQcmljZVJvdykgPT4gYS50YXJnZXRQcmljZSAtIGIudGFyZ2V0UHJpY2UpLm1hcCgocm93OiBUYXJnZXRQcmljZVJvdywgaW5kZXg6IG51bWJlcikgPT4gKHsuLi5yb3csIGNvdW50ZXI6IGluZGV4ICsgMX0pKSB9O1xuICAgIGNhc2UgJ0FERF9UQVJHRVRfUFJJQ0VfUk9XJzoge1xuICAgICAgY29uc3QgbmV3Um93cyA9IFsuLi5zdGF0ZS50YXJnZXRQcmljZVJvd3MsIGFjdGlvbi5wYXlsb2FkXS5zb3J0KChhOiBUYXJnZXRQcmljZVJvdywgYjogVGFyZ2V0UHJpY2VSb3cpID0+IGEudGFyZ2V0UHJpY2UgLSBiLnRhcmdldFByaWNlKS5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3csIGluZGV4OiBudW1iZXIpID0+ICh7Li4ucm93LCBjb3VudGVyOiBpbmRleCArIDF9KSk7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgdGFyZ2V0UHJpY2VSb3dzOiBuZXdSb3dzIH07XG4gICAgfVxuICAgIGNhc2UgJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJzoge1xuICAgICAgY29uc3QgdXBkYXRlZFJvd3MgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MubWFwKChyb3c6IFRhcmdldFByaWNlUm93KSA9PlxuICAgICAgICByb3cuaWQgPT09IGFjdGlvbi5wYXlsb2FkLmlkID8gYWN0aW9uLnBheWxvYWQgOiByb3dcbiAgICAgICkuc29ydCgoYTogVGFyZ2V0UHJpY2VSb3csIGI6IFRhcmdldFByaWNlUm93KSA9PiBhLnRhcmdldFByaWNlIC0gYi50YXJnZXRQcmljZSkubWFwKChyb3c6IFRhcmdldFByaWNlUm93LCBpbmRleDogbnVtYmVyKSA9PiAoey4uLnJvdywgY291bnRlcjogaW5kZXggKyAxfSkpO1xuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIHRhcmdldFByaWNlUm93czogdXBkYXRlZFJvd3MgfTtcbiAgICB9XG4gICAgY2FzZSAnUkVNT1ZFX1RBUkdFVF9QUklDRV9ST1cnOiB7XG4gICAgICBjb25zdCBmaWx0ZXJlZFJvd3MgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MuZmlsdGVyKChyb3c6IFRhcmdldFByaWNlUm93KSA9PiByb3cuaWQgIT09IGFjdGlvbi5wYXlsb2FkKS5zb3J0KChhOiBUYXJnZXRQcmljZVJvdywgYjogVGFyZ2V0UHJpY2VSb3cpID0+IGEudGFyZ2V0UHJpY2UgLSBiLnRhcmdldFByaWNlKS5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3csIGluZGV4OiBudW1iZXIpID0+ICh7Li4ucm93LCBjb3VudGVyOiBpbmRleCArIDF9KSk7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgdGFyZ2V0UHJpY2VSb3dzOiBmaWx0ZXJlZFJvd3MgfTtcbiAgICB9XG4gICAgY2FzZSAnQUREX09SREVSX0hJU1RPUllfRU5UUlknOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIG9yZGVySGlzdG9yeTogW2FjdGlvbi5wYXlsb2FkLCAuLi5zdGF0ZS5vcmRlckhpc3RvcnldIH07XG4gICAgY2FzZSAnQ0xFQVJfT1JERVJfSElTVE9SWSc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgb3JkZXJIaXN0b3J5OiBbXSB9O1xuICAgIGNhc2UgJ1NFVF9BUFBfU0VUVElOR1MnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGFwcFNldHRpbmdzOiB7IC4uLnN0YXRlLmFwcFNldHRpbmdzLCAuLi5hY3Rpb24ucGF5bG9hZCB9IH07XG4gICAgY2FzZSAnU0VUX01BUktFVF9QUklDRSc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgY3VycmVudE1hcmtldFByaWNlOiBhY3Rpb24ucGF5bG9hZCB9O1xuICAgIC8vIGNhc2UgJ1NFVF9CT1RfU1RBVFVTJzogLy8gUmVtb3ZlZFxuICAgIGNhc2UgJ1VQREFURV9CQUxBTkNFUyc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgY3J5cHRvMUJhbGFuY2U6IGFjdGlvbi5wYXlsb2FkLmNyeXB0bzEgIT09IHVuZGVmaW5lZCA/IGFjdGlvbi5wYXlsb2FkLmNyeXB0bzEgOiBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgY3J5cHRvMkJhbGFuY2U6IGFjdGlvbi5wYXlsb2FkLmNyeXB0bzIgIT09IHVuZGVmaW5lZCA/IGFjdGlvbi5wYXlsb2FkLmNyeXB0bzIgOiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IGFjdGlvbi5wYXlsb2FkLnN0YWJsZWNvaW4gIT09IHVuZGVmaW5lZCA/IGFjdGlvbi5wYXlsb2FkLnN0YWJsZWNvaW4gOiBzdGF0ZS5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgIH07XG4gICAgY2FzZSAnU0VUX0JBTEFOQ0VTJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBjcnlwdG8xQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuY3J5cHRvMSxcbiAgICAgICAgY3J5cHRvMkJhbGFuY2U6IGFjdGlvbi5wYXlsb2FkLmNyeXB0bzIsXG4gICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBhY3Rpb24ucGF5bG9hZC5zdGFibGVjb2luICE9PSB1bmRlZmluZWQgPyBhY3Rpb24ucGF5bG9hZC5zdGFibGVjb2luIDogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICB9O1xuICAgIGNhc2UgJ1VQREFURV9TVEFCTEVDT0lOX0JBTEFOQ0UnOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBhY3Rpb24ucGF5bG9hZCxcbiAgICAgIH07XG4gICAgY2FzZSAnUkVTRVRfU0VTU0lPTic6XG4gICAgICBjb25zdCBjb25maWdGb3JSZXNldCA9IHsgLi4uc3RhdGUuY29uZmlnIH07XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5pbml0aWFsVHJhZGluZ1N0YXRlLFxuICAgICAgICBjb25maWc6IGNvbmZpZ0ZvclJlc2V0LFxuICAgICAgICBhcHBTZXR0aW5nczogeyAuLi5zdGF0ZS5hcHBTZXR0aW5ncyB9LFxuICAgICAgICBjdXJyZW50TWFya2V0UHJpY2U6IGNhbGN1bGF0ZUluaXRpYWxNYXJrZXRQcmljZShjb25maWdGb3JSZXNldCksXG4gICAgICAgIC8vIFByZXNlcnZlIGN1cnJlbnQgYmFsYW5jZXMgaW5zdGVhZCBvZiByZXNldHRpbmcgdG8gaW5pdGlhbCB2YWx1ZXNcbiAgICAgICAgY3J5cHRvMUJhbGFuY2U6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogc3RhdGUuY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBzdGF0ZS5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgIH07XG4gICAgY2FzZSAnU0VUX0JBQ0tFTkRfU1RBVFVTJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBiYWNrZW5kU3RhdHVzOiBhY3Rpb24ucGF5bG9hZCB9O1xuICAgIGNhc2UgJ1NZU1RFTV9TVEFSVF9CT1RfSU5JVElBVEUnOlxuICAgICAgLy8gQ29udGludWUgZnJvbSBwcmV2aW91cyBzdGF0ZSBpbnN0ZWFkIG9mIHJlc2V0dGluZ1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIGJvdFN5c3RlbVN0YXR1czogJ1dhcm1pbmdVcCcsXG4gICAgICAgIC8vIEtlZXAgZXhpc3RpbmcgdGFyZ2V0UHJpY2VSb3dzIGFuZCBvcmRlckhpc3RvcnkgLSBkb24ndCByZXNldFxuICAgICAgfTtcbiAgICBjYXNlICdTWVNURU1fQ09NUExFVEVfV0FSTVVQJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBib3RTeXN0ZW1TdGF0dXM6ICdSdW5uaW5nJyB9O1xuICAgIGNhc2UgJ1NZU1RFTV9TVE9QX0JPVCc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgYm90U3lzdGVtU3RhdHVzOiAnU3RvcHBlZCcgfTtcbiAgICBjYXNlICdTWVNURU1fUkVTRVRfQk9UJzpcbiAgICAgIC8vIEZyZXNoIFN0YXJ0OiBDbGVhciBhbGwgdGFyZ2V0IHByaWNlIHJvd3MgYW5kIG9yZGVyIGhpc3RvcnkgY29tcGxldGVseVxuICAgICAgbGFzdEFjdGlvblRpbWVzdGFtcFBlckNvdW50ZXIuY2xlYXIoKTtcbiAgICAgIC8vIENsZWFyIGN1cnJlbnQgc2Vzc2lvbiB0byBmb3JjZSBuZXcgc2Vzc2lvbiBuYW1lIGdlbmVyYXRpb25cbiAgICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICAgIHNlc3Npb25NYW5hZ2VyLmNsZWFyQ3VycmVudFNlc3Npb24oKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBib3RTeXN0ZW1TdGF0dXM6ICdTdG9wcGVkJyxcbiAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBbXSwgLy8gQ29tcGxldGVseSBjbGVhciBhbGwgdGFyZ2V0IHByaWNlIHJvd3NcbiAgICAgICAgb3JkZXJIaXN0b3J5OiBbXSxcbiAgICAgIH07XG4gICAgY2FzZSAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCB0YXJnZXRQcmljZVJvd3M6IGFjdGlvbi5wYXlsb2FkIH07XG4gICAgY2FzZSAnUkVTRVRfRk9SX05FV19DUllQVE8nOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uaW5pdGlhbFRyYWRpbmdTdGF0ZSxcbiAgICAgICAgY29uZmlnOiBzdGF0ZS5jb25maWcsIC8vIEtlZXAgdGhlIG5ldyBjcnlwdG8gY29uZmlndXJhdGlvblxuICAgICAgICBiYWNrZW5kU3RhdHVzOiBzdGF0ZS5iYWNrZW5kU3RhdHVzLCAvLyBQcmVzZXJ2ZSBiYWNrZW5kIHN0YXR1c1xuICAgICAgICBib3RTeXN0ZW1TdGF0dXM6ICdTdG9wcGVkJywgLy8gRW5zdXJlIGJvdCBpcyBzdG9wcGVkXG4gICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogMCwgLy8gUmVzZXQgbWFya2V0IHByaWNlIGZvciBuZXcgY3J5cHRvXG4gICAgICAgIC8vIFByZXNlcnZlIGN1cnJlbnQgYmFsYW5jZXMgaW5zdGVhZCBvZiByZXNldHRpbmcgdG8gaW5pdGlhbCB2YWx1ZXNcbiAgICAgICAgY3J5cHRvMUJhbGFuY2U6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogc3RhdGUuY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBzdGF0ZS5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgIH07XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiBzdGF0ZTtcbiAgfVxufTtcblxuaW50ZXJmYWNlIFRyYWRpbmdDb250ZXh0VHlwZSBleHRlbmRzIE9taXQ8VHJhZGluZ1N0YXRlLCAnYmFja2VuZFN0YXR1cycgfCAnYm90U3lzdGVtU3RhdHVzJz4ge1xuICBkaXNwYXRjaDogUmVhY3QuRGlzcGF0Y2g8VHJhZGluZ0FjdGlvbj47XG4gIHNldFRhcmdldFByaWNlczogKHByaWNlczogbnVtYmVyW10pID0+IHZvaWQ7XG4gIGdldERpc3BsYXlPcmRlcnM6ICgpID0+IERpc3BsYXlPcmRlclJvd1tdO1xuICBiYWNrZW5kU3RhdHVzOiAnb25saW5lJyB8ICdvZmZsaW5lJyB8ICd1bmtub3duJztcbiAgZmV0Y2hNYXJrZXRQcmljZTogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgY2hlY2tCYWNrZW5kU3RhdHVzOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBib3RTeXN0ZW1TdGF0dXM6IEJvdFN5c3RlbVN0YXR1cztcbiAgaXNCb3RBY3RpdmU6IGJvb2xlYW47IC8vIERlcml2ZWQgZnJvbSBib3RTeXN0ZW1TdGF0dXNcbiAgc3RhcnRCYWNrZW5kQm90OiAoY29uZmlnSWQ6IHN0cmluZykgPT4gUHJvbWlzZTxib29sZWFuPjtcbiAgc3RvcEJhY2tlbmRCb3Q6IChjb25maWdJZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xuICBzYXZlQ29uZmlnVG9CYWNrZW5kOiAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKSA9PiBQcm9taXNlPHN0cmluZyB8IG51bGw+O1xuICBzYXZlQ3VycmVudFNlc3Npb246ICgpID0+IFByb21pc2U8Ym9vbGVhbj47XG59XG5cbmNvbnN0IFRyYWRpbmdDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUcmFkaW5nQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbi8vIFNJTVBMSUZJRUQgTE9HSUMgLSBOTyBDT01QTEVYIENPT0xET1dOU1xuXG5leHBvcnQgY29uc3QgVHJhZGluZ1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pID0+IHtcbiAgLy8gSW5pdGlhbGl6ZSBzdGF0ZSB3aXRoIGxvY2FsU3RvcmFnZSBkYXRhIGlmIGF2YWlsYWJsZVxuICBjb25zdCBpbml0aWFsaXplU3RhdGUgPSAoKSA9PiB7XG4gICAgLy8gQ2hlY2sgaWYgdGhpcyBpcyBhIG5ldyBzZXNzaW9uIGZyb20gVVJMIHBhcmFtZXRlclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgdXJsUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKTtcbiAgICAgIGNvbnN0IGlzTmV3U2Vzc2lvbiA9IHVybFBhcmFtcy5nZXQoJ25ld1Nlc3Npb24nKSA9PT0gJ3RydWUnO1xuXG4gICAgICBpZiAoaXNOZXdTZXNzaW9uKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4aVIE5ldyBzZXNzaW9uIHJlcXVlc3RlZCAtIHN0YXJ0aW5nIHdpdGggY29tcGxldGVseSBmcmVzaCBzdGF0ZScpO1xuICAgICAgICAvLyBOb3RlOiBVUkwgcGFyYW1ldGVyIHdpbGwgYmUgY2xlYXJlZCBpbiB1c2VFZmZlY3QgdG8gYXZvaWQgcmVuZGVyLXRpbWUgc3RhdGUgdXBkYXRlc1xuICAgICAgICByZXR1cm4gaW5pdGlhbFRyYWRpbmdTdGF0ZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgbmV3IHdpbmRvdy90YWIgYnkgY2hlY2tpbmcgaWYgd2UgaGF2ZSBhIGN1cnJlbnQgc2Vzc2lvblxuICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICBjb25zdCBjdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpO1xuXG4gICAgLy8gSWYgbm8gY3VycmVudCBzZXNzaW9uIGV4aXN0cyBmb3IgdGhpcyB3aW5kb3csIHN0YXJ0IGZyZXNoXG4gICAgaWYgKCFjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+GlSBOZXcgd2luZG93IGRldGVjdGVkIC0gc3RhcnRpbmcgd2l0aCBmcmVzaCBzdGF0ZScpO1xuICAgICAgcmV0dXJuIGluaXRpYWxUcmFkaW5nU3RhdGU7XG4gICAgfVxuXG4gICAgLy8gSWYgd2UgaGF2ZSBhIHNlc3Npb24sIHRyeSB0byBsb2FkIHRoZSBzZXNzaW9uIGRhdGEgZmlyc3QgKHByaW9yaXR5IG92ZXIgbG9jYWxTdG9yYWdlKVxuICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uID0gc2Vzc2lvbk1hbmFnZXIubG9hZFNlc3Npb24oY3VycmVudFNlc3Npb25JZCk7XG4gICAgaWYgKGN1cnJlbnRTZXNzaW9uKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBSZXN0b3JpbmcgYWN0aXZlIHNlc3Npb24gYWZ0ZXIgcGFnZSByZWZyZXNoOicsIHtcbiAgICAgICAgc2Vzc2lvbklkOiBjdXJyZW50U2Vzc2lvbklkLFxuICAgICAgICBuYW1lOiBjdXJyZW50U2Vzc2lvbi5uYW1lLFxuICAgICAgICBpc0FjdGl2ZTogY3VycmVudFNlc3Npb24uaXNBY3RpdmUsXG4gICAgICAgIGNyeXB0bzE6IGN1cnJlbnRTZXNzaW9uLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yOiBjdXJyZW50U2Vzc2lvbi5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgc3RhYmxlY29pbjogY3VycmVudFNlc3Npb24uc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgIHRhcmdldFByaWNlUm93czogY3VycmVudFNlc3Npb24udGFyZ2V0UHJpY2VSb3dzLmxlbmd0aCxcbiAgICAgICAgb3JkZXJIaXN0b3J5OiBjdXJyZW50U2Vzc2lvbi5vcmRlckhpc3RvcnkubGVuZ3RoXG4gICAgICB9KTtcblxuICAgICAgLy8gRW5zdXJlIHRoZSBzZXNzaW9uIHJlbWFpbnMgYWN0aXZlIGlmIGl0IHdhcyBhY3RpdmUgYmVmb3JlIHJlZnJlc2hcbiAgICAgIGlmIChjdXJyZW50U2Vzc2lvbi5pc0FjdGl2ZSkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIE1haW50YWluaW5nIGFjdGl2ZSBzZXNzaW9uIHN0YXRlIGFmdGVyIHBhZ2UgcmVmcmVzaCcpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5pbml0aWFsVHJhZGluZ1N0YXRlLFxuICAgICAgICBjb25maWc6IGN1cnJlbnRTZXNzaW9uLmNvbmZpZyxcbiAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBjdXJyZW50U2Vzc2lvbi50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgIG9yZGVySGlzdG9yeTogY3VycmVudFNlc3Npb24ub3JkZXJIaXN0b3J5LFxuICAgICAgICBjdXJyZW50TWFya2V0UHJpY2U6IGN1cnJlbnRTZXNzaW9uLmN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgY3J5cHRvMUJhbGFuY2U6IGN1cnJlbnRTZXNzaW9uLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogY3VycmVudFNlc3Npb24uY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBjdXJyZW50U2Vzc2lvbi5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgYm90U3lzdGVtU3RhdHVzOiAoY3VycmVudFNlc3Npb24uaXNBY3RpdmUgPyAnUnVubmluZycgOiAnU3RvcHBlZCcpIGFzIEJvdFN5c3RlbVN0YXR1c1xuICAgICAgfTtcbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBsb2NhbFN0b3JhZ2UgaWYgc2Vzc2lvbiBsb2FkaW5nIGZhaWxzXG4gICAgY29uc3Qgc2F2ZWRTdGF0ZSA9IGxvYWRTdGF0ZUZyb21Mb2NhbFN0b3JhZ2UoKTtcbiAgICBpZiAoc2F2ZWRTdGF0ZSkge1xuICAgICAgcmV0dXJuIHsgLi4uaW5pdGlhbFRyYWRpbmdTdGF0ZSwgLi4uc2F2ZWRTdGF0ZSB9O1xuICAgIH1cbiAgICByZXR1cm4gaW5pdGlhbFRyYWRpbmdTdGF0ZTtcbiAgfTtcblxuICBjb25zdCBbc3RhdGUsIGRpc3BhdGNoXSA9IHVzZVJlZHVjZXIodHJhZGluZ1JlZHVjZXIsIGluaXRpYWxpemVTdGF0ZSgpKTtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcblxuICAvLyBDbGVhciBVUkwgcGFyYW1ldGVyIGFmdGVyIGNvbXBvbmVudCBtb3VudHMgdG8gYXZvaWQgcmVuZGVyLXRpbWUgc3RhdGUgdXBkYXRlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgdXJsUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKTtcbiAgICAgIGNvbnN0IGlzTmV3U2Vzc2lvbiA9IHVybFBhcmFtcy5nZXQoJ25ld1Nlc3Npb24nKSA9PT0gJ3RydWUnO1xuXG4gICAgICBpZiAoaXNOZXdTZXNzaW9uKSB7XG4gICAgICAgIC8vIENsZWFyIHRoZSBVUkwgcGFyYW1ldGVyIHRvIGF2b2lkIGNvbmZ1c2lvblxuICAgICAgICBjb25zdCBuZXdVcmwgPSB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWU7XG4gICAgICAgIHdpbmRvdy5oaXN0b3J5LnJlcGxhY2VTdGF0ZSh7fSwgJycsIG5ld1VybCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbXSk7IC8vIFJ1biBvbmx5IG9uY2Ugb24gbW91bnRcbiAgY29uc3QgYXVkaW9SZWYgPSB1c2VSZWY8SFRNTEF1ZGlvRWxlbWVudCB8IG51bGw+KG51bGwpO1xuICAvLyBSZW1vdmVkIHByb2Nlc3NpbmcgbG9ja3MgYW5kIGNvb2xkb3ducyBmb3IgY29udGludW91cyB0cmFkaW5nXG5cbiAgLy8gRW5oYW5jZWQgVGVsZWdyYW0gbm90aWZpY2F0aW9uIGZ1bmN0aW9uIHdpdGggZXJyb3IgaGFuZGxpbmcsIHJldHJ5IGxvZ2ljLCBhbmQgb2ZmbGluZSBxdWV1ZVxuICBjb25zdCBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAobWVzc2FnZTogc3RyaW5nLCBpc0Vycm9yOiBib29sZWFuID0gZmFsc2UsIHJldHJ5Q291bnQ6IG51bWJlciA9IDApID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdGVsZWdyYW1Ub2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0ZWxlZ3JhbV9ib3RfdG9rZW4nKTtcbiAgICAgIGNvbnN0IHRlbGVncmFtQ2hhdElkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3RlbGVncmFtX2NoYXRfaWQnKTtcblxuICAgICAgaWYgKCF0ZWxlZ3JhbVRva2VuIHx8ICF0ZWxlZ3JhbUNoYXRJZCkge1xuICAgICAgICBjb25zb2xlLmxvZygnVGVsZWdyYW0gbm90IGNvbmZpZ3VyZWQgLSBza2lwcGluZyBub3RpZmljYXRpb24nKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB3ZSdyZSBvbmxpbmUgYmVmb3JlIGF0dGVtcHRpbmcgdG8gc2VuZFxuICAgICAgY29uc3QgbmV0d29ya01vbml0b3IgPSBOZXR3b3JrTW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgICAgaWYgKCFuZXR3b3JrTW9uaXRvci5nZXRTdGF0dXMoKS5pc09ubGluZSkge1xuICAgICAgICAvLyBRdWV1ZSB0aGUgbm90aWZpY2F0aW9uIGZvciB3aGVuIHdlJ3JlIGJhY2sgb25saW5lXG4gICAgICAgIGlmICh0ZWxlZ3JhbU5vdGlmaWNhdGlvblF1ZXVlLmxlbmd0aCA8IE1BWF9RVUVVRV9TSVpFKSB7XG4gICAgICAgICAgdGVsZWdyYW1Ob3RpZmljYXRpb25RdWV1ZS5wdXNoKHtcbiAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICBpc0Vycm9yXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk6QgUXVldWVkIHRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3Igd2hlbiBvbmxpbmUgKHF1ZXVlIHNpemU6JywgdGVsZWdyYW1Ob3RpZmljYXRpb25RdWV1ZS5sZW5ndGgsICcpJyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gVGVsZWdyYW0gbm90aWZpY2F0aW9uIHF1ZXVlIGlzIGZ1bGwsIGRyb3BwaW5nIG9sZGVzdCBub3RpZmljYXRpb24nKTtcbiAgICAgICAgICB0ZWxlZ3JhbU5vdGlmaWNhdGlvblF1ZXVlLnNoaWZ0KCk7IC8vIFJlbW92ZSBvbGRlc3RcbiAgICAgICAgICB0ZWxlZ3JhbU5vdGlmaWNhdGlvblF1ZXVlLnB1c2goeyBtZXNzYWdlLCB0aW1lc3RhbXA6IERhdGUubm93KCksIGlzRXJyb3IgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBBZGQgdGltZW91dCB0byBwcmV2ZW50IGhhbmdpbmcgcmVxdWVzdHNcbiAgICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IGNvbnRyb2xsZXIuYWJvcnQoKSwgMTAwMDApOyAvLyAxMCBzZWNvbmQgdGltZW91dFxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS50ZWxlZ3JhbS5vcmcvYm90JHt0ZWxlZ3JhbVRva2VufS9zZW5kTWVzc2FnZWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgY2hhdF9pZDogdGVsZWdyYW1DaGF0SWQsXG4gICAgICAgICAgdGV4dDogbWVzc2FnZSxcbiAgICAgICAgICBwYXJzZV9tb2RlOiAnSFRNTCdcbiAgICAgICAgfSksXG4gICAgICAgIHNpZ25hbDogY29udHJvbGxlci5zaWduYWxcbiAgICAgIH0pO1xuXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb246JywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG5cbiAgICAgICAgLy8gUmV0cnkgbG9naWMgZm9yIG5vbi1lcnJvciBub3RpZmljYXRpb25zXG4gICAgICAgIGlmICghaXNFcnJvciAmJiByZXRyeUNvdW50IDwgMikge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFJldHJ5aW5nIHRlbGVncmFtIG5vdGlmaWNhdGlvbiAoYXR0ZW1wdCAke3JldHJ5Q291bnQgKyAxfS8zKS4uLmApO1xuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uKG1lc3NhZ2UsIGlzRXJyb3IsIHJldHJ5Q291bnQgKyAxKTtcbiAgICAgICAgICB9LCA1MDAwICogKHJldHJ5Q291bnQgKyAxKSk7IC8vIEV4cG9uZW50aWFsIGJhY2tvZmY6IDVzLCAxMHNcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyBEb24ndCByZXRyeSBlcnJvciBub3RpZmljYXRpb25zIHRvIGF2b2lkIGluZmluaXRlIGxvb3BzXG4gICAgICAgIGlmICghaXNFcnJvciAmJiByZXRyeUNvdW50ID49IDIpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQWxsIHRlbGVncmFtIG5vdGlmaWNhdGlvbiByZXRyeSBhdHRlbXB0cyBmYWlsZWQnKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBUZWxlZ3JhbSBub3RpZmljYXRpb24gc2VudCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBUZWxlZ3JhbSBub3RpZmljYXRpb246JywgZXJyb3IpO1xuXG4gICAgICAvLyBSZXRyeSBsb2dpYyBmb3IgbmV0d29yayBlcnJvcnMgKGJ1dCBub3QgZm9yIGVycm9yIG5vdGlmaWNhdGlvbnMgdG8gYXZvaWQgbG9vcHMpXG4gICAgICBpZiAoIWlzRXJyb3IgJiYgcmV0cnlDb3VudCA8IDIpIHtcbiAgICAgICAgY29uc29sZS5sb2coYPCflIQgUmV0cnlpbmcgdGVsZWdyYW0gbm90aWZpY2F0aW9uIGFmdGVyIG5ldHdvcmsgZXJyb3IgKGF0dGVtcHQgJHtyZXRyeUNvdW50ICsgMX0vMykuLi5gKTtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uKG1lc3NhZ2UsIGlzRXJyb3IsIHJldHJ5Q291bnQgKyAxKTtcbiAgICAgICAgfSwgMTAwMDAgKiAocmV0cnlDb3VudCArIDEpKTsgLy8gTG9uZ2VyIGJhY2tvZmYgZm9yIG5ldHdvcmsgZXJyb3JzOiAxMHMsIDIwc1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmICghaXNFcnJvciAmJiByZXRyeUNvdW50ID49IDIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFsbCB0ZWxlZ3JhbSBub3RpZmljYXRpb24gcmV0cnkgYXR0ZW1wdHMgZmFpbGVkIGR1ZSB0byBuZXR3b3JrIGVycm9ycycpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIFNwZWNpZmljIGVycm9yIG5vdGlmaWNhdGlvbiBmdW5jdGlvbnNcbiAgY29uc3Qgc2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoZXJyb3JUeXBlOiBzdHJpbmcsIGVycm9yTWVzc2FnZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpO1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBg4pqg77iPIDxiPkVSUk9SIEFMRVJUPC9iPlxcblxcbmAgK1xuICAgICAgICAgICAgICAgICAgIGDwn5S0IDxiPlR5cGU6PC9iPiAke2Vycm9yVHlwZX1cXG5gICtcbiAgICAgICAgICAgICAgICAgICBg8J+TnSA8Yj5NZXNzYWdlOjwvYj4gJHtlcnJvck1lc3NhZ2V9XFxuYCArXG4gICAgICAgICAgICAgICAgICAgYOKPsCA8Yj5UaW1lOjwvYj4gJHt0aW1lc3RhbXB9XFxuYCArXG4gICAgICAgICAgICAgICAgICAgYPCfpJYgPGI+Qm90OjwvYj4gJHtzdGF0ZS5jb25maWcuY3J5cHRvMX0vJHtzdGF0ZS5jb25maWcuY3J5cHRvMn0gJHtzdGF0ZS5jb25maWcudHJhZGluZ01vZGV9YDtcblxuICAgIGF3YWl0IHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbihtZXNzYWdlLCB0cnVlKTsgLy8gTWFyayBhcyBlcnJvciB0byBwcmV2ZW50IHJlY3Vyc2lvblxuICB9LCBbc3RhdGUuY29uZmlnLCBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb25dKTtcblxuICBjb25zdCBzZW5kTG93QmFsYW5jZU5vdGlmaWNhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChjcnlwdG86IHN0cmluZywgY3VycmVudEJhbGFuY2U6IG51bWJlciwgcmVxdWlyZWRBbW91bnQ6IG51bWJlcikgPT4ge1xuICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKTtcbiAgICBjb25zdCBtZXNzYWdlID0gYPCfkrAgPGI+TE9XIEJBTEFOQ0UgQUxFUlQ8L2I+XFxuXFxuYCArXG4gICAgICAgICAgICAgICAgICAgYPCfqpkgPGI+Q3VycmVuY3k6PC9iPiAke2NyeXB0b31cXG5gICtcbiAgICAgICAgICAgICAgICAgICBg8J+TiiA8Yj5DdXJyZW50IEJhbGFuY2U6PC9iPiAke2N1cnJlbnRCYWxhbmNlLnRvRml4ZWQoNil9ICR7Y3J5cHRvfVxcbmAgK1xuICAgICAgICAgICAgICAgICAgIGDimqEgPGI+UmVxdWlyZWQgQW1vdW50OjwvYj4gJHtyZXF1aXJlZEFtb3VudC50b0ZpeGVkKDYpfSAke2NyeXB0b31cXG5gICtcbiAgICAgICAgICAgICAgICAgICBg8J+TiSA8Yj5TaG9ydGFnZTo8L2I+ICR7KHJlcXVpcmVkQW1vdW50IC0gY3VycmVudEJhbGFuY2UpLnRvRml4ZWQoNil9ICR7Y3J5cHRvfVxcbmAgK1xuICAgICAgICAgICAgICAgICAgIGDij7AgPGI+VGltZTo8L2I+ICR7dGltZXN0YW1wfVxcbmAgK1xuICAgICAgICAgICAgICAgICAgIGDwn6SWIDxiPkJvdDo8L2I+ICR7c3RhdGUuY29uZmlnLmNyeXB0bzF9LyR7c3RhdGUuY29uZmlnLmNyeXB0bzJ9ICR7c3RhdGUuY29uZmlnLnRyYWRpbmdNb2RlfWA7XG5cbiAgICBhd2FpdCBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24obWVzc2FnZSk7XG4gIH0sIFtzdGF0ZS5jb25maWcsIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbl0pO1xuXG4gIGNvbnN0IHNlbmRBUElFcnJvck5vdGlmaWNhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChhcGlOYW1lOiBzdHJpbmcsIGVycm9yRGV0YWlsczogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpO1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBg8J+UjCA8Yj5BUEkgRVJST1IgQUxFUlQ8L2I+XFxuXFxuYCArXG4gICAgICAgICAgICAgICAgICAgYPCfjJAgPGI+QVBJOjwvYj4gJHthcGlOYW1lfVxcbmAgK1xuICAgICAgICAgICAgICAgICAgIGDinYwgPGI+RXJyb3I6PC9iPiAke2Vycm9yRGV0YWlsc31cXG5gICtcbiAgICAgICAgICAgICAgICAgICBg4o+wIDxiPlRpbWU6PC9iPiAke3RpbWVzdGFtcH1cXG5gICtcbiAgICAgICAgICAgICAgICAgICBg8J+kliA8Yj5Cb3Q6PC9iPiAke3N0YXRlLmNvbmZpZy5jcnlwdG8xfS8ke3N0YXRlLmNvbmZpZy5jcnlwdG8yfSAke3N0YXRlLmNvbmZpZy50cmFkaW5nTW9kZX1gO1xuXG4gICAgYXdhaXQgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uKG1lc3NhZ2UpO1xuICB9LCBbc3RhdGUuY29uZmlnLCBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb25dKTtcblxuICAvLyBJbml0aWFsaXplIGZldGNoTWFya2V0UHJpY2UgYWZ0ZXIgdGVsZWdyYW0gZnVuY3Rpb25zXG4gIGNvbnN0IGZldGNoTWFya2V0UHJpY2UgPSB1c2VDYWxsYmFjayhhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIERvbid0IGZldGNoIHByaWNlIGlmIGVpdGhlciBjcnlwdG8gaXMgbm90IHNlbGVjdGVkXG4gICAgICBpZiAoIXN0YXRlLmNvbmZpZy5jcnlwdG8xIHx8ICFzdGF0ZS5jb25maWcuY3J5cHRvMikge1xuICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfTUFSS0VUX1BSSUNFJywgcGF5bG9hZDogMCB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBsZXQgcHJpY2U6IG51bWJlcjtcblxuICAgICAgLy8gRm9yIFN0YWJsZWNvaW5Td2FwIG1vZGUsIHVzZSByZWFsLXRpbWUgcHJpY2VzIGZvciBhY2N1cmF0ZSBtYXJrZXQgY2FsY3VsYXRpb25cbiAgICAgIGlmIChzdGF0ZS5jb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIikge1xuICAgICAgICAvLyBUcnkgdG8gZ2V0IHJlYWwtdGltZSBwcmljZXMgZmlyc3RcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBjcnlwdG8xVVNEUHJpY2UgPSBhd2FpdCBnZXRSZWFsVGltZVVTRFByaWNlKHN0YXRlLmNvbmZpZy5jcnlwdG8xKTtcbiAgICAgICAgICBjb25zdCBjcnlwdG8yVVNEUHJpY2UgPSBhd2FpdCBnZXRSZWFsVGltZVVTRFByaWNlKHN0YXRlLmNvbmZpZy5jcnlwdG8yKTtcbiAgICAgICAgICBwcmljZSA9IGNyeXB0bzFVU0RQcmljZSAvIGNyeXB0bzJVU0RQcmljZTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiBTdGFibGVjb2luU3dhcCBNYXJrZXQgUHJpY2UgQ2FsY3VsYXRpb24gKFJlYWwtdGltZSk6XG4gICAgICAgICAgICAtICR7c3RhdGUuY29uZmlnLmNyeXB0bzF9IFVTRCBQcmljZTogJCR7Y3J5cHRvMVVTRFByaWNlLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICAtICR7c3RhdGUuY29uZmlnLmNyeXB0bzJ9IFVTRCBQcmljZTogJCR7Y3J5cHRvMlVTRFByaWNlLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICAtIE1hcmtldCBQcmljZSAoJHtzdGF0ZS5jb25maWcuY3J5cHRvMX0vJHtzdGF0ZS5jb25maWcuY3J5cHRvMn0pOiAke3ByaWNlLnRvRml4ZWQoNil9XG4gICAgICAgICAgICAtIENhbGN1bGF0aW9uOiAke2NyeXB0bzFVU0RQcmljZX0gw7cgJHtjcnlwdG8yVVNEUHJpY2V9ID0gJHtwcmljZS50b0ZpeGVkKDYpfWApO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIGNhY2hlZC9zdGF0aWMgcHJpY2VzXG4gICAgICAgICAgY29uc3QgY3J5cHRvMVVTRFByaWNlID0gZ2V0VVNEUHJpY2Uoc3RhdGUuY29uZmlnLmNyeXB0bzEpO1xuICAgICAgICAgIGNvbnN0IGNyeXB0bzJVU0RQcmljZSA9IGdldFVTRFByaWNlKHN0YXRlLmNvbmZpZy5jcnlwdG8yKTtcbiAgICAgICAgICBwcmljZSA9IGNyeXB0bzFVU0RQcmljZSAvIGNyeXB0bzJVU0RQcmljZTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiBTdGFibGVjb2luU3dhcCBNYXJrZXQgUHJpY2UgQ2FsY3VsYXRpb24gKEZhbGxiYWNrKTpcbiAgICAgICAgICAgIC0gJHtzdGF0ZS5jb25maWcuY3J5cHRvMX0gVVNEIFByaWNlOiAkJHtjcnlwdG8xVVNEUHJpY2UudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgIC0gJHtzdGF0ZS5jb25maWcuY3J5cHRvMn0gVVNEIFByaWNlOiAkJHtjcnlwdG8yVVNEUHJpY2UudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgIC0gTWFya2V0IFByaWNlICgke3N0YXRlLmNvbmZpZy5jcnlwdG8xfS8ke3N0YXRlLmNvbmZpZy5jcnlwdG8yfSk6ICR7cHJpY2UudG9GaXhlZCg2KX1gKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gRm9yIFNpbXBsZVNwb3QgbW9kZSwgdXNlIHRoZSBleGlzdGluZyBBUEktYmFzZWQgYXBwcm9hY2hcbiAgICAgICAgcHJpY2UgPSBhd2FpdCBnZXRNYXJrZXRQcmljZUZyb21BUEkoc3RhdGUuY29uZmlnKTtcbiAgICAgIH1cblxuICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX01BUktFVF9QUklDRScsIHBheWxvYWQ6IHByaWNlIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggbWFya2V0IHByaWNlOicsIGVycm9yKTtcbiAgICAgIHNlbmRBUElFcnJvck5vdGlmaWNhdGlvbignUHJpY2UgQVBJJywgYEZhaWxlZCB0byBmZXRjaCBtYXJrZXQgcHJpY2U6ICR7ZXJyb3J9YCkuY2F0Y2goY29uc29sZS5lcnJvcik7XG4gICAgfVxuICB9LCBbc3RhdGUuY29uZmlnLCBkaXNwYXRjaF0pO1xuXG4gIC8vIFJlYWwtdGltZSBwcmljZSB1cGRhdGUgZWZmZWN0IC0gZmV0Y2hlcyBsaXZlIHByaWNlcyBmcm9tIEJpbmFuY2UgQVBJXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbCBmZXRjaFxuICAgIGZldGNoTWFya2V0UHJpY2UoKTtcblxuICAgIC8vIFNldCB1cCByZWFsLXRpbWUgcHJpY2UgdXBkYXRlIGludGVydmFsIChldmVyeSAyIHNlY29uZHMgYXMgcmVxdWVzdGVkKVxuICAgIGNvbnN0IHJlYWxUaW1lUHJpY2VJbnRlcnZhbCA9IHNldEludGVydmFsKGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnN0IG5ldHdvcmtNb25pdG9yID0gTmV0d29ya01vbml0b3IuZ2V0SW5zdGFuY2UoKTtcbiAgICAgIGlmIChuZXR3b3JrTW9uaXRvci5nZXRTdGF0dXMoKS5pc09ubGluZSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIEZldGNoIGZyZXNoIHByaWNlIGZyb20gQVBJIGluc3RlYWQgb2YgdXNpbmcgbW9jayBmbHVjdHVhdGlvblxuICAgICAgICAgIGF3YWl0IGZldGNoTWFya2V0UHJpY2UoKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBGYWlsZWQgdG8gdXBkYXRlIHJlYWwtdGltZSBwcmljZTonLCBlcnJvcik7XG4gICAgICAgICAgLy8gU2VuZCBlcnJvciBub3RpZmljYXRpb24gZm9yIHByaWNlIGZldGNoIGZhaWx1cmVzXG4gICAgICAgICAgYXdhaXQgc2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24oJ1ByaWNlIFVwZGF0ZSBFcnJvcicsIGBGYWlsZWQgdG8gZmV0Y2ggcmVhbC10aW1lIHByaWNlOiAke2Vycm9yfWApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSwgMjAwMCk7IC8vIFVwZGF0ZSBldmVyeSAyIHNlY29uZHMgYXMgcmVxdWVzdGVkXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJJbnRlcnZhbChyZWFsVGltZVByaWNlSW50ZXJ2YWwpO1xuICAgIH07XG4gIH0sIFtmZXRjaE1hcmtldFByaWNlLCBzZW5kVGVsZWdyYW1FcnJvck5vdGlmaWNhdGlvbl0pO1xuXG4gIC8vIEF1ZGlvIGluaXRpYWxpemF0aW9uIGFuZCB1c2VyIGludGVyYWN0aW9uIGRldGVjdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICBhdWRpb1JlZi5jdXJyZW50ID0gbmV3IEF1ZGlvKCk7XG5cbiAgICAgIC8vIEFkZCB1c2VyIGludGVyYWN0aW9uIGxpc3RlbmVyIHRvIGVuYWJsZSBhdWRpb1xuICAgICAgY29uc3QgZW5hYmxlQXVkaW8gPSAoKSA9PiB7XG4gICAgICAgIGlmIChhdWRpb1JlZi5jdXJyZW50KSB7XG4gICAgICAgICAgLy8gVHJ5IHRvIHBsYXkgYSBzaWxlbnQgYXVkaW8gdG8gZW5hYmxlIGF1ZGlvIGNvbnRleHRcbiAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LnZvbHVtZSA9IDA7XG4gICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5wbGF5KCkudGhlbigoKSA9PiB7XG4gICAgICAgICAgICBpZiAoYXVkaW9SZWYuY3VycmVudCkge1xuICAgICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LnZvbHVtZSA9IDE7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UiiBBdWRpbyBjb250ZXh0IGVuYWJsZWQgYWZ0ZXIgdXNlciBpbnRlcmFjdGlvbicpO1xuICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHtcbiAgICAgICAgICAgIC8vIFN0aWxsIGJsb2NrZWQsIGJ1dCB3ZSB0cmllZFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIC8vIFJlbW92ZSB0aGUgbGlzdGVuZXIgYWZ0ZXIgZmlyc3QgaW50ZXJhY3Rpb25cbiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBlbmFibGVBdWRpbyk7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBlbmFibGVBdWRpbyk7XG4gICAgICB9O1xuXG4gICAgICAvLyBMaXN0ZW4gZm9yIGZpcnN0IHVzZXIgaW50ZXJhY3Rpb25cbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgZW5hYmxlQXVkaW8pO1xuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGVuYWJsZUF1ZGlvKTtcblxuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBlbmFibGVBdWRpbyk7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBlbmFibGVBdWRpbyk7XG4gICAgICB9O1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IHBsYXlTb3VuZCA9IHVzZUNhbGxiYWNrKChzb3VuZEtleTogJ3NvdW5kT3JkZXJFeGVjdXRpb24nIHwgJ3NvdW5kRXJyb3InKSA9PiB7XG4gICAgLy8gR2V0IGN1cnJlbnQgc2Vzc2lvbidzIGFsYXJtIHNldHRpbmdzLCBmYWxsYmFjayB0byBnbG9iYWwgYXBwIHNldHRpbmdzXG4gICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uSWQgPSBzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCk7XG4gICAgbGV0IGFsYXJtU2V0dGluZ3MgPSBzdGF0ZS5hcHBTZXR0aW5nczsgLy8gRGVmYXVsdCBmYWxsYmFja1xuXG4gICAgaWYgKGN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uID0gc2Vzc2lvbk1hbmFnZXIubG9hZFNlc3Npb24oY3VycmVudFNlc3Npb25JZCk7XG4gICAgICBpZiAoY3VycmVudFNlc3Npb24gJiYgY3VycmVudFNlc3Npb24uYWxhcm1TZXR0aW5ncykge1xuICAgICAgICBhbGFybVNldHRpbmdzID0gY3VycmVudFNlc3Npb24uYWxhcm1TZXR0aW5ncztcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoYWxhcm1TZXR0aW5ncy5zb3VuZEFsZXJ0c0VuYWJsZWQgJiYgYXVkaW9SZWYuY3VycmVudCkge1xuICAgICAgbGV0IHNvdW5kUGF0aDogc3RyaW5nIHwgdW5kZWZpbmVkO1xuICAgICAgaWYgKHNvdW5kS2V5ID09PSAnc291bmRPcmRlckV4ZWN1dGlvbicgJiYgYWxhcm1TZXR0aW5ncy5hbGVydE9uT3JkZXJFeGVjdXRpb24pIHtcbiAgICAgICAgc291bmRQYXRoID0gYWxhcm1TZXR0aW5ncy5zb3VuZE9yZGVyRXhlY3V0aW9uO1xuICAgICAgfSBlbHNlIGlmIChzb3VuZEtleSA9PT0gJ3NvdW5kRXJyb3InICYmIGFsYXJtU2V0dGluZ3MuYWxlcnRPbkVycm9yKSB7XG4gICAgICAgIHNvdW5kUGF0aCA9IGFsYXJtU2V0dGluZ3Muc291bmRFcnJvcjtcbiAgICAgIH1cblxuICAgICAgaWYgKHNvdW5kUGF0aCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIFN0b3AgYW55IGN1cnJlbnRseSBwbGF5aW5nIGF1ZGlvIGZpcnN0XG4gICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5wYXVzZSgpO1xuICAgICAgICAgIGF1ZGlvUmVmLmN1cnJlbnQuY3VycmVudFRpbWUgPSAwO1xuXG4gICAgICAgICAgLy8gU2V0IHRoZSBzb3VyY2UgYW5kIGxvYWQgdGhlIGF1ZGlvXG4gICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5zcmMgPSBzb3VuZFBhdGg7XG4gICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5sb2FkKCk7IC8vIEV4cGxpY2l0bHkgbG9hZCB0aGUgYXVkaW9cblxuICAgICAgICAgIC8vIEFkZCBlcnJvciBoYW5kbGVyIGZvciBhdWRpbyBsb2FkaW5nXG4gICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5vbmVycm9yID0gKGUpID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybihg4pqg77iPIEF1ZGlvIGxvYWRpbmcgZmFpbGVkIGZvciAke3NvdW5kUGF0aH06YCwgZSk7XG4gICAgICAgICAgICAvLyBUcnkgZmFsbGJhY2sgc291bmRcbiAgICAgICAgICAgIGlmIChzb3VuZFBhdGggIT09ICcvc291bmRzL2NoaW1lMi53YXYnICYmIGF1ZGlvUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5zcmMgPSAnL3NvdW5kcy9jaGltZTIud2F2JztcbiAgICAgICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5sb2FkKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcblxuICAgICAgICAgIC8vIFBsYXkgdGhlIHNvdW5kIGFuZCBsaW1pdCBkdXJhdGlvbiB0byAyIHNlY29uZHNcbiAgICAgICAgICBjb25zdCBwbGF5UHJvbWlzZSA9IGF1ZGlvUmVmLmN1cnJlbnQucGxheSgpO1xuXG4gICAgICAgICAgaWYgKHBsYXlQcm9taXNlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHBsYXlQcm9taXNlLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAvLyBTZXQgYSB0aW1lb3V0IHRvIHBhdXNlIHRoZSBhdWRpbyBhZnRlciAyIHNlY29uZHNcbiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGF1ZGlvUmVmLmN1cnJlbnQgJiYgIWF1ZGlvUmVmLmN1cnJlbnQucGF1c2VkKSB7XG4gICAgICAgICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LnBhdXNlKCk7XG4gICAgICAgICAgICAgICAgICBhdWRpb1JlZi5jdXJyZW50LmN1cnJlbnRUaW1lID0gMDsgLy8gUmVzZXQgZm9yIG5leHQgcGxheVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSwgMjAwMCk7IC8vIDIgc2Vjb25kc1xuICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHtcbiAgICAgICAgICAgICAgLy8gSGFuZGxlIGRpZmZlcmVudCB0eXBlcyBvZiBhdWRpbyBlcnJvcnMgZ3JhY2VmdWxseVxuICAgICAgICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIubWVzc2FnZSB8fCBTdHJpbmcoZXJyKTtcblxuICAgICAgICAgICAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCd1c2VyIGRpZG5cXCd0IGludGVyYWN0JykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdhdXRvcGxheScpKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIcgQXVkaW8gYXV0b3BsYXkgYmxvY2tlZCBieSBicm93c2VyIC0gdXNlciBpbnRlcmFjdGlvbiByZXF1aXJlZCcpO1xuICAgICAgICAgICAgICAgIC8vIENvdWxkIHNob3cgYSBub3RpZmljYXRpb24gdG8gdXNlciBhYm91dCBlbmFibGluZyBhdWRpb1xuICAgICAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnaW50ZXJydXB0ZWQnKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ3N1cHBvcnRlZCBzb3VyY2UnKSkge1xuICAgICAgICAgICAgICAgIC8vIFRoZXNlIGFyZSBleHBlY3RlZCBlcnJvcnMsIGRvbid0IGxvZyB0aGVtXG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwi4pqg77iPIEF1ZGlvIHBsYXliYWNrIGZhaWxlZDpcIiwgZXJyb3JNZXNzYWdlKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUud2Fybihg4pqg77iPIEF1ZGlvIHNldHVwIGZhaWxlZCBmb3IgJHtzb3VuZFBhdGh9OmAsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSwgW3N0YXRlLmFwcFNldHRpbmdzXSk7XG5cbiAgLy8gRWZmZWN0IHRvIHVwZGF0ZSBtYXJrZXQgcHJpY2Ugd2hlbiB0cmFkaW5nIHBhaXIgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFdoZW4gY3J5cHRvMSBvciBjcnlwdG8yIChwYXJ0cyBvZiBzdGF0ZS5jb25maWcpIGNoYW5nZSxcbiAgICAvLyB0aGUgZmV0Y2hNYXJrZXRQcmljZSB1c2VDYWxsYmFjayBnZXRzIGEgbmV3IHJlZmVyZW5jZS5cbiAgICAvLyBUaGUgdXNlRWZmZWN0IGFib3ZlICh3aGljaCBkZXBlbmRzIG9uIGZldGNoTWFya2V0UHJpY2UpXG4gICAgLy8gd2lsbCByZS1ydW4sIGNsZWFyIHRoZSBvbGQgaW50ZXJ2YWwsIG1ha2UgYW4gaW5pdGlhbCBmZXRjaCB3aXRoIHRoZSBuZXcgY29uZmlnLFxuICAgIC8vIGFuZCBzZXQgdXAgYSBuZXcgaW50ZXJ2YWwuXG4gICAgLy8gVGhlIHJlZHVjZXIgZm9yIFNFVF9DT05GSUcgYWxzbyBzZXRzIGFuIGluaXRpYWwgbWFya2V0IHByaWNlIGlmIGNyeXB0bzEvY3J5cHRvMiBjaGFuZ2VzLlxuICAgIC8vIFRodXMsIG5vIGV4cGxpY2l0IGRpc3BhdGNoIGlzIG5lZWRlZCBoZXJlLlxuICB9LCBbc3RhdGUuY29uZmlnLmNyeXB0bzEsIHN0YXRlLmNvbmZpZy5jcnlwdG8yXSk7IC8vIERlcGVuZGVuY2llcyBlbnN1cmUgdGhpcyByZWFjdHMgdG8gcGFpciBjaGFuZ2VzXG5cbiAgY29uc3Qgc2V0VGFyZ2V0UHJpY2VzID0gdXNlQ2FsbGJhY2soKHByaWNlczogbnVtYmVyW10pID0+IHtcbiAgICBpZiAoIXByaWNlcyB8fCAhQXJyYXkuaXNBcnJheShwcmljZXMpKSByZXR1cm47XG5cbiAgICAvLyBTb3J0IHByaWNlcyBmcm9tIGxvd2VzdCB0byBoaWdoZXN0IGZvciBwcm9wZXIgY291bnRlciBhc3NpZ25tZW50XG4gICAgY29uc3Qgc29ydGVkUHJpY2VzID0gWy4uLnByaWNlc10uZmlsdGVyKChwcmljZTogbnVtYmVyKSA9PiAhaXNOYU4ocHJpY2UpICYmIHByaWNlID4gMCkuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xuXG4gICAgY29uc3QgbmV3Um93czogVGFyZ2V0UHJpY2VSb3dbXSA9IHNvcnRlZFByaWNlcy5tYXAoKHByaWNlOiBudW1iZXIsIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgY29uc3QgZXhpc3RpbmdSb3cgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MuZmluZCgocjogVGFyZ2V0UHJpY2VSb3cpID0+IHIudGFyZ2V0UHJpY2UgPT09IHByaWNlKTtcbiAgICAgICAgaWYgKGV4aXN0aW5nUm93KSB7XG4gICAgICAgICAgLy8gVXBkYXRlIGNvdW50ZXIgZm9yIGV4aXN0aW5nIHJvdyBiYXNlZCBvbiBzb3J0ZWQgcG9zaXRpb25cbiAgICAgICAgICByZXR1cm4geyAuLi5leGlzdGluZ1JvdywgY291bnRlcjogaW5kZXggKyAxIH07XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICBjb3VudGVyOiBpbmRleCArIDEsIC8vIENvdW50ZXIgc3RhcnRzIGZyb20gMSwgbG93ZXN0IHByaWNlIGdldHMgY291bnRlciAxXG4gICAgICAgICAgc3RhdHVzOiAnRnJlZScgYXMgT3JkZXJTdGF0dXMsXG4gICAgICAgICAgb3JkZXJMZXZlbDogMCxcbiAgICAgICAgICB2YWx1ZUxldmVsOiBzdGF0ZS5jb25maWcuYmFzZUJpZCwgLy8gVXNlIGJhc2VCaWQgZnJvbSBjb25maWdcbiAgICAgICAgICB0YXJnZXRQcmljZTogcHJpY2UsXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfVEFSR0VUX1BSSUNFX1JPV1MnLCBwYXlsb2FkOiBuZXdSb3dzIH0pO1xuICB9LCBbc3RhdGUudGFyZ2V0UHJpY2VSb3dzLCBzdGF0ZS5jb25maWcuYmFzZUJpZCwgZGlzcGF0Y2hdKTtcblxuICAvLyBDb3JlIFRyYWRpbmcgTG9naWMgKFNpbXVsYXRlZCkgLSBDT05USU5VT1VTIFRSQURJTkcgVkVSU0lPTlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIENoZWNrIG5ldHdvcmsgc3RhdHVzIGZpcnN0XG4gICAgY29uc3QgbmV0d29ya01vbml0b3IgPSBOZXR3b3JrTW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IGlzT25saW5lID0gbmV0d29ya01vbml0b3IuZ2V0U3RhdHVzKCkuaXNPbmxpbmU7XG5cbiAgICAvLyBPbmx5IGNoZWNrIGVzc2VudGlhbCBjb25kaXRpb25zIEFORCBuZXR3b3JrIHN0YXR1c1xuICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgIT09ICdSdW5uaW5nJyB8fFxuICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MubGVuZ3RoID09PSAwIHx8XG4gICAgICAgIHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSA8PSAwIHx8XG4gICAgICAgICFpc09ubGluZSkge1xuXG4gICAgICBpZiAoIWlzT25saW5lICYmIHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5S0IFRyYWRpbmcgcGF1c2VkIC0gbmV0d29yayBvZmZsaW5lJyk7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gRXhlY3V0ZSB0cmFkaW5nIGxvZ2ljIGltbWVkaWF0ZWx5IC0gbm8gbG9ja3MsIG5vIGNvb2xkb3ducywgbm8gZGVsYXlzXG4gICAgY29uc3QgeyBjb25maWcsIGN1cnJlbnRNYXJrZXRQcmljZSwgdGFyZ2V0UHJpY2VSb3dzLCBjcnlwdG8xQmFsYW5jZSwgY3J5cHRvMkJhbGFuY2UgfSA9IHN0YXRlO1xuICAgIGNvbnN0IHNvcnRlZFJvd3NGb3JMb2dpYyA9IFsuLi50YXJnZXRQcmljZVJvd3NdLnNvcnQoKGE6IFRhcmdldFByaWNlUm93LCBiOiBUYXJnZXRQcmljZVJvdykgPT4gYS50YXJnZXRQcmljZSAtIGIudGFyZ2V0UHJpY2UpO1xuXG4gICAgLy8gVXNlIG11dGFibGUgdmFyaWFibGVzIGZvciBiYWxhbmNlIHRyYWNraW5nIHdpdGhpbiB0aGlzIGN5Y2xlXG4gICAgbGV0IGN1cnJlbnRDcnlwdG8xQmFsYW5jZSA9IGNyeXB0bzFCYWxhbmNlO1xuICAgIGxldCBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgPSBjcnlwdG8yQmFsYW5jZTtcbiAgICBsZXQgYWN0aW9uc1Rha2VuID0gMDtcblxuICAgIGNvbnNvbGUubG9nKGDwn5qAIENPTlRJTlVPVVMgVFJBRElORzogUHJpY2UgJCR7Y3VycmVudE1hcmtldFByaWNlLnRvRml4ZWQoMil9IHwgVGFyZ2V0czogJHtzb3J0ZWRSb3dzRm9yTG9naWMubGVuZ3RofSB8IEJhbGFuY2U6ICQke2N1cnJlbnRDcnlwdG8yQmFsYW5jZX0gJHtjb25maWcuY3J5cHRvMn1gKTtcblxuICAgIC8vIFNob3cgd2hpY2ggdGFyZ2V0cyBhcmUgaW4gcmFuZ2VcbiAgICBjb25zdCB0YXJnZXRzSW5SYW5nZSA9IHNvcnRlZFJvd3NGb3JMb2dpYy5maWx0ZXIocm93ID0+IHtcbiAgICAgIGNvbnN0IGRpZmZQZXJjZW50ID0gKE1hdGguYWJzKGN1cnJlbnRNYXJrZXRQcmljZSAtIHJvdy50YXJnZXRQcmljZSkgLyBjdXJyZW50TWFya2V0UHJpY2UpICogMTAwO1xuICAgICAgcmV0dXJuIGRpZmZQZXJjZW50IDw9IGNvbmZpZy5zbGlwcGFnZVBlcmNlbnQ7XG4gICAgfSk7XG5cbiAgICBpZiAodGFyZ2V0c0luUmFuZ2UubGVuZ3RoID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYPCfjq8gVEFSR0VUUyBJTiBSQU5HRSAowrEke2NvbmZpZy5zbGlwcGFnZVBlcmNlbnR9JSk6YCwgdGFyZ2V0c0luUmFuZ2UubWFwKHJvdyA9PiBgQ291bnRlciAke3Jvdy5jb3VudGVyfSAoJHtyb3cuc3RhdHVzfSlgKSk7XG4gICAgfVxuXG4gICAgLy8gQ09OVElOVU9VUyBUUkFESU5HIExPR0lDOiBQcm9jZXNzIGFsbCB0YXJnZXRzIGltbWVkaWF0ZWx5XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzb3J0ZWRSb3dzRm9yTG9naWMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGFjdGl2ZVJvdyA9IHNvcnRlZFJvd3NGb3JMb2dpY1tpXTtcbiAgICAgIGNvbnN0IHByaWNlRGlmZlBlcmNlbnQgPSBNYXRoLmFicyhjdXJyZW50TWFya2V0UHJpY2UgLSBhY3RpdmVSb3cudGFyZ2V0UHJpY2UpIC8gY3VycmVudE1hcmtldFByaWNlICogMTAwO1xuXG4gICAgICAvLyBTVEVQIDE6IENoZWNrIGlmIFRhcmdldFJvd04gaXMgdHJpZ2dlcmVkICh3aXRoaW4gc2xpcHBhZ2UgcmFuZ2UpXG4gICAgICBpZiAocHJpY2VEaWZmUGVyY2VudCA8PSBjb25maWcuc2xpcHBhZ2VQZXJjZW50KSB7XG5cbiAgICAgICAgaWYgKGNvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTaW1wbGVTcG90XCIpIHtcbiAgICAgICAgICAvLyBTVEVQIDI6IEV2YWx1YXRlIFRyaWdnZXJlZCBUYXJnZXRSb3dOJ3MgU3RhdHVzXG4gICAgICAgICAgaWYgKGFjdGl2ZVJvdy5zdGF0dXMgPT09IFwiRnJlZVwiKSB7XG4gICAgICAgICAgICAvLyBTVEVQIDJhOiBFeGVjdXRlIEJVWSBvbiBUYXJnZXRSb3dOXG4gICAgICAgICAgICBjb25zdCBjb3N0Q3J5cHRvMiA9IGFjdGl2ZVJvdy52YWx1ZUxldmVsO1xuXG4gICAgICAgICAgICBpZiAoY3VycmVudENyeXB0bzJCYWxhbmNlID49IGNvc3RDcnlwdG8yKSB7XG4gICAgICAgICAgICAgIGNvbnN0IGFtb3VudENyeXB0bzFCb3VnaHQgPSBjb3N0Q3J5cHRvMiAvIGN1cnJlbnRNYXJrZXRQcmljZTtcbiAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZFJvdzogVGFyZ2V0UHJpY2VSb3cgPSB7XG4gICAgICAgICAgICAgICAgLi4uYWN0aXZlUm93LFxuICAgICAgICAgICAgICAgIHN0YXR1czogXCJGdWxsXCIsXG4gICAgICAgICAgICAgICAgb3JkZXJMZXZlbDogYWN0aXZlUm93Lm9yZGVyTGV2ZWwgKyAxLFxuICAgICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIGFjdGl2ZVJvdy5vcmRlckxldmVsICsgMSksXG4gICAgICAgICAgICAgICAgY3J5cHRvMUFtb3VudEhlbGQ6IGFtb3VudENyeXB0bzFCb3VnaHQsXG4gICAgICAgICAgICAgICAgb3JpZ2luYWxDb3N0Q3J5cHRvMjogY29zdENyeXB0bzIsXG4gICAgICAgICAgICAgICAgY3J5cHRvMVZhcjogYW1vdW50Q3J5cHRvMUJvdWdodCxcbiAgICAgICAgICAgICAgICBjcnlwdG8yVmFyOiAtY29zdENyeXB0bzIsXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZFJvdyB9KTtcbiAgICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgKyBhbW91bnRDcnlwdG8xQm91Z2h0LCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgLSBjb3N0Q3J5cHRvMiB9IH0pO1xuICAgICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgICBwYWlyOiBgJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn1gLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzEsXG4gICAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiQlVZXCIsXG4gICAgICAgICAgICAgICAgICBhbW91bnRDcnlwdG8xOiBhbW91bnRDcnlwdG8xQm91Z2h0LFxuICAgICAgICAgICAgICAgICAgYXZnUHJpY2U6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogY29zdENyeXB0bzIsXG4gICAgICAgICAgICAgICAgICBwcmljZTE6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8xIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMlN5bWJvbDogY29uZmlnLmNyeXB0bzIgfHwgJydcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEJVWTogQ291bnRlciAke2FjdGl2ZVJvdy5jb3VudGVyfSBib3VnaHQgJHthbW91bnRDcnlwdG8xQm91Z2h0LnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IGF0ICQke2N1cnJlbnRNYXJrZXRQcmljZS50b0ZpeGVkKDIpfWApO1xuICAgICAgICAgICAgICB0b2FzdCh7IHRpdGxlOiBcIkJVWSBFeGVjdXRlZFwiLCBkZXNjcmlwdGlvbjogYENvdW50ZXIgJHthY3RpdmVSb3cuY291bnRlcn06ICR7YW1vdW50Q3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfWAsIGR1cmF0aW9uOiAyMDAwIH0pO1xuICAgICAgICAgICAgICBwbGF5U291bmQoJ3NvdW5kT3JkZXJFeGVjdXRpb24nKTtcblxuICAgICAgICAgICAgICAvLyBTZW5kIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgQlVZXG4gICAgICAgICAgICAgIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbihcbiAgICAgICAgICAgICAgICBg8J+foiA8Yj5CVVkgRVhFQ1VURUQ8L2I+XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfk4ogQ291bnRlcjogJHthY3RpdmVSb3cuY291bnRlcn1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+SsCBBbW91bnQ6ICR7YW1vdW50Q3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfVxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5K1IFByaWNlOiAkJHtjdXJyZW50TWFya2V0UHJpY2UudG9GaXhlZCgyKX1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+SuCBDb3N0OiAkJHtjb3N0Q3J5cHRvMi50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfVxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5OIIE1vZGU6IFNpbXBsZSBTcG90YFxuICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgIGFjdGlvbnNUYWtlbisrO1xuXG4gICAgICAgICAgICAgIC8vIFVwZGF0ZSBsb2NhbCBiYWxhbmNlIGZvciBzdWJzZXF1ZW50IGNoZWNrcyBpbiB0aGlzIGN5Y2xlXG4gICAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8yQmFsYW5jZSAtPSBjb3N0Q3J5cHRvMjtcbiAgICAgICAgICAgICAgY3VycmVudENyeXB0bzFCYWxhbmNlICs9IGFtb3VudENyeXB0bzFCb3VnaHQ7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAvLyBJbnN1ZmZpY2llbnQgYmFsYW5jZSAtIHNlbmQgVGVsZWdyYW0gbm90aWZpY2F0aW9uXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinYwgSW5zdWZmaWNpZW50ICR7Y29uZmlnLmNyeXB0bzJ9IGJhbGFuY2U6ICR7Y3VycmVudENyeXB0bzJCYWxhbmNlLnRvRml4ZWQoNil9IDwgJHtjb3N0Q3J5cHRvMi50b0ZpeGVkKDYpfWApO1xuICAgICAgICAgICAgICBzZW5kTG93QmFsYW5jZU5vdGlmaWNhdGlvbihjb25maWcuY3J5cHRvMiwgY3VycmVudENyeXB0bzJCYWxhbmNlLCBjb3N0Q3J5cHRvMikuY2F0Y2goY29uc29sZS5lcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gU1RFUCAzOiBDaGVjayBhbmQgUHJvY2VzcyBJbmZlcmlvciBUYXJnZXRSb3cgKFRhcmdldFJvd05fbWludXNfMSkgLSBBTFdBWVMsIHJlZ2FyZGxlc3Mgb2YgQlVZXG4gICAgICAgICAgY29uc3QgY3VycmVudENvdW50ZXIgPSBhY3RpdmVSb3cuY291bnRlcjtcbiAgICAgICAgICBjb25zdCBpbmZlcmlvclJvdyA9IHNvcnRlZFJvd3NGb3JMb2dpYy5maW5kKHJvdyA9PiByb3cuY291bnRlciA9PT0gY3VycmVudENvdW50ZXIgLSAxKTtcblxuICAgICAgICAgIGlmIChpbmZlcmlvclJvdyAmJiBpbmZlcmlvclJvdy5zdGF0dXMgPT09IFwiRnVsbFwiICYmIGluZmVyaW9yUm93LmNyeXB0bzFBbW91bnRIZWxkICYmIGluZmVyaW9yUm93Lm9yaWdpbmFsQ29zdENyeXB0bzIpIHtcbiAgICAgICAgICAgIGNvbnN0IGFtb3VudENyeXB0bzFUb1NlbGwgPSBpbmZlcmlvclJvdy5jcnlwdG8xQW1vdW50SGVsZDtcbiAgICAgICAgICAgIGNvbnN0IGNyeXB0bzJSZWNlaXZlZCA9IGFtb3VudENyeXB0bzFUb1NlbGwgKiBjdXJyZW50TWFya2V0UHJpY2U7XG4gICAgICAgICAgICBjb25zdCByZWFsaXplZFByb2ZpdCA9IGNyeXB0bzJSZWNlaXZlZCAtIGluZmVyaW9yUm93Lm9yaWdpbmFsQ29zdENyeXB0bzI7XG5cbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBDcnlwdG8xIHByb2ZpdC9sb3NzIGJhc2VkIG9uIGluY29tZSBzcGxpdCBwZXJjZW50YWdlXG4gICAgICAgICAgICBjb25zdCByZWFsaXplZFByb2ZpdENyeXB0bzEgPSBjdXJyZW50TWFya2V0UHJpY2UgPiAwID8gKHJlYWxpemVkUHJvZml0ICogY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQgLyAxMDApIC8gY3VycmVudE1hcmtldFByaWNlIDogMDtcblxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZEluZmVyaW9yUm93OiBUYXJnZXRQcmljZVJvdyA9IHtcbiAgICAgICAgICAgICAgLi4uaW5mZXJpb3JSb3csXG4gICAgICAgICAgICAgIHN0YXR1czogXCJGcmVlXCIsXG4gICAgICAgICAgICAgIGNyeXB0bzFBbW91bnRIZWxkOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIG9yaWdpbmFsQ29zdENyeXB0bzI6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgdmFsdWVMZXZlbDogY29uZmlnLmJhc2VCaWQgKiBNYXRoLnBvdyhjb25maWcubXVsdGlwbGllciwgaW5mZXJpb3JSb3cub3JkZXJMZXZlbCksXG4gICAgICAgICAgICAgIGNyeXB0bzFWYXI6IC1hbW91bnRDcnlwdG8xVG9TZWxsLFxuICAgICAgICAgICAgICBjcnlwdG8yVmFyOiBjcnlwdG8yUmVjZWl2ZWQsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX1RBUkdFVF9QUklDRV9ST1cnLCBwYXlsb2FkOiB1cGRhdGVkSW5mZXJpb3JSb3cgfSk7XG4gICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfQkFMQU5DRVMnLCBwYXlsb2FkOiB7IGNyeXB0bzE6IGN1cnJlbnRDcnlwdG8xQmFsYW5jZSAtIGFtb3VudENyeXB0bzFUb1NlbGwsIGNyeXB0bzI6IGN1cnJlbnRDcnlwdG8yQmFsYW5jZSArIGNyeXB0bzJSZWNlaXZlZCB9IH0pO1xuICAgICAgICAgICAgZGlzcGF0Y2goe1xuICAgICAgICAgICAgICB0eXBlOiAnQUREX09SREVSX0hJU1RPUllfRU5UUlknLFxuICAgICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgICAgaWQ6IHV1aWR2NCgpLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICBwYWlyOiBgJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn1gLFxuICAgICAgICAgICAgICAgIGNyeXB0bzE6IGNvbmZpZy5jcnlwdG8xLFxuICAgICAgICAgICAgICAgIG9yZGVyVHlwZTogXCJTRUxMXCIsXG4gICAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMTogYW1vdW50Q3J5cHRvMVRvU2VsbCxcbiAgICAgICAgICAgICAgICBhdmdQcmljZTogY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogY3J5cHRvMlJlY2VpdmVkLFxuICAgICAgICAgICAgICAgIHByaWNlMTogY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8xIHx8ICcnLFxuICAgICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5jcnlwdG8yIHx8ICcnLFxuICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzI6IHJlYWxpemVkUHJvZml0LFxuICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzE6IHJlYWxpemVkUHJvZml0Q3J5cHRvMVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgU0VMTDogQ291bnRlciAke2N1cnJlbnRDb3VudGVyIC0gMX0gc29sZCAke2Ftb3VudENyeXB0bzFUb1NlbGwudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX0uIFByb2ZpdDogJCR7cmVhbGl6ZWRQcm9maXQudG9GaXhlZCgyKX1gKTtcbiAgICAgICAgICAgIHRvYXN0KHsgdGl0bGU6IFwiU0VMTCBFeGVjdXRlZFwiLCBkZXNjcmlwdGlvbjogYENvdW50ZXIgJHtjdXJyZW50Q291bnRlciAtIDF9OiBQcm9maXQgJCR7cmVhbGl6ZWRQcm9maXQudG9GaXhlZCgyKX1gLCBkdXJhdGlvbjogMjAwMCB9KTtcbiAgICAgICAgICAgIHBsYXlTb3VuZCgnc291bmRPcmRlckV4ZWN1dGlvbicpO1xuXG4gICAgICAgICAgICAvLyBTZW5kIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgU0VMTFxuICAgICAgICAgICAgY29uc3QgcHJvZml0RW1vamkgPSByZWFsaXplZFByb2ZpdCA+IDAgPyAn8J+TiCcgOiByZWFsaXplZFByb2ZpdCA8IDAgPyAn8J+TiScgOiAn4p6WJztcbiAgICAgICAgICAgIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbihcbiAgICAgICAgICAgICAgYPCflLQgPGI+U0VMTCBFWEVDVVRFRDwvYj5cXG5gICtcbiAgICAgICAgICAgICAgYPCfk4ogQ291bnRlcjogJHtjdXJyZW50Q291bnRlciAtIDF9XFxuYCArXG4gICAgICAgICAgICAgIGDwn5KwIEFtb3VudDogJHthbW91bnRDcnlwdG8xVG9TZWxsLnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9XFxuYCArXG4gICAgICAgICAgICAgIGDwn5K1IFByaWNlOiAkJHtjdXJyZW50TWFya2V0UHJpY2UudG9GaXhlZCgyKX1cXG5gICtcbiAgICAgICAgICAgICAgYPCfkrggUmVjZWl2ZWQ6ICQke2NyeXB0bzJSZWNlaXZlZC50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfVxcbmAgK1xuICAgICAgICAgICAgICBgJHtwcm9maXRFbW9qaX0gUHJvZml0OiAkJHtyZWFsaXplZFByb2ZpdC50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfVxcbmAgK1xuICAgICAgICAgICAgICBg8J+TiCBNb2RlOiBTaW1wbGUgU3BvdGBcbiAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgIGFjdGlvbnNUYWtlbisrO1xuXG4gICAgICAgICAgICAvLyBVcGRhdGUgbG9jYWwgYmFsYW5jZSBmb3Igc3Vic2VxdWVudCBjaGVja3MgaW4gdGhpcyBjeWNsZVxuICAgICAgICAgICAgY3VycmVudENyeXB0bzFCYWxhbmNlIC09IGFtb3VudENyeXB0bzFUb1NlbGw7XG4gICAgICAgICAgICBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgKz0gY3J5cHRvMlJlY2VpdmVkO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIikge1xuICAgICAgICAgIC8vIFNURVAgMjogRXZhbHVhdGUgVHJpZ2dlcmVkIFRhcmdldFJvd04ncyBTdGF0dXMgKFN0YWJsZWNvaW4gU3dhcCBNb2RlKVxuICAgICAgICAgIGlmIChhY3RpdmVSb3cuc3RhdHVzID09PSBcIkZyZWVcIikge1xuICAgICAgICAgICAgLy8gU1RFUCAyYTogRXhlY3V0ZSBUd28tU3RlcCBcIkJ1eSBDcnlwdG8xIHZpYSBTdGFibGVjb2luXCIgb24gVGFyZ2V0Um93TlxuICAgICAgICAgICAgY29uc3QgYW1vdW50Q3J5cHRvMlRvVXNlID0gYWN0aXZlUm93LnZhbHVlTGV2ZWw7IC8vIFZhbHVlID0gQmFzZUJpZCAqIChNdWx0aXBsaWVyIF4gTGV2ZWwpXG5cbiAgICAgICAgICAgIGlmIChjdXJyZW50Q3J5cHRvMkJhbGFuY2UgPj0gYW1vdW50Q3J5cHRvMlRvVXNlKSB7XG4gICAgICAgICAgICAgIC8vIFN0ZXAgMTogU2VsbCBDcnlwdG8yIGZvciBQcmVmZXJyZWRTdGFibGVjb2luXG4gICAgICAgICAgICAgIC8vIFVzZSBjdXJyZW50IGZsdWN0dWF0aW5nIG1hcmtldCBwcmljZSBmb3IgbW9yZSByZWFsaXN0aWMgUC9MXG4gICAgICAgICAgICAgIGNvbnN0IGNyeXB0bzJVU0RQcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8yIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICAgIGNvbnN0IHN0YWJsZWNvaW5VU0RQcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICAgIGNvbnN0IGNyeXB0bzJTdGFibGVjb2luUHJpY2UgPSBjcnlwdG8yVVNEUHJpY2UgLyBzdGFibGVjb2luVVNEUHJpY2U7XG4gICAgICAgICAgICAgIGNvbnN0IHN0YWJsZWNvaW5PYnRhaW5lZCA9IGFtb3VudENyeXB0bzJUb1VzZSAqIGNyeXB0bzJTdGFibGVjb2luUHJpY2U7XG5cbiAgICAgICAgICAgICAgLy8gU3RlcCAyOiBCdXkgQ3J5cHRvMSB3aXRoIFN0YWJsZWNvaW5cbiAgICAgICAgICAgICAgLy8gVXNlIGN1cnJlbnQgZmx1Y3R1YXRpbmcgbWFya2V0IHByaWNlICh0aGlzIGlzIHRoZSBrZXkgZm9yIFAvTCBjYWxjdWxhdGlvbilcbiAgICAgICAgICAgICAgY29uc3QgY3J5cHRvMVVTRFByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzEgfHwgJ0JUQycpO1xuICAgICAgICAgICAgICAvLyBBcHBseSBjdXJyZW50IG1hcmtldCBwcmljZSBmbHVjdHVhdGlvbiB0byBDcnlwdG8xIHByaWNlXG4gICAgICAgICAgICAgIGNvbnN0IGZsdWN0dWF0ZWRDcnlwdG8xUHJpY2UgPSBjcnlwdG8xVVNEUHJpY2UgKiAoc3RhdGUuY3VycmVudE1hcmtldFByaWNlIC8gKGNyeXB0bzFVU0RQcmljZSAvIGNyeXB0bzJVU0RQcmljZSkpO1xuICAgICAgICAgICAgICBjb25zdCBjcnlwdG8xU3RhYmxlY29pblByaWNlID0gZmx1Y3R1YXRlZENyeXB0bzFQcmljZSAvIHN0YWJsZWNvaW5VU0RQcmljZTtcbiAgICAgICAgICAgICAgY29uc3QgY3J5cHRvMUJvdWdodCA9IHN0YWJsZWNvaW5PYnRhaW5lZCAvIGNyeXB0bzFTdGFibGVjb2luUHJpY2U7XG5cbiAgICAgICAgICAgICAgLy8gVXBkYXRlIFJvdyBOOiBGcmVlIOKGkiBGdWxsLCBMZXZlbCsrLCBWYWx1ZSByZWNhbGN1bGF0ZWRcbiAgICAgICAgICAgICAgY29uc3QgbmV3TGV2ZWwgPSBhY3RpdmVSb3cub3JkZXJMZXZlbCArIDE7XG4gICAgICAgICAgICAgIGNvbnN0IG5ld1ZhbHVlID0gY29uZmlnLmJhc2VCaWQgKiBNYXRoLnBvdyhjb25maWcubXVsdGlwbGllciwgbmV3TGV2ZWwpO1xuXG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRSb3c6IFRhcmdldFByaWNlUm93ID0ge1xuICAgICAgICAgICAgICAgIC4uLmFjdGl2ZVJvdyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6IFwiRnVsbFwiLCAvLyBGcmVlIOKGkiBGdWxsXG4gICAgICAgICAgICAgICAgb3JkZXJMZXZlbDogbmV3TGV2ZWwsIC8vIExldmVsID0gTGV2ZWwgKyAxXG4gICAgICAgICAgICAgICAgdmFsdWVMZXZlbDogbmV3VmFsdWUsIC8vIFZhbHVlID0gQmFzZUJpZCAqIChNdWx0aXBsaWVyIF4gTGV2ZWwpXG4gICAgICAgICAgICAgICAgY3J5cHRvMUFtb3VudEhlbGQ6IGNyeXB0bzFCb3VnaHQsIC8vIFN0b3JlIGFtb3VudCBoZWxkIGZvciBOLTEgbG9naWNcbiAgICAgICAgICAgICAgICBvcmlnaW5hbENvc3RDcnlwdG8yOiBhbW91bnRDcnlwdG8yVG9Vc2UsIC8vIFN0b3JlIG9yaWdpbmFsIGNvc3QgZm9yIHByb2ZpdCBjYWxjdWxhdGlvblxuICAgICAgICAgICAgICAgIGNyeXB0bzFWYXI6IGNyeXB0bzFCb3VnaHQsIC8vIFBvc2l0aXZlIGFtb3VudCBvZiBDcnlwdG8xIGFjcXVpcmVkXG4gICAgICAgICAgICAgICAgY3J5cHRvMlZhcjogLWFtb3VudENyeXB0bzJUb1VzZSwgLy8gTmVnYXRpdmUgYW1vdW50IG9mIENyeXB0bzIgc29sZFxuICAgICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5KwIFN0YWJsZWNvaW5Td2FwIEJVWSAtIFNldHRpbmcgb3JpZ2luYWxDb3N0Q3J5cHRvMjpgLCB7XG4gICAgICAgICAgICAgICAgY291bnRlcjogYWN0aXZlUm93LmNvdW50ZXIsXG4gICAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMlRvVXNlOiBhbW91bnRDcnlwdG8yVG9Vc2UsXG4gICAgICAgICAgICAgICAgY3J5cHRvMUJvdWdodDogY3J5cHRvMUJvdWdodCxcbiAgICAgICAgICAgICAgICBvcmlnaW5hbENvc3RDcnlwdG8yOiBhbW91bnRDcnlwdG8yVG9Vc2UsXG4gICAgICAgICAgICAgICAgbmV3TGV2ZWw6IG5ld0xldmVsLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFTdGFibGVjb2luUHJpY2U6IGNyeXB0bzFTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgZmx1Y3R1YXRlZENyeXB0bzFQcmljZTogZmx1Y3R1YXRlZENyeXB0bzFQcmljZSxcbiAgICAgICAgICAgICAgICBjdXJyZW50TWFya2V0UHJpY2U6IHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZVxuICAgICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfVEFSR0VUX1BSSUNFX1JPVycsIHBheWxvYWQ6IHVwZGF0ZWRSb3cgfSk7XG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9CQUxBTkNFUycsIHBheWxvYWQ6IHsgY3J5cHRvMTogY3VycmVudENyeXB0bzFCYWxhbmNlICsgY3J5cHRvMUJvdWdodCwgY3J5cHRvMjogY3VycmVudENyeXB0bzJCYWxhbmNlIC0gYW1vdW50Q3J5cHRvMlRvVXNlIH0gfSk7XG5cbiAgICAgICAgICAgICAgLy8gQWRkIGhpc3RvcnkgZW50cmllcyBmb3IgYm90aCBzdGVwcyBvZiB0aGUgc3RhYmxlY29pbiBzd2FwXG4gICAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBQL0wgZm9yIHRoZSBjcnlwdG8yIHNlbGwgYmFzZWQgb24gcHJpY2UgZmx1Y3R1YXRpb25cbiAgICAgICAgICAgICAgY29uc3QgYmFzZUNyeXB0bzJQcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8yKSAvIGdldFVTRFByaWNlKGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luKTtcbiAgICAgICAgICAgICAgY29uc3QgZXN0aW1hdGVkUHJvZml0Q3J5cHRvMiA9IGFtb3VudENyeXB0bzJUb1VzZSAqIChjcnlwdG8yU3RhYmxlY29pblByaWNlIC0gYmFzZUNyeXB0bzJQcmljZSk7XG4gICAgICAgICAgICAgIGNvbnN0IGVzdGltYXRlZFByb2ZpdENyeXB0bzEgPSBlc3RpbWF0ZWRQcm9maXRDcnlwdG8yIC8gY3J5cHRvMVN0YWJsZWNvaW5QcmljZTtcblxuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiBTdGFibGVjb2luU3dhcCBTdGVwIEEgU0VMTCBQL0w6YCwge1xuICAgICAgICAgICAgICAgIGNyeXB0bzI6IGNvbmZpZy5jcnlwdG8yLFxuICAgICAgICAgICAgICAgIGFtb3VudFNvbGQ6IGFtb3VudENyeXB0bzJUb1VzZSxcbiAgICAgICAgICAgICAgICBjdXJyZW50UHJpY2U6IGNyeXB0bzJTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgYmFzZVByaWNlOiBiYXNlQ3J5cHRvMlByaWNlLFxuICAgICAgICAgICAgICAgIGVzdGltYXRlZFByb2ZpdENyeXB0bzI6IGVzdGltYXRlZFByb2ZpdENyeXB0bzIsXG4gICAgICAgICAgICAgICAgZXN0aW1hdGVkUHJvZml0Q3J5cHRvMTogZXN0aW1hdGVkUHJvZml0Q3J5cHRvMVxuICAgICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgICBwYWlyOiBgJHtjb25maWcuY3J5cHRvMn0vJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1gLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzIsXG4gICAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiU0VMTFwiLFxuICAgICAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMTogYW1vdW50Q3J5cHRvMlRvVXNlLFxuICAgICAgICAgICAgICAgICAgYXZnUHJpY2U6IGNyeXB0bzJTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgICB2YWx1ZUNyeXB0bzI6IHN0YWJsZWNvaW5PYnRhaW5lZCxcbiAgICAgICAgICAgICAgICAgIHByaWNlMTogY3J5cHRvMlN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8yIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMlN5bWJvbDogY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4gfHwgJycsXG4gICAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yOiBlc3RpbWF0ZWRQcm9maXRDcnlwdG8yLCAvLyBTaG93IGVzdGltYXRlZCBQL0wgYmFzZWQgb24gcHJpY2UgbW92ZW1lbnRcbiAgICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzE6IGVzdGltYXRlZFByb2ZpdENyeXB0bzFcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnQUREX09SREVSX0hJU1RPUllfRU5UUlknLFxuICAgICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICAgIHBhaXI6IGAke2NvbmZpZy5jcnlwdG8xfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMSxcbiAgICAgICAgICAgICAgICAgIG9yZGVyVHlwZTogXCJCVVlcIixcbiAgICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGNyeXB0bzFCb3VnaHQsXG4gICAgICAgICAgICAgICAgICBhdmdQcmljZTogY3J5cHRvMVN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbk9idGFpbmVkLFxuICAgICAgICAgICAgICAgICAgcHJpY2UxOiBjcnlwdG8xU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMVN5bWJvbDogY29uZmlnLmNyeXB0bzEgfHwgJycsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8yU3ltYm9sOiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYOKchSBTVEFCTEVDT0lOIEJVWTogQ291bnRlciAke2FjdGl2ZVJvdy5jb3VudGVyfSB8IFN0ZXAgMTogU29sZCAke2Ftb3VudENyeXB0bzJUb1VzZX0gJHtjb25maWcuY3J5cHRvMn0g4oaSICR7c3RhYmxlY29pbk9idGFpbmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59IHwgU3RlcCAyOiBCb3VnaHQgJHtjcnlwdG8xQm91Z2h0LnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IHwgTGV2ZWw6ICR7YWN0aXZlUm93Lm9yZGVyTGV2ZWx9IOKGkiAke25ld0xldmVsfWApO1xuICAgICAgICAgICAgICB0b2FzdCh7IHRpdGxlOiBcIkJVWSBFeGVjdXRlZCAoU3RhYmxlY29pbilcIiwgZGVzY3JpcHRpb246IGBDb3VudGVyICR7YWN0aXZlUm93LmNvdW50ZXJ9OiAke2NyeXB0bzFCb3VnaHQudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX0gdmlhICR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59YCwgZHVyYXRpb246IDIwMDAgfSk7XG4gICAgICAgICAgICAgIHBsYXlTb3VuZCgnc291bmRPcmRlckV4ZWN1dGlvbicpO1xuXG4gICAgICAgICAgICAgIC8vIFNlbmQgVGVsZWdyYW0gbm90aWZpY2F0aW9uIGZvciBTdGFibGVjb2luIEJVWVxuICAgICAgICAgICAgICBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24oXG4gICAgICAgICAgICAgICAgYPCfn6IgPGI+QlVZIEVYRUNVVEVEIChTdGFibGVjb2luIFN3YXApPC9iPlxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5OKIENvdW50ZXI6ICR7YWN0aXZlUm93LmNvdW50ZXJ9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCflIQgU3RlcCAxOiBTb2xkICR7YW1vdW50Q3J5cHRvMlRvVXNlLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9IOKGkiAke3N0YWJsZWNvaW5PYnRhaW5lZC50b0ZpeGVkKDIpfSAke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufVxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5SEIFN0ZXAgMjogQm91Z2h0ICR7Y3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfVxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5OKIExldmVsOiAke2FjdGl2ZVJvdy5vcmRlckxldmVsfSDihpIgJHtuZXdMZXZlbH1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+TiCBNb2RlOiBTdGFibGVjb2luIFN3YXBgXG4gICAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgICAgYWN0aW9uc1Rha2VuKys7XG5cbiAgICAgICAgICAgICAgLy8gVXBkYXRlIGxvY2FsIGJhbGFuY2UgZm9yIHN1YnNlcXVlbnQgY2hlY2tzIGluIHRoaXMgY3ljbGVcbiAgICAgICAgICAgICAgY3VycmVudENyeXB0bzJCYWxhbmNlIC09IGFtb3VudENyeXB0bzJUb1VzZTtcbiAgICAgICAgICAgICAgY3VycmVudENyeXB0bzFCYWxhbmNlICs9IGNyeXB0bzFCb3VnaHQ7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAvLyBJbnN1ZmZpY2llbnQgYmFsYW5jZSAtIHNlbmQgVGVsZWdyYW0gbm90aWZpY2F0aW9uXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinYwgSW5zdWZmaWNpZW50ICR7Y29uZmlnLmNyeXB0bzJ9IGJhbGFuY2UgZm9yIHN0YWJsZWNvaW4gc3dhcDogJHtjdXJyZW50Q3J5cHRvMkJhbGFuY2UudG9GaXhlZCg2KX0gPCAke2Ftb3VudENyeXB0bzJUb1VzZS50b0ZpeGVkKDYpfWApO1xuICAgICAgICAgICAgICBzZW5kTG93QmFsYW5jZU5vdGlmaWNhdGlvbihjb25maWcuY3J5cHRvMiwgY3VycmVudENyeXB0bzJCYWxhbmNlLCBhbW91bnRDcnlwdG8yVG9Vc2UpLmNhdGNoKGNvbnNvbGUuZXJyb3IpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFNURVAgMzogQ2hlY2sgYW5kIFByb2Nlc3MgSW5mZXJpb3IgVGFyZ2V0Um93IChUYXJnZXRSb3dOX21pbnVzXzEpIC0gQUxXQVlTLCByZWdhcmRsZXNzIG9mIEJVWVxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRDb3VudGVyID0gYWN0aXZlUm93LmNvdW50ZXI7XG4gICAgICAgICAgY29uc3QgaW5mZXJpb3JSb3cgPSBzb3J0ZWRSb3dzRm9yTG9naWMuZmluZChyb3cgPT4gcm93LmNvdW50ZXIgPT09IGN1cnJlbnRDb3VudGVyIC0gMSk7XG5cbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBTdGFibGVjb2luU3dhcCBTRUxMIENoZWNrOmAsIHtcbiAgICAgICAgICAgIGN1cnJlbnRDb3VudGVyOiBjdXJyZW50Q291bnRlcixcbiAgICAgICAgICAgIGluZmVyaW9yUm93Rm91bmQ6ICEhaW5mZXJpb3JSb3csXG4gICAgICAgICAgICBpbmZlcmlvclJvd1N0YXR1czogaW5mZXJpb3JSb3c/LnN0YXR1cyxcbiAgICAgICAgICAgIGluZmVyaW9yUm93Q3J5cHRvMUhlbGQ6IGluZmVyaW9yUm93Py5jcnlwdG8xQW1vdW50SGVsZCxcbiAgICAgICAgICAgIGluZmVyaW9yUm93T3JpZ2luYWxDb3N0OiBpbmZlcmlvclJvdz8ub3JpZ2luYWxDb3N0Q3J5cHRvMixcbiAgICAgICAgICAgIGNhbkV4ZWN1dGVTZWxsOiAhIShpbmZlcmlvclJvdyAmJiBpbmZlcmlvclJvdy5zdGF0dXMgPT09IFwiRnVsbFwiICYmIGluZmVyaW9yUm93LmNyeXB0bzFBbW91bnRIZWxkICYmIGluZmVyaW9yUm93Lm9yaWdpbmFsQ29zdENyeXB0bzIpXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBpZiAoaW5mZXJpb3JSb3cgJiYgaW5mZXJpb3JSb3cuc3RhdHVzID09PSBcIkZ1bGxcIiAmJiBpbmZlcmlvclJvdy5jcnlwdG8xQW1vdW50SGVsZCAmJiBpbmZlcmlvclJvdy5vcmlnaW5hbENvc3RDcnlwdG8yKSB7XG4gICAgICAgICAgICAvLyBFeGVjdXRlIFR3by1TdGVwIFwiU2VsbCBDcnlwdG8xICYgUmVhY3F1aXJlIENyeXB0bzIgdmlhIFN0YWJsZWNvaW5cIiBmb3IgVGFyZ2V0Um93Tl9taW51c18xXG4gICAgICAgICAgICBjb25zdCBhbW91bnRDcnlwdG8xVG9TZWxsID0gaW5mZXJpb3JSb3cuY3J5cHRvMUFtb3VudEhlbGQ7XG5cbiAgICAgICAgICAgIC8vIFN0ZXAgQTogU2VsbCBDcnlwdG8xIGZvciBQcmVmZXJyZWRTdGFibGVjb2luXG4gICAgICAgICAgICAvLyBVc2UgY3VycmVudCBmbHVjdHVhdGluZyBtYXJrZXQgcHJpY2UgZm9yIHJlYWxpc3RpYyBQL0xcbiAgICAgICAgICAgIGNvbnN0IGNyeXB0bzFVU0RQcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8xIHx8ICdCVEMnKTtcbiAgICAgICAgICAgIGNvbnN0IGNyeXB0bzJVU0RQcmljZSA9IGdldFVTRFByaWNlKGNvbmZpZy5jcnlwdG8yIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICBjb25zdCBzdGFibGVjb2luVVNEUHJpY2UgPSBnZXRVU0RQcmljZShjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnVVNEVCcpO1xuXG4gICAgICAgICAgICAvLyBBcHBseSBjdXJyZW50IG1hcmtldCBwcmljZSBmbHVjdHVhdGlvbiB0byBDcnlwdG8xIHByaWNlXG4gICAgICAgICAgICBjb25zdCBmbHVjdHVhdGVkQ3J5cHRvMVByaWNlID0gY3J5cHRvMVVTRFByaWNlICogKHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSAvIChjcnlwdG8xVVNEUHJpY2UgLyBjcnlwdG8yVVNEUHJpY2UpKTtcbiAgICAgICAgICAgIGNvbnN0IGNyeXB0bzFTdGFibGVjb2luUHJpY2UgPSBmbHVjdHVhdGVkQ3J5cHRvMVByaWNlIC8gc3RhYmxlY29pblVTRFByaWNlO1xuICAgICAgICAgICAgY29uc3Qgc3RhYmxlY29pbkZyb21DMVNlbGwgPSBhbW91bnRDcnlwdG8xVG9TZWxsICogY3J5cHRvMVN0YWJsZWNvaW5QcmljZTtcblxuICAgICAgICAgICAgLy8gU3RlcCBCOiBCdXkgQ3J5cHRvMiB3aXRoIFN0YWJsZWNvaW5cbiAgICAgICAgICAgIGNvbnN0IGNyeXB0bzJTdGFibGVjb2luUHJpY2UgPSBjcnlwdG8yVVNEUHJpY2UgLyBzdGFibGVjb2luVVNEUHJpY2U7XG4gICAgICAgICAgICBjb25zdCBjcnlwdG8yUmVhY3F1aXJlZCA9IHN0YWJsZWNvaW5Gcm9tQzFTZWxsIC8gY3J5cHRvMlN0YWJsZWNvaW5QcmljZTtcblxuICAgICAgICAgICAgLy8gQ2FsY3VsYXRlIHJlYWxpemVkIHByb2ZpdCBpbiBDcnlwdG8yXG4gICAgICAgICAgICAvLyBXZSBjb21wYXJlIHRoZSB2YWx1ZSBvZiBjcnlwdG8yIHdlIGdvdCBiYWNrIHZzIHdoYXQgd2Ugb3JpZ2luYWxseSBzcGVudFxuICAgICAgICAgICAgY29uc3Qgb3JpZ2luYWxDb3N0ID0gaW5mZXJpb3JSb3cub3JpZ2luYWxDb3N0Q3J5cHRvMiB8fCAwO1xuICAgICAgICAgICAgY29uc3QgcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPSBjcnlwdG8yUmVhY3F1aXJlZCAtIG9yaWdpbmFsQ29zdDtcblxuICAgICAgICAgICAgLy8gQ2FsY3VsYXRlIENyeXB0bzEgcHJvZml0L2xvc3MgLSB0aGlzIHNob3VsZCBiZSB0aGUgcHJvZml0IGluIHRlcm1zIG9mIENyeXB0bzFcbiAgICAgICAgICAgIC8vIEZvciBTdGFibGVjb2luU3dhcCwgd2UgbmVlZCB0byBjYWxjdWxhdGUgaG93IG11Y2ggQ3J5cHRvMSBwcm9maXQgdGhpcyByZXByZXNlbnRzXG4gICAgICAgICAgICBjb25zdCByZWFsaXplZFByb2ZpdENyeXB0bzEgPSBjcnlwdG8xU3RhYmxlY29pblByaWNlID4gMCA/IHJlYWxpemVkUHJvZml0SW5DcnlwdG8yIC8gY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA6IDA7XG5cbiAgICAgICAgICAgIC8vIElNUE9SVEFOVDogSWYgUC9MIGlzIGV4YWN0bHkgMCwgaXQgbWlnaHQgaW5kaWNhdGUgYSBwcmljZSBjYWxjdWxhdGlvbiBpc3N1ZVxuICAgICAgICAgICAgaWYgKHJlYWxpemVkUHJvZml0SW5DcnlwdG8yID09PSAwKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUud2Fybihg4pqg77iPIFAvTCBpcyBleGFjdGx5IDAgLSB0aGlzIG1pZ2h0IGluZGljYXRlIGFuIGlzc3VlOlxuICAgICAgICAgICAgICAgIC0gQXJlIHByaWNlcyBjaGFuZ2luZyBiZXR3ZWVuIEJVWSBhbmQgU0VMTD9cbiAgICAgICAgICAgICAgICAtIElzIHRoZSBtYXJrZXQgcHJpY2UgZmx1Y3R1YXRpb24gd29ya2luZz9cbiAgICAgICAgICAgICAgICAtIEN1cnJlbnQgbWFya2V0IHByaWNlOiAke3N0YXRlLmN1cnJlbnRNYXJrZXRQcmljZX1gKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfkrAgU3RhYmxlY29pblN3YXAgUC9MIENhbGN1bGF0aW9uIERFVEFJTEVEOlxuICAgICAgICAgICAgICAtIEluZmVyaW9yIFJvdyBDb3VudGVyOiAke2luZmVyaW9yUm93LmNvdW50ZXJ9XG4gICAgICAgICAgICAgIC0gT3JpZ2luYWwgQ29zdCAoQ3J5cHRvMik6ICR7b3JpZ2luYWxDb3N0fVxuICAgICAgICAgICAgICAtIENyeXB0bzIgUmVhY3F1aXJlZDogJHtjcnlwdG8yUmVhY3F1aXJlZH1cbiAgICAgICAgICAgICAgLSBSZWFsaXplZCBQL0wgKENyeXB0bzIpOiAke3JlYWxpemVkUHJvZml0SW5DcnlwdG8yfVxuICAgICAgICAgICAgICAtIENyeXB0bzEgU3RhYmxlY29pbiBQcmljZTogJHtjcnlwdG8xU3RhYmxlY29pblByaWNlfVxuICAgICAgICAgICAgICAtIFJlYWxpemVkIFAvTCAoQ3J5cHRvMSk6ICR7cmVhbGl6ZWRQcm9maXRDcnlwdG8xfVxuICAgICAgICAgICAgICAtIElzIFByb2ZpdGFibGU6ICR7cmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgPiAwID8gJ1lFUycgOiAnTk8nfVxuICAgICAgICAgICAgICAtIENhbGN1bGF0aW9uOiAke2NyeXB0bzJSZWFjcXVpcmVkfSAtICR7b3JpZ2luYWxDb3N0fSA9ICR7cmVhbGl6ZWRQcm9maXRJbkNyeXB0bzJ9YCk7XG5cbiAgICAgICAgICAgIC8vIFVwZGF0ZSBSb3cgTi0xOiBGdWxsIOKGkiBGcmVlLCBMZXZlbCBVTkNIQU5HRUQsIFZhcnMgY2xlYXJlZFxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZEluZmVyaW9yUm93OiBUYXJnZXRQcmljZVJvdyA9IHtcbiAgICAgICAgICAgICAgLi4uaW5mZXJpb3JSb3csXG4gICAgICAgICAgICAgIHN0YXR1czogXCJGcmVlXCIsIC8vIEZ1bGwg4oaSIEZyZWVcbiAgICAgICAgICAgICAgLy8gb3JkZXJMZXZlbDogUkVNQUlOUyBVTkNIQU5HRUQgcGVyIHNwZWNpZmljYXRpb25cbiAgICAgICAgICAgICAgY3J5cHRvMUFtb3VudEhlbGQ6IHVuZGVmaW5lZCwgLy8gQ2xlYXIgaGVsZCBhbW91bnRcbiAgICAgICAgICAgICAgb3JpZ2luYWxDb3N0Q3J5cHRvMjogdW5kZWZpbmVkLCAvLyBDbGVhciBvcmlnaW5hbCBjb3N0XG4gICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIGluZmVyaW9yUm93Lm9yZGVyTGV2ZWwpLCAvLyBSZWNhbGN1bGF0ZSB2YWx1ZSBiYXNlZCBvbiBjdXJyZW50IGxldmVsXG4gICAgICAgICAgICAgIGNyeXB0bzFWYXI6IDAsIC8vIENsZWFyIHRvIDAuMDAgKEZyZWUgc3RhdHVzKVxuICAgICAgICAgICAgICBjcnlwdG8yVmFyOiAwLCAvLyBDbGVhciB0byAwLjAwIChGcmVlIHN0YXR1cylcbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZEluZmVyaW9yUm93IH0pO1xuICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgLSBhbW91bnRDcnlwdG8xVG9TZWxsLCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgKyBjcnlwdG8yUmVhY3F1aXJlZCB9IH0pO1xuXG4gICAgICAgICAgICAvLyBBZGQgaGlzdG9yeSBlbnRyaWVzIGZvciBib3RoIHN0ZXBzIG9mIHRoZSBOLTEgc3RhYmxlY29pbiBzd2FwXG4gICAgICAgICAgICAvLyBTdGVwIEE6IFNFTEwgQ3J5cHRvMSBmb3IgU3RhYmxlY29pbiAod2l0aCBwcm9maXQvbG9zcyBkYXRhIGZvciBhbmFseXRpY3MpXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiBBZGRpbmcgU3RhYmxlY29pblN3YXAgU0VMTCBvcmRlciB0byBoaXN0b3J5IHdpdGggUC9MOmAsIHtcbiAgICAgICAgICAgICAgcmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMjogcmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIsXG4gICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzE6IHJlYWxpemVkUHJvZml0Q3J5cHRvMSxcbiAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59YCxcbiAgICAgICAgICAgICAgb3JkZXJUeXBlOiBcIlNFTExcIixcbiAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMTogYW1vdW50Q3J5cHRvMVRvU2VsbCxcbiAgICAgICAgICAgICAgYXZnUHJpY2U6IGNyeXB0bzFTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbkZyb21DMVNlbGwsXG4gICAgICAgICAgICAgIHByaWNlMTogY3J5cHRvMVN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgY3J5cHRvMVN5bWJvbDogY29uZmlnLmNyeXB0bzEsXG4gICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luXG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgZGlzcGF0Y2goe1xuICAgICAgICAgICAgICB0eXBlOiAnQUREX09SREVSX0hJU1RPUllfRU5UUlknLFxuICAgICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgICAgaWQ6IHV1aWR2NCgpLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICBwYWlyOiBgJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1gLFxuICAgICAgICAgICAgICAgIGNyeXB0bzE6IGNvbmZpZy5jcnlwdG8xLFxuICAgICAgICAgICAgICAgIG9yZGVyVHlwZTogXCJTRUxMXCIsXG4gICAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMTogYW1vdW50Q3J5cHRvMVRvU2VsbCxcbiAgICAgICAgICAgICAgICBhdmdQcmljZTogY3J5cHRvMVN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICB2YWx1ZUNyeXB0bzI6IHN0YWJsZWNvaW5Gcm9tQzFTZWxsLFxuICAgICAgICAgICAgICAgIHByaWNlMTogY3J5cHRvMVN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICBjcnlwdG8xU3ltYm9sOiBjb25maWcuY3J5cHRvMSB8fCAnJyxcbiAgICAgICAgICAgICAgICBjcnlwdG8yU3ltYm9sOiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbiB8fCAnJyxcbiAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yOiByZWFsaXplZFByb2ZpdEluQ3J5cHRvMixcbiAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8xOiByZWFsaXplZFByb2ZpdENyeXB0bzFcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIC8vIFN0ZXAgQjogQlVZIENyeXB0bzIgd2l0aCBTdGFibGVjb2luIChubyBwcm9maXQvbG9zcyBhcyBpdCdzIGp1c3QgY29udmVyc2lvbilcbiAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzJ9LyR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59YCxcbiAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMixcbiAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiQlVZXCIsXG4gICAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMTogY3J5cHRvMlJlYWNxdWlyZWQsXG4gICAgICAgICAgICAgICAgYXZnUHJpY2U6IGNyeXB0bzJTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgdmFsdWVDcnlwdG8yOiBzdGFibGVjb2luRnJvbUMxU2VsbCxcbiAgICAgICAgICAgICAgICBwcmljZTE6IGNyeXB0bzJTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgY3J5cHRvMVN5bWJvbDogY29uZmlnLmNyeXB0bzIgfHwgJycsXG4gICAgICAgICAgICAgICAgY3J5cHRvMlN5bWJvbDogY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4gfHwgJydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgU1RBQkxFQ09JTiBTRUxMOiBDb3VudGVyICR7Y3VycmVudENvdW50ZXIgLSAxfSB8IFN0ZXAgQTogU29sZCAke2Ftb3VudENyeXB0bzFUb1NlbGwudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX0g4oaSICR7c3RhYmxlY29pbkZyb21DMVNlbGwudG9GaXhlZCgyKX0gJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn0gfCBTdGVwIEI6IEJvdWdodCAke2NyeXB0bzJSZWFjcXVpcmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9IHwgUHJvZml0OiAke3JlYWxpemVkUHJvZml0SW5DcnlwdG8yLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9IHwgTGV2ZWw6ICR7aW5mZXJpb3JSb3cub3JkZXJMZXZlbH0gKHVuY2hhbmdlZClgKTtcbiAgICAgICAgICAgIHRvYXN0KHsgdGl0bGU6IFwiU0VMTCBFeGVjdXRlZCAoU3RhYmxlY29pbilcIiwgZGVzY3JpcHRpb246IGBDb3VudGVyICR7Y3VycmVudENvdW50ZXIgLSAxfTogUHJvZml0ICR7cmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn0gdmlhICR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59YCwgZHVyYXRpb246IDIwMDAgfSk7XG4gICAgICAgICAgICBwbGF5U291bmQoJ3NvdW5kT3JkZXJFeGVjdXRpb24nKTtcblxuICAgICAgICAgICAgLy8gU2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb24gZm9yIFN0YWJsZWNvaW4gU0VMTFxuICAgICAgICAgICAgY29uc3QgcHJvZml0RW1vamkgPSByZWFsaXplZFByb2ZpdEluQ3J5cHRvMiA+IDAgPyAn8J+TiCcgOiByZWFsaXplZFByb2ZpdEluQ3J5cHRvMiA8IDAgPyAn8J+TiScgOiAn4p6WJztcbiAgICAgICAgICAgIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbihcbiAgICAgICAgICAgICAgYPCflLQgPGI+U0VMTCBFWEVDVVRFRCAoU3RhYmxlY29pbiBTd2FwKTwvYj5cXG5gICtcbiAgICAgICAgICAgICAgYPCfk4ogQ291bnRlcjogJHtjdXJyZW50Q291bnRlciAtIDF9XFxuYCArXG4gICAgICAgICAgICAgIGDwn5SEIFN0ZXAgQTogU29sZCAke2Ftb3VudENyeXB0bzFUb1NlbGwudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX0g4oaSICR7c3RhYmxlY29pbkZyb21DMVNlbGwudG9GaXhlZCgyKX0gJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1cXG5gICtcbiAgICAgICAgICAgICAgYPCflIQgU3RlcCBCOiBCb3VnaHQgJHtjcnlwdG8yUmVhY3F1aXJlZC50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfVxcbmAgK1xuICAgICAgICAgICAgICBgJHtwcm9maXRFbW9qaX0gUHJvZml0OiAke3JlYWxpemVkUHJvZml0SW5DcnlwdG8yLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9XFxuYCArXG4gICAgICAgICAgICAgIGDwn5OKIExldmVsOiAke2luZmVyaW9yUm93Lm9yZGVyTGV2ZWx9ICh1bmNoYW5nZWQpXFxuYCArXG4gICAgICAgICAgICAgIGDwn5OIIE1vZGU6IFN0YWJsZWNvaW4gU3dhcGBcbiAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgIGFjdGlvbnNUYWtlbisrO1xuXG4gICAgICAgICAgICAvLyBVcGRhdGUgbG9jYWwgYmFsYW5jZSBmb3Igc3Vic2VxdWVudCBjaGVja3MgaW4gdGhpcyBjeWNsZVxuICAgICAgICAgICAgY3VycmVudENyeXB0bzFCYWxhbmNlIC09IGFtb3VudENyeXB0bzFUb1NlbGw7XG4gICAgICAgICAgICBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgKz0gY3J5cHRvMlJlYWNxdWlyZWQ7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGFjdGlvbnNUYWtlbiA+IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn46vIENZQ0xFIENPTVBMRVRFOiAke2FjdGlvbnNUYWtlbn0gYWN0aW9ucyB0YWtlbiBhdCBwcmljZSAkJHtjdXJyZW50TWFya2V0UHJpY2UudG9GaXhlZCgyKX1gKTtcbiAgICB9XG5cbiAgfSwgW3N0YXRlLmJvdFN5c3RlbVN0YXR1cywgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLCBzdGF0ZS50YXJnZXRQcmljZVJvd3MsIHN0YXRlLmNvbmZpZywgc3RhdGUuY3J5cHRvMUJhbGFuY2UsIHN0YXRlLmNyeXB0bzJCYWxhbmNlLCBzdGF0ZS5zdGFibGVjb2luQmFsYW5jZSwgZGlzcGF0Y2gsIHRvYXN0LCBwbGF5U291bmQsIHNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbl0pO1xuXG5cbiAgY29uc3QgZ2V0RGlzcGxheU9yZGVycyA9IHVzZUNhbGxiYWNrKCgpOiBEaXNwbGF5T3JkZXJSb3dbXSA9PiB7XG4gICAgaWYgKCFzdGF0ZS50YXJnZXRQcmljZVJvd3MgfHwgIUFycmF5LmlzQXJyYXkoc3RhdGUudGFyZ2V0UHJpY2VSb3dzKSkge1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIHJldHVybiBzdGF0ZS50YXJnZXRQcmljZVJvd3MubWFwKChyb3c6IFRhcmdldFByaWNlUm93KSA9PiB7XG4gICAgICBjb25zdCBjdXJyZW50UHJpY2UgPSBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UgfHwgMDtcbiAgICAgIGNvbnN0IHRhcmdldFByaWNlID0gcm93LnRhcmdldFByaWNlIHx8IDA7XG4gICAgICBjb25zdCBwZXJjZW50RnJvbUFjdHVhbFByaWNlID0gY3VycmVudFByaWNlICYmIHRhcmdldFByaWNlXG4gICAgICAgID8gKChjdXJyZW50UHJpY2UgLyB0YXJnZXRQcmljZSkgLSAxKSAqIDEwMFxuICAgICAgICA6IDA7XG5cbiAgICAgIGxldCBpbmNvbWVDcnlwdG8xOiBudW1iZXIgfCB1bmRlZmluZWQ7XG4gICAgICBsZXQgaW5jb21lQ3J5cHRvMjogbnVtYmVyIHwgdW5kZWZpbmVkO1xuXG4gICAgICBpZiAocm93LnN0YXR1cyA9PT0gXCJGdWxsXCIgJiYgcm93LmNyeXB0bzFBbW91bnRIZWxkICYmIHJvdy5vcmlnaW5hbENvc3RDcnlwdG8yKSB7XG4gICAgICAgIGNvbnN0IHRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMiA9IChjdXJyZW50UHJpY2UgKiByb3cuY3J5cHRvMUFtb3VudEhlbGQpIC0gcm93Lm9yaWdpbmFsQ29zdENyeXB0bzI7XG4gICAgICAgIGluY29tZUNyeXB0bzIgPSAodG90YWxVbnJlYWxpemVkUHJvZml0SW5DcnlwdG8yICogc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQpIC8gMTAwO1xuICAgICAgICBpZiAoY3VycmVudFByaWNlID4gMCkge1xuICAgICAgICAgIGluY29tZUNyeXB0bzEgPSAodG90YWxVbnJlYWxpemVkUHJvZml0SW5DcnlwdG8yICogc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQgLyAxMDApIC8gY3VycmVudFByaWNlO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnJvdyxcbiAgICAgICAgY3VycmVudFByaWNlLFxuICAgICAgICBwcmljZURpZmZlcmVuY2U6IHRhcmdldFByaWNlIC0gY3VycmVudFByaWNlLFxuICAgICAgICBwcmljZURpZmZlcmVuY2VQZXJjZW50OiBjdXJyZW50UHJpY2UgPiAwID8gKCh0YXJnZXRQcmljZSAtIGN1cnJlbnRQcmljZSkgLyBjdXJyZW50UHJpY2UpICogMTAwIDogMCxcbiAgICAgICAgcG90ZW50aWFsUHJvZml0Q3J5cHRvMTogKHN0YXRlLmNvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50IC8gMTAwKSAqIHJvdy52YWx1ZUxldmVsIC8gKHRhcmdldFByaWNlIHx8IDEpLFxuICAgICAgICBwb3RlbnRpYWxQcm9maXRDcnlwdG8yOiAoc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQgLyAxMDApICogcm93LnZhbHVlTGV2ZWwsXG4gICAgICAgIHBlcmNlbnRGcm9tQWN0dWFsUHJpY2UsXG4gICAgICAgIGluY29tZUNyeXB0bzEsXG4gICAgICAgIGluY29tZUNyeXB0bzIsXG4gICAgICB9O1xuICAgIH0pLnNvcnQoKGE6IERpc3BsYXlPcmRlclJvdywgYjogRGlzcGxheU9yZGVyUm93KSA9PiBiLnRhcmdldFByaWNlIC0gYS50YXJnZXRQcmljZSk7XG4gIH0sIFtzdGF0ZS50YXJnZXRQcmljZVJvd3MsIHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSwgc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQsIHN0YXRlLmNvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50LCBzdGF0ZS5jb25maWcuYmFzZUJpZCwgc3RhdGUuY29uZmlnLm11bHRpcGxpZXJdKTtcblxuXG4gIC8vIEJhY2tlbmQgSW50ZWdyYXRpb24gRnVuY3Rpb25zXG4gIGNvbnN0IHNhdmVDb25maWdUb0JhY2tlbmQgPSB1c2VDYWxsYmFjayhhc3luYyAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNvbmZpZ0RhdGEgPSB7XG4gICAgICAgIG5hbWU6IGAke2NvbmZpZy5jcnlwdG8xfS8ke2NvbmZpZy5jcnlwdG8yfSAke2NvbmZpZy50cmFkaW5nTW9kZX1gLFxuICAgICAgICB0cmFkaW5nTW9kZTogY29uZmlnLnRyYWRpbmdNb2RlLFxuICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMSxcbiAgICAgICAgY3J5cHRvMjogY29uZmlnLmNyeXB0bzIsXG4gICAgICAgIGJhc2VCaWQ6IGNvbmZpZy5iYXNlQmlkLFxuICAgICAgICBtdWx0aXBsaWVyOiBjb25maWcubXVsdGlwbGllcixcbiAgICAgICAgbnVtRGlnaXRzOiBjb25maWcubnVtRGlnaXRzLFxuICAgICAgICBzbGlwcGFnZVBlcmNlbnQ6IGNvbmZpZy5zbGlwcGFnZVBlcmNlbnQsXG4gICAgICAgIGluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQ6IGNvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50LFxuICAgICAgICBpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50OiBjb25maWcuaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCxcbiAgICAgICAgcHJlZmVycmVkU3RhYmxlY29pbjogY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4sXG4gICAgICAgIHRhcmdldFByaWNlczogc3RhdGUudGFyZ2V0UHJpY2VSb3dzLm1hcChyb3cgPT4gcm93LnRhcmdldFByaWNlKVxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0cmFkaW5nQXBpLnNhdmVDb25maWcoY29uZmlnRGF0YSk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIENvbmZpZyBzYXZlZCB0byBiYWNrZW5kOicsIHJlc3BvbnNlKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5jb25maWc/LmlkIHx8IG51bGw7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gc2F2ZSBjb25maWcgdG8gYmFja2VuZDonLCBlcnJvcik7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkJhY2tlbmQgRXJyb3JcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIHNhdmUgY29uZmlndXJhdGlvbiB0byBiYWNrZW5kXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgICAgZHVyYXRpb246IDMwMDBcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9LCBbc3RhdGUudGFyZ2V0UHJpY2VSb3dzLCB0b2FzdF0pO1xuXG4gIGNvbnN0IHN0YXJ0QmFja2VuZEJvdCA9IHVzZUNhbGxiYWNrKGFzeW5jIChjb25maWdJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdHJhZGluZ0FwaS5zdGFydEJvdChjb25maWdJZCk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIEJvdCBzdGFydGVkIG9uIGJhY2tlbmQ6JywgcmVzcG9uc2UpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJCb3QgU3RhcnRlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJUcmFkaW5nIGJvdCBzdGFydGVkIHN1Y2Nlc3NmdWxseSBvbiBiYWNrZW5kXCIsXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICB9KTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIHN0YXJ0IGJvdCBvbiBiYWNrZW5kOicsIGVycm9yKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiQmFja2VuZCBFcnJvclwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gc3RhcnQgYm90IG9uIGJhY2tlbmRcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgICBkdXJhdGlvbjogMzAwMFxuICAgICAgfSk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9LCBbdG9hc3RdKTtcblxuICBjb25zdCBzdG9wQmFja2VuZEJvdCA9IHVzZUNhbGxiYWNrKGFzeW5jIChjb25maWdJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdHJhZGluZ0FwaS5zdG9wQm90KGNvbmZpZ0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQm90IHN0b3BwZWQgb24gYmFja2VuZDonLCByZXNwb25zZSk7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkJvdCBTdG9wcGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlRyYWRpbmcgYm90IHN0b3BwZWQgc3VjY2Vzc2Z1bGx5IG9uIGJhY2tlbmRcIixcbiAgICAgICAgZHVyYXRpb246IDMwMDBcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gc3RvcCBib3Qgb24gYmFja2VuZDonLCBlcnJvcik7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkJhY2tlbmQgRXJyb3JcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIHN0b3AgYm90IG9uIGJhY2tlbmRcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgICBkdXJhdGlvbjogMzAwMFxuICAgICAgfSk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9LCBbdG9hc3RdKTtcblxuICBjb25zdCBjaGVja0JhY2tlbmRTdGF0dXMgPSB1c2VDYWxsYmFjayhhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgY29uc3QgYXBpVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTDtcbiAgICBpZiAoIWFwaVVybCkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3I6IE5FWFRfUFVCTElDX0FQSV9VUkwgaXMgbm90IGRlZmluZWQuIEJhY2tlbmQgY29ubmVjdGl2aXR5IGNoZWNrIGNhbm5vdCBiZSBwZXJmb3JtZWQuJyk7XG4gICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnLCBwYXlsb2FkOiAnb2ZmbGluZScgfSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBoZWFsdGhSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2FwaVVybH0vaGVhbHRoL2ApO1xuICAgICAgaWYgKCFoZWFsdGhSZXNwb25zZS5vaykge1xuICAgICAgICAvLyBMb2cgbW9yZSBkZXRhaWxzIGlmIHRoZSByZXNwb25zZSB3YXMgcmVjZWl2ZWQgYnV0IG5vdCBPS1xuICAgICAgICBjb25zb2xlLmVycm9yKGBCYWNrZW5kIGhlYWx0aCBjaGVjayBmYWlsZWQgd2l0aCBzdGF0dXM6ICR7aGVhbHRoUmVzcG9uc2Uuc3RhdHVzfSAke2hlYWx0aFJlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlVGV4dCA9IGF3YWl0IGhlYWx0aFJlc3BvbnNlLnRleHQoKS5jYXRjaCgoKSA9PiAnQ291bGQgbm90IHJlYWQgcmVzcG9uc2UgdGV4dC4nKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignQmFja2VuZCBoZWFsdGggY2hlY2sgcmVzcG9uc2UgYm9keTonLCByZXNwb25zZVRleHQpO1xuICAgICAgfVxuICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0JBQ0tFTkRfU1RBVFVTJywgcGF5bG9hZDogaGVhbHRoUmVzcG9uc2Uub2sgPyAnb25saW5lJyA6ICdvZmZsaW5lJyB9KTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnLCBwYXlsb2FkOiAnb2ZmbGluZScgfSk7XG4gICAgICBjb25zb2xlLmVycm9yKCdCYWNrZW5kIGNvbm5lY3Rpdml0eSBjaGVjayBmYWlsZWQuIEVycm9yIGRldGFpbHM6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yLmNhdXNlKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZldGNoIGVycm9yIGNhdXNlOicsIGVycm9yLmNhdXNlKTtcbiAgICAgIH1cbiAgICAgIC8vIEl0J3MgYWxzbyB1c2VmdWwgdG8gbG9nIHRoZSBhcGlVcmwgdG8gZW5zdXJlIGl0J3Mgd2hhdCB3ZSBleHBlY3RcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0F0dGVtcHRlZCB0byBmZXRjaCBBUEkgVVJMOicsIGAke2FwaVVybH0vaGVhbHRoL2ApO1xuICAgIH1cbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBiYWNrZW5kIHN0YXR1cyBjaGVjayAob25lLXRpbWUgb25seSlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsIGNoZWNrIGZvciBiYWNrZW5kIHN0YXR1cyBvbmx5XG4gICAgY2hlY2tCYWNrZW5kU3RhdHVzKCk7XG4gIH0sIFtjaGVja0JhY2tlbmRTdGF0dXNdKTtcblxuICAvLyBTYXZlIHN0YXRlIHRvIGxvY2FsU3RvcmFnZSB3aGVuZXZlciBpdCBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2F2ZVN0YXRlVG9Mb2NhbFN0b3JhZ2Uoc3RhdGUpO1xuICB9LCBbc3RhdGVdKTtcblxuXG4gIC8vIEVmZmVjdCB0byBoYW5kbGUgYm90IHdhcm0tdXAgcGVyaW9kIChpbW1lZGlhdGUgZXhlY3V0aW9uKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdXYXJtaW5nVXAnKSB7XG4gICAgICBjb25zb2xlLmxvZyhcIkJvdCBpcyBXYXJtaW5nIFVwLi4uIEltbWVkaWF0ZSBleGVjdXRpb24gZW5hYmxlZC5cIik7XG4gICAgICAvLyBJbW1lZGlhdGUgdHJhbnNpdGlvbiB0byBSdW5uaW5nIHN0YXRlIC0gbm8gZGVsYXlzXG4gICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTWVNURU1fQ09NUExFVEVfV0FSTVVQJyB9KTtcbiAgICAgIGNvbnNvbGUubG9nKFwiQm90IGlzIG5vdyBSdW5uaW5nIGltbWVkaWF0ZWx5LlwiKTtcbiAgICB9XG4gIH0sIFtzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMsIGRpc3BhdGNoXSk7XG5cbiAgLy8gRWZmZWN0IHRvIGhhbmRsZSBzZXNzaW9uIHJ1bnRpbWUgdHJhY2tpbmdcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzZXNzaW9uTWFuYWdlciA9IFNlc3Npb25NYW5hZ2VyLmdldEluc3RhbmNlKCk7XG4gICAgY29uc3QgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25NYW5hZ2VyLmdldEN1cnJlbnRTZXNzaW9uSWQoKTtcblxuICAgIGlmIChjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycpIHtcbiAgICAgICAgLy8gQm90IHN0YXJ0ZWQgcnVubmluZywgc3RhcnQgcnVudGltZSB0cmFja2luZ1xuICAgICAgICBzZXNzaW9uTWFuYWdlci5zdGFydFNlc3Npb25SdW50aW1lKGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFN0YXJ0ZWQgcnVudGltZSB0cmFja2luZyBmb3Igc2Vzc2lvbjonLCBjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgIH0gZWxzZSBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnU3RvcHBlZCcpIHtcbiAgICAgICAgLy8gQm90IHN0b3BwZWQsIHN0b3AgcnVudGltZSB0cmFja2luZ1xuICAgICAgICBzZXNzaW9uTWFuYWdlci5zdG9wU2Vzc2lvblJ1bnRpbWUoY3VycmVudFNlc3Npb25JZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfij7nvuI8gU3RvcHBlZCBydW50aW1lIHRyYWNraW5nIGZvciBzZXNzaW9uOicsIGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3N0YXRlLmJvdFN5c3RlbVN0YXR1c10pO1xuXG4gIC8vIEF1dG8tY3JlYXRlIHNlc3Npb24gd2hlbiBib3QgYWN0dWFsbHkgc3RhcnRzIHJ1bm5pbmcgKG5vdCBkdXJpbmcgd2FybXVwKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcblxuICAgIC8vIENoZWNrIGlmIHdlIG5lZWQgdG8gY3JlYXRlIGEgc2Vzc2lvbiB3aGVuIGJvdCBhY3R1YWxseSBzdGFydHMgcnVubmluZ1xuICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJyAmJiAhc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpKSB7XG4gICAgICAvLyBPbmx5IGNyZWF0ZSBpZiB3ZSBoYXZlIHZhbGlkIGNyeXB0byBjb25maWd1cmF0aW9uIEFORCB0YXJnZXQgcHJpY2VzIHNldFxuICAgICAgLy8gVGhpcyBwcmV2ZW50cyBhdXRvLWNyZWF0aW9uIGZvciBmcmVzaCB3aW5kb3dzXG4gICAgICBpZiAoc3RhdGUuY29uZmlnLmNyeXB0bzEgJiYgc3RhdGUuY29uZmlnLmNyeXB0bzIgJiYgc3RhdGUudGFyZ2V0UHJpY2VSb3dzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2Vzc2lvbk1hbmFnZXIuY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZShzdGF0ZS5jb25maWcpXG4gICAgICAgICAgLnRoZW4oKHNlc3Npb25JZCkgPT4ge1xuICAgICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2V0Q3VycmVudFNlc3Npb24oc2Vzc2lvbklkKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgQXV0by1jcmVhdGVkIHNlc3Npb24gd2hlbiBib3Qgc3RhcnRlZDonLCBzZXNzaW9uSWQpO1xuICAgICAgICAgIH0pXG4gICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBhdXRvLWNyZWF0ZSBzZXNzaW9uOicsIGVycm9yKTtcbiAgICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBIYW5kbGUgcnVudGltZSB0cmFja2luZyBiYXNlZCBvbiBib3Qgc3RhdHVzXG4gICAgY29uc3QgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25NYW5hZ2VyLmdldEN1cnJlbnRTZXNzaW9uSWQoKTtcbiAgICBpZiAoY3VycmVudFNlc3Npb25JZCkge1xuICAgICAgaWYgKHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnKSB7XG4gICAgICAgIC8vIFN0YXJ0IHJ1bnRpbWUgdHJhY2tpbmcgd2hlbiBib3QgYmVjb21lcyBhY3RpdmVcbiAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc3RhcnRTZXNzaW9uUnVudGltZShjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KPse+4jyBTdGFydGVkIHJ1bnRpbWUgdHJhY2tpbmcgZm9yIHNlc3Npb246JywgY3VycmVudFNlc3Npb25JZCk7XG4gICAgICB9IGVsc2UgaWYgKHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1N0b3BwZWQnKSB7XG4gICAgICAgIC8vIFN0b3AgcnVudGltZSB0cmFja2luZyB3aGVuIGJvdCBzdG9wc1xuICAgICAgICBzZXNzaW9uTWFuYWdlci5zdG9wU2Vzc2lvblJ1bnRpbWUoY3VycmVudFNlc3Npb25JZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfij7nvuI8gU3RvcHBlZCBydW50aW1lIHRyYWNraW5nIGZvciBzZXNzaW9uOicsIGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3N0YXRlLmJvdFN5c3RlbVN0YXR1cywgc3RhdGUuY29uZmlnLmNyeXB0bzEsIHN0YXRlLmNvbmZpZy5jcnlwdG8yXSk7XG5cbiAgLy8gSGFuZGxlIGNyeXB0byBwYWlyIGNoYW5nZXMgZHVyaW5nIGFjdGl2ZSB0cmFkaW5nXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uSWQgPSBzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCk7XG5cbiAgICBpZiAoY3VycmVudFNlc3Npb25JZCkge1xuICAgICAgY29uc3QgY3VycmVudFNlc3Npb24gPSBzZXNzaW9uTWFuYWdlci5sb2FkU2Vzc2lvbihjdXJyZW50U2Vzc2lvbklkKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgY3J5cHRvIHBhaXIgaGFzIGNoYW5nZWQgZnJvbSB0aGUgY3VycmVudCBzZXNzaW9uXG4gICAgICBpZiAoY3VycmVudFNlc3Npb24gJiZcbiAgICAgICAgICAoY3VycmVudFNlc3Npb24uY29uZmlnLmNyeXB0bzEgIT09IHN0YXRlLmNvbmZpZy5jcnlwdG8xIHx8XG4gICAgICAgICAgIGN1cnJlbnRTZXNzaW9uLmNvbmZpZy5jcnlwdG8yICE9PSBzdGF0ZS5jb25maWcuY3J5cHRvMikpIHtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBDcnlwdG8gcGFpciBjaGFuZ2VkIGR1cmluZyBzZXNzaW9uLCBhdXRvLXNhdmluZyBhbmQgcmVzZXR0aW5nLi4uJyk7XG5cbiAgICAgICAgLy8gQXV0by1zYXZlIGN1cnJlbnQgc2Vzc2lvbiBpZiBib3Qgd2FzIHJ1bm5pbmcgb3IgaGFzIGRhdGFcbiAgICAgICAgaWYgKHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnIHx8XG4gICAgICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MubGVuZ3RoID4gMCB8fFxuICAgICAgICAgICAgc3RhdGUub3JkZXJIaXN0b3J5Lmxlbmd0aCA+IDApIHtcblxuICAgICAgICAgIC8vIFN0b3AgdGhlIGJvdCBpZiBpdCdzIHJ1bm5pbmdcbiAgICAgICAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycpIHtcbiAgICAgICAgICAgIHN0b3BCYWNrZW5kQm90KGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFNhdmUgY3VycmVudCBzZXNzaW9uIHdpdGggdGltZXN0YW1wXG4gICAgICAgICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgICAgICAgbWludXRlOiAnMi1kaWdpdCcsXG4gICAgICAgICAgICBob3VyMTI6IGZhbHNlXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc3Qgc2F2ZWROYW1lID0gYCR7Y3VycmVudFNlc3Npb24ubmFtZX0gKEF1dG9TYXZlZCAke3RpbWVzdGFtcH0pYDtcblxuICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLmNyZWF0ZU5ld1Nlc3Npb24oc2F2ZWROYW1lLCBjdXJyZW50U2Vzc2lvbi5jb25maWcpXG4gICAgICAgICAgICAudGhlbihhc3luYyAoc2F2ZWRTZXNzaW9uSWQpID0+IHtcbiAgICAgICAgICAgICAgYXdhaXQgc2Vzc2lvbk1hbmFnZXIuc2F2ZVNlc3Npb24oXG4gICAgICAgICAgICAgICAgc2F2ZWRTZXNzaW9uSWQsXG4gICAgICAgICAgICAgICAgY3VycmVudFNlc3Npb24uY29uZmlnLFxuICAgICAgICAgICAgICAgIHN0YXRlLnRhcmdldFByaWNlUm93cyxcbiAgICAgICAgICAgICAgICBzdGF0ZS5vcmRlckhpc3RvcnksXG4gICAgICAgICAgICAgICAgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgICAgIHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICAgICAgICAgIHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgICAgICAgIHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgICAgICAgIGZhbHNlXG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IEF1dG9TYXZlZCBzZXNzaW9uOicsIHNhdmVkTmFtZSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFJlc2V0IGZvciBuZXcgY3J5cHRvIHBhaXJcbiAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnUkVTRVRfRk9SX05FV19DUllQVE8nIH0pO1xuXG4gICAgICAgIC8vIE9ubHkgY3JlYXRlIG5ldyBzZXNzaW9uIGZvciB0aGUgbmV3IGNyeXB0byBwYWlyIGlmIGJvdCB3YXMgYWN0dWFsbHkgcnVubmluZ1xuICAgICAgICBpZiAoc3RhdGUuY29uZmlnLmNyeXB0bzEgJiYgc3RhdGUuY29uZmlnLmNyeXB0bzIgJiYgc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycpIHtcbiAgICAgICAgICBjb25zdCBjdXJyZW50QmFsYW5jZXMgPSB7XG4gICAgICAgICAgICBjcnlwdG8xOiBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICAgIGNyeXB0bzI6IHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgICAgc3RhYmxlY29pbjogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2VcbiAgICAgICAgICB9O1xuICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLmNyZWF0ZU5ld1Nlc3Npb25XaXRoQXV0b05hbWUoc3RhdGUuY29uZmlnLCB1bmRlZmluZWQsIGN1cnJlbnRCYWxhbmNlcylcbiAgICAgICAgICAgIC50aGVuKChuZXdTZXNzaW9uSWQpID0+IHtcbiAgICAgICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2V0Q3VycmVudFNlc3Npb24obmV3U2Vzc2lvbklkKTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CfhpUgQ3JlYXRlZCBuZXcgc2Vzc2lvbiBmb3IgY3J5cHRvIHBhaXI6Jywgc3RhdGUuY29uZmlnLmNyeXB0bzEsICcvJywgc3RhdGUuY29uZmlnLmNyeXB0bzIsICd3aXRoIGJhbGFuY2VzOicsIGN1cnJlbnRCYWxhbmNlcyk7XG5cbiAgICAgICAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgICAgICAgIHRpdGxlOiBcIkNyeXB0byBQYWlyIENoYW5nZWRcIixcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYFByZXZpb3VzIHNlc3Npb24gQXV0b1NhdmVkLiBOZXcgc2Vzc2lvbiBjcmVhdGVkIGZvciAke3N0YXRlLmNvbmZpZy5jcnlwdG8xfS8ke3N0YXRlLmNvbmZpZy5jcnlwdG8yfWAsXG4gICAgICAgICAgICAgICAgZHVyYXRpb246IDUwMDBcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBJZiBib3Qgd2Fzbid0IHJ1bm5pbmcsIGp1c3QgY2xlYXIgdGhlIGN1cnJlbnQgc2Vzc2lvbiB3aXRob3V0IGNyZWF0aW5nIGEgbmV3IG9uZVxuICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLmNsZWFyQ3VycmVudFNlc3Npb24oKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBDcnlwdG8gcGFpciBjaGFuZ2VkIGJ1dCBib3Qgd2FzblxcJ3QgcnVubmluZyAtIGNsZWFyZWQgY3VycmVudCBzZXNzaW9uJyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzdGF0ZS5jb25maWcuY3J5cHRvMSwgc3RhdGUuY29uZmlnLmNyeXB0bzJdKTtcblxuICAvLyBJbml0aWFsaXplIG5ldHdvcmsgbW9uaXRvcmluZyBhbmQgYXV0by1zYXZlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbmV0d29ya01vbml0b3IgPSBOZXR3b3JrTW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IGF1dG9TYXZlTWFuYWdlciA9IEF1dG9TYXZlTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IG1lbW9yeU1vbml0b3IgPSBNZW1vcnlNb25pdG9yLmdldEluc3RhbmNlKCk7XG4gICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuXG4gICAgLy8gU2V0IHVwIG5ldHdvcmsgc3RhdHVzIGxpc3RlbmVyXG4gICAgY29uc3QgdW5zdWJzY3JpYmVOZXR3b3JrID0gbmV0d29ya01vbml0b3IuYWRkTGlzdGVuZXIoKGlzT25saW5lLCBpc0luaXRpYWwpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn4yQIE5ldHdvcmsgc3RhdHVzIGNoYW5nZWQ6ICR7aXNPbmxpbmUgPyAnT25saW5lJyA6ICdPZmZsaW5lJ31gKTtcblxuICAgICAgLy8gSGFuZGxlIG9mZmxpbmUgc3RhdGUgLSBzdG9wIGJvdCBhbmQgc2F2ZSBzZXNzaW9uXG4gICAgICBpZiAoIWlzT25saW5lICYmICFpc0luaXRpYWwpIHtcbiAgICAgICAgLy8gU3RvcCB0aGUgYm90IGlmIGl0J3MgcnVubmluZ1xuICAgICAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UtCBJbnRlcm5ldCBsb3N0IC0gc3RvcHBpbmcgYm90IGFuZCBzYXZpbmcgc2Vzc2lvbicpO1xuICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NZU1RFTV9TVE9QX0JPVCcgfSk7XG5cbiAgICAgICAgICAvLyBBdXRvLXNhdmUgY3VycmVudCBzZXNzaW9uXG4gICAgICAgICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uSWQgPSBzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCk7XG4gICAgICAgICAgaWYgKGN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uID0gc2Vzc2lvbk1hbmFnZXIubG9hZFNlc3Npb24oY3VycmVudFNlc3Npb25JZCk7XG4gICAgICAgICAgICBpZiAoY3VycmVudFNlc3Npb24pIHtcbiAgICAgICAgICAgICAgLy8gQ3JlYXRlIG9mZmxpbmUgYmFja3VwIHNlc3Npb25cbiAgICAgICAgICAgICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgICAgICAgICAgbW9udGg6ICdzaG9ydCcsXG4gICAgICAgICAgICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICAgICAgICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgICAgICAgICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgICAgICAgICAgICAgIGhvdXIxMjogZmFsc2VcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIGNvbnN0IG9mZmxpbmVOYW1lID0gYCR7Y3VycmVudFNlc3Npb24ubmFtZX0gKE9mZmxpbmUgQmFja3VwICR7dGltZXN0YW1wfSlgO1xuXG4gICAgICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLmNyZWF0ZU5ld1Nlc3Npb24ob2ZmbGluZU5hbWUsIGN1cnJlbnRTZXNzaW9uLmNvbmZpZylcbiAgICAgICAgICAgICAgICAudGhlbihhc3luYyAoYmFja3VwU2Vzc2lvbklkKSA9PiB7XG4gICAgICAgICAgICAgICAgICBhd2FpdCBzZXNzaW9uTWFuYWdlci5zYXZlU2Vzc2lvbihcbiAgICAgICAgICAgICAgICAgICAgYmFja3VwU2Vzc2lvbklkLFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZS5jb25maWcsXG4gICAgICAgICAgICAgICAgICAgIHN0YXRlLnRhcmdldFByaWNlUm93cyxcbiAgICAgICAgICAgICAgICAgICAgc3RhdGUub3JkZXJIaXN0b3J5LFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICAgICAgICAgIHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICAgICAgICAgICAgICBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgICAgICAgICAgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgICAgICAgICAgICAgIGZhbHNlXG4gICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gQ3JlYXRlZCBvZmZsaW5lIGJhY2t1cCBzZXNzaW9uOicsIG9mZmxpbmVOYW1lKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBTZW5kIHRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgbmV0d29yayBkaXNjb25uZWN0aW9uXG4gICAgICAgIHNlbmRUZWxlZ3JhbUVycm9yTm90aWZpY2F0aW9uKFxuICAgICAgICAgICdOZXR3b3JrIERpc2Nvbm5lY3Rpb24nLFxuICAgICAgICAgICdJbnRlcm5ldCBjb25uZWN0aW9uIGxvc3QuIEJvdCBoYXMgYmVlbiBzdG9wcGVkIGFuZCBzZXNzaW9uIHNhdmVkIGF1dG9tYXRpY2FsbHkuIFRyYWRpbmcgd2lsbCByZXN1bWUgd2hlbiBjb25uZWN0aW9uIGlzIHJlc3RvcmVkLidcbiAgICAgICAgKS5jYXRjaChjb25zb2xlLmVycm9yKTtcblxuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6IFwiTmV0d29yayBEaXNjb25uZWN0ZWRcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCJCb3Qgc3RvcHBlZCBhbmQgc2Vzc2lvbiBzYXZlZC4gVHJhZGluZyBwYXVzZWQgdW50aWwgY29ubmVjdGlvbiByZXN0b3JlZC5cIixcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICAgICAgZHVyYXRpb246IDgwMDBcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2UgaWYgKGlzT25saW5lICYmICFpc0luaXRpYWwpIHtcbiAgICAgICAgLy8gUHJvY2VzcyBhbnkgcXVldWVkIHRlbGVncmFtIG5vdGlmaWNhdGlvbnMgZmlyc3RcbiAgICAgICAgcHJvY2Vzc1F1ZXVlZFRlbGVncmFtTm90aWZpY2F0aW9ucygpLmNhdGNoKGNvbnNvbGUuZXJyb3IpO1xuXG4gICAgICAgIC8vIFNlbmQgZGVsYXllZCBub3RpZmljYXRpb24gYWJvdXQgdGhlIHByZXZpb3VzIGRpc2Nvbm5lY3Rpb24gbm93IHRoYXQgd2UncmUgYmFjayBvbmxpbmVcbiAgICAgICAgc2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24oXG4gICAgICAgICAgJ05ldHdvcmsgUmVjb25uZWN0ZWQnLFxuICAgICAgICAgICdJbnRlcm5ldCBjb25uZWN0aW9uIGhhcyBiZWVuIHJlc3RvcmVkLiBUaGUgYm90IHdhcyBhdXRvbWF0aWNhbGx5IHN0b3BwZWQgZHVyaW5nIHRoZSBkaXNjb25uZWN0aW9uIHRvIHByZXZlbnQgdHJhZGluZyBlcnJvcnMuIFlvdSBjYW4gbm93IHJlc3VtZSB0cmFkaW5nIHNhZmVseS4nXG4gICAgICAgICkuY2F0Y2goY29uc29sZS5lcnJvcik7XG5cbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcIk5ldHdvcmsgUmVjb25uZWN0ZWRcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCJDb25uZWN0aW9uIHJlc3RvcmVkLiBZb3UgY2FuIHJlc3VtZSB0cmFkaW5nLlwiLFxuICAgICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gU2V0IHVwIG1lbW9yeSBtb25pdG9yaW5nXG4gICAgY29uc3QgdW5zdWJzY3JpYmVNZW1vcnkgPSBtZW1vcnlNb25pdG9yLmFkZExpc3RlbmVyKChtZW1vcnkpID0+IHtcbiAgICAgIGNvbnN0IHVzZWRNQiA9IG1lbW9yeS51c2VkSlNIZWFwU2l6ZSAvIDEwMjQgLyAxMDI0O1xuICAgICAgaWYgKHVzZWRNQiA+IDE1MCkgeyAvLyAxNTBNQiB0aHJlc2hvbGRcbiAgICAgICAgY29uc29sZS53YXJuKGDwn6egIEhpZ2ggbWVtb3J5IHVzYWdlOiAke3VzZWRNQi50b0ZpeGVkKDIpfU1CYCk7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCJIaWdoIE1lbW9yeSBVc2FnZVwiLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBgTWVtb3J5IHVzYWdlIGlzIGhpZ2ggKCR7dXNlZE1CLnRvRml4ZWQoMCl9TUIpLiBDb25zaWRlciByZWZyZXNoaW5nIHRoZSBwYWdlLmAsXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgICAgIGR1cmF0aW9uOiA1MDAwXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gU2V0IHVwIGF1dG8tc2F2ZVxuICAgIGNvbnN0IHNhdmVGdW5jdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIFNhdmUgdG8gc2Vzc2lvbiBtYW5hZ2VyIGlmIHdlIGhhdmUgYSBjdXJyZW50IHNlc3Npb25cbiAgICAgICAgY29uc3QgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25NYW5hZ2VyLmdldEN1cnJlbnRTZXNzaW9uSWQoKTtcbiAgICAgICAgaWYgKGN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgICAgICBhd2FpdCBzZXNzaW9uTWFuYWdlci5zYXZlU2Vzc2lvbihcbiAgICAgICAgICAgIGN1cnJlbnRTZXNzaW9uSWQsXG4gICAgICAgICAgICBzdGF0ZS5jb25maWcsXG4gICAgICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgICBzdGF0ZS5vcmRlckhpc3RvcnksXG4gICAgICAgICAgICBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICAgIHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgICAgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgICAgICBzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJ1xuICAgICAgICAgICk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBbHNvIHNhdmUgdG8gbG9jYWxTdG9yYWdlIGFzIGJhY2t1cFxuICAgICAgICBzYXZlU3RhdGVUb0xvY2FsU3RvcmFnZShzdGF0ZSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRvLXNhdmUgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgYXV0b1NhdmVNYW5hZ2VyLmVuYWJsZShzYXZlRnVuY3Rpb24sIDMwMDAwKTsgLy8gQXV0by1zYXZlIGV2ZXJ5IDMwIHNlY29uZHNcblxuICAgIC8vIEFkZCBiZWZvcmV1bmxvYWQgbGlzdGVuZXIgdG8gc2F2ZSBzZXNzaW9uIG9uIGJyb3dzZXIgY2xvc2UvcmVmcmVzaFxuICAgIGNvbnN0IGhhbmRsZUJlZm9yZVVubG9hZCA9IChldmVudDogQmVmb3JlVW5sb2FkRXZlbnQpID0+IHtcbiAgICAgIC8vIFNhdmUgaW1tZWRpYXRlbHkgYmVmb3JlIHVubG9hZFxuICAgICAgc2F2ZUZ1bmN0aW9uKCk7XG5cbiAgICAgIC8vIElmIGJvdCBpcyBydW5uaW5nLCBzaG93IHdhcm5pbmdcbiAgICAgIGlmIChzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJykge1xuICAgICAgICBjb25zdCBtZXNzYWdlID0gJ1RyYWRpbmcgYm90IGlzIGN1cnJlbnRseSBydW5uaW5nLiBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gbGVhdmU/JztcbiAgICAgICAgZXZlbnQucmV0dXJuVmFsdWUgPSBtZXNzYWdlO1xuICAgICAgICByZXR1cm4gbWVzc2FnZTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZCk7XG5cbiAgICAvLyBDbGVhbnVwIGZ1bmN0aW9uXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHVuc3Vic2NyaWJlTmV0d29yaygpO1xuICAgICAgdW5zdWJzY3JpYmVNZW1vcnkoKTtcbiAgICAgIGF1dG9TYXZlTWFuYWdlci5kaXNhYmxlKCk7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKTtcbiAgICB9O1xuICB9LCBbc3RhdGUsIHRvYXN0XSk7XG5cbiAgLy8gRm9yY2Ugc2F2ZSB3aGVuIGJvdCBzdGF0dXMgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGF1dG9TYXZlTWFuYWdlciA9IEF1dG9TYXZlTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgIGF1dG9TYXZlTWFuYWdlci5zYXZlTm93KCk7XG4gIH0sIFtzdGF0ZS5ib3RTeXN0ZW1TdGF0dXNdKTtcblxuICAvLyBNYW51YWwgc2F2ZSBzZXNzaW9uIGZ1bmN0aW9uXG4gIGNvbnN0IHNhdmVDdXJyZW50U2Vzc2lvbiA9IHVzZUNhbGxiYWNrKGFzeW5jICgpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgICAgY29uc3QgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25NYW5hZ2VyLmdldEN1cnJlbnRTZXNzaW9uSWQoKTtcblxuICAgICAgaWYgKCFjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICAgIC8vIE5vIGN1cnJlbnQgc2Vzc2lvbiwgb25seSBjcmVhdGUgb25lIGlmIGJvdCBpcyBydW5uaW5nIG9yIGhhcyBtZWFuaW5nZnVsIGRhdGFcbiAgICAgICAgaWYgKHN0YXRlLmNvbmZpZy5jcnlwdG8xICYmIHN0YXRlLmNvbmZpZy5jcnlwdG8yICYmXG4gICAgICAgICAgICAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycgfHwgc3RhdGUudGFyZ2V0UHJpY2VSb3dzLmxlbmd0aCA+IDAgfHwgc3RhdGUub3JkZXJIaXN0b3J5Lmxlbmd0aCA+IDApKSB7XG4gICAgICAgICAgY29uc3QgY3VycmVudEJhbGFuY2VzID0ge1xuICAgICAgICAgICAgY3J5cHRvMTogc3RhdGUuY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgICAgICBjcnlwdG8yOiBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgIHN0YWJsZWNvaW46IHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlXG4gICAgICAgICAgfTtcbiAgICAgICAgICBjb25zdCBzZXNzaW9uSWQgPSBhd2FpdCBzZXNzaW9uTWFuYWdlci5jcmVhdGVOZXdTZXNzaW9uV2l0aEF1dG9OYW1lKHN0YXRlLmNvbmZpZywgdW5kZWZpbmVkLCBjdXJyZW50QmFsYW5jZXMpO1xuICAgICAgICAgIHNlc3Npb25NYW5hZ2VyLnNldEN1cnJlbnRTZXNzaW9uKHNlc3Npb25JZCk7XG4gICAgICAgICAgYXdhaXQgc2Vzc2lvbk1hbmFnZXIuc2F2ZVNlc3Npb24oXG4gICAgICAgICAgICBzZXNzaW9uSWQsXG4gICAgICAgICAgICBzdGF0ZS5jb25maWcsXG4gICAgICAgICAgICBzdGF0ZS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgICBzdGF0ZS5vcmRlckhpc3RvcnksXG4gICAgICAgICAgICBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICAgIHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgICAgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgICAgICBzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJ1xuICAgICAgICAgICk7XG4gICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBObyBzZXNzaW9uIHRvIHNhdmUgLSBib3Qgbm90IHJ1bm5pbmcgYW5kIG5vIG1lYW5pbmdmdWwgZGF0YScpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIC8vIFNhdmUgZXhpc3Rpbmcgc2Vzc2lvblxuICAgICAgcmV0dXJuIGF3YWl0IHNlc3Npb25NYW5hZ2VyLnNhdmVTZXNzaW9uKFxuICAgICAgICBjdXJyZW50U2Vzc2lvbklkLFxuICAgICAgICBzdGF0ZS5jb25maWcsXG4gICAgICAgIHN0YXRlLnRhcmdldFByaWNlUm93cyxcbiAgICAgICAgc3RhdGUub3JkZXJIaXN0b3J5LFxuICAgICAgICBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgIHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgIHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnXG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBjdXJyZW50IHNlc3Npb246JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSwgW3N0YXRlXSk7XG5cbiAgLy8gQ29udGV4dCB2YWx1ZVxuICBjb25zdCBjb250ZXh0VmFsdWU6IFRyYWRpbmdDb250ZXh0VHlwZSA9IHtcbiAgICAuLi5zdGF0ZSxcbiAgICBkaXNwYXRjaCxcbiAgICBzZXRUYXJnZXRQcmljZXMsXG4gICAgZ2V0RGlzcGxheU9yZGVycyxcbiAgICBjaGVja0JhY2tlbmRTdGF0dXMsXG4gICAgZmV0Y2hNYXJrZXRQcmljZSxcbiAgICBzdGFydEJhY2tlbmRCb3QsXG4gICAgc3RvcEJhY2tlbmRCb3QsXG4gICAgc2F2ZUNvbmZpZ1RvQmFja2VuZCxcbiAgICBzYXZlQ3VycmVudFNlc3Npb24sXG4gICAgYmFja2VuZFN0YXR1czogc3RhdGUuYmFja2VuZFN0YXR1cyBhcyAnb25saW5lJyB8ICdvZmZsaW5lJyB8ICd1bmtub3duJyxcbiAgICBib3RTeXN0ZW1TdGF0dXM6IHN0YXRlLmJvdFN5c3RlbVN0YXR1cyxcbiAgICBpc0JvdEFjdGl2ZTogc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycsXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8VHJhZGluZ0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9UcmFkaW5nQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbi8vIEN1c3RvbSBob29rIHRvIHVzZSB0aGUgdHJhZGluZyBjb250ZXh0XG5leHBvcnQgY29uc3QgdXNlVHJhZGluZ0NvbnRleHQgPSAoKTogVHJhZGluZ0NvbnRleHRUeXBlID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVHJhZGluZ0NvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUcmFkaW5nQ29udGV4dCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVHJhZGluZ1Byb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVJlZHVjZXIiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsIkRFRkFVTFRfQVBQX1NFVFRJTkdTIiwiQVZBSUxBQkxFX1NUQUJMRUNPSU5TIiwidjQiLCJ1dWlkdjQiLCJERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMiLCJ1c2VUb2FzdCIsInRyYWRpbmdBcGkiLCJTZXNzaW9uTWFuYWdlciIsIk5ldHdvcmtNb25pdG9yIiwiQXV0b1NhdmVNYW5hZ2VyIiwiTWVtb3J5TW9uaXRvciIsImNhbGN1bGF0ZUluaXRpYWxNYXJrZXRQcmljZSIsImNvbmZpZyIsImNyeXB0bzEiLCJjcnlwdG8yIiwiY2FsY3VsYXRlRmFsbGJhY2tNYXJrZXRQcmljZSIsImdldE1hcmtldFByaWNlRnJvbUFQSSIsInN5bWJvbDEiLCJ0b1VwcGVyQ2FzZSIsInN5bWJvbDIiLCJzeW1ib2wzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiZGF0YSIsImpzb24iLCJwcmljZSIsInBhcnNlRmxvYXQiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJ3YXJuIiwicmVzcG9uc2UxIiwicmVzcG9uc2UyIiwiUHJvbWlzZSIsImFsbCIsImRhdGExIiwiZGF0YTIiLCJwcmljZTEiLCJwcmljZTIiLCJjcm9zc1ByaWNlIiwiY3J5cHRvMUlkIiwiZ2V0Q29pbkdlY2tvSWQiLCJjcnlwdG8ySWQiLCJnZWNrb0Vycm9yIiwiY3J5cHRvMVVTRFByaWNlIiwiZ2V0UmVhbFRpbWVVU0RQcmljZSIsImNyeXB0bzJVU0RQcmljZSIsImNhbGN1bGF0ZWRQcmljZSIsIm1vY2tQcmljZSIsInN5bWJvbCIsIm1hcHBpbmciLCJnZXRTdGFibGVjb2luRXhjaGFuZ2VSYXRlIiwiY3J5cHRvIiwic3RhYmxlY29pbiIsImNyeXB0b0lkIiwic3RhYmxlY29pbklkIiwicmF0ZSIsImNyeXB0b1VTRFByaWNlIiwiZ2V0VVNEUHJpY2UiLCJzdGFibGVjb2luVVNEUHJpY2UiLCJwcmljZUNhY2hlIiwibGFzdFByaWNlVXBkYXRlIiwiUFJJQ0VfQ0FDSEVfRFVSQVRJT04iLCJ0ZWxlZ3JhbU5vdGlmaWNhdGlvblF1ZXVlIiwiTUFYX1FVRVVFX1NJWkUiLCJub3ciLCJEYXRlIiwiY2FjaGVLZXkiLCJjb2luR2Vja29JZCIsInVzZCIsImdldFN0YXRpY1VTRFByaWNlIiwidXNkUHJpY2VzIiwiYmFzZVByaWNlIiwiZmx1Y3R1YXRpb24iLCJNYXRoIiwicmFuZG9tIiwiZmluYWxQcmljZSIsInRvRml4ZWQiLCJpbml0aWFsQmFzZUNvbmZpZyIsInRyYWRpbmdNb2RlIiwiYmFzZUJpZCIsIm11bHRpcGxpZXIiLCJudW1EaWdpdHMiLCJzbGlwcGFnZVBlcmNlbnQiLCJpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50IiwiaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCIsInByZWZlcnJlZFN0YWJsZWNvaW4iLCJpbml0aWFsVHJhZGluZ1N0YXRlIiwidGFyZ2V0UHJpY2VSb3dzIiwib3JkZXJIaXN0b3J5IiwiYXBwU2V0dGluZ3MiLCJjdXJyZW50TWFya2V0UHJpY2UiLCJib3RTeXN0ZW1TdGF0dXMiLCJjcnlwdG8xQmFsYW5jZSIsImNyeXB0bzJCYWxhbmNlIiwic3RhYmxlY29pbkJhbGFuY2UiLCJiYWNrZW5kU3RhdHVzIiwibGFzdEFjdGlvblRpbWVzdGFtcFBlckNvdW50ZXIiLCJNYXAiLCJTVE9SQUdFX0tFWSIsInNhdmVTdGF0ZVRvTG9jYWxTdG9yYWdlIiwic3RhdGUiLCJzdGF0ZVRvU2F2ZSIsInRpbWVzdGFtcCIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJKU09OIiwic3RyaW5naWZ5IiwibG9hZFN0YXRlRnJvbUxvY2FsU3RvcmFnZSIsInNhdmVkU3RhdGUiLCJnZXRJdGVtIiwicGFyc2VkIiwicGFyc2UiLCJ0cmFkaW5nUmVkdWNlciIsImFjdGlvbiIsInR5cGUiLCJuZXdDb25maWciLCJwYXlsb2FkIiwic29ydCIsImEiLCJiIiwidGFyZ2V0UHJpY2UiLCJtYXAiLCJyb3ciLCJpbmRleCIsImNvdW50ZXIiLCJuZXdSb3dzIiwidXBkYXRlZFJvd3MiLCJpZCIsImZpbHRlcmVkUm93cyIsImZpbHRlciIsInVuZGVmaW5lZCIsImNvbmZpZ0ZvclJlc2V0IiwiY2xlYXIiLCJzZXNzaW9uTWFuYWdlciIsImdldEluc3RhbmNlIiwiY2xlYXJDdXJyZW50U2Vzc2lvbiIsIlRyYWRpbmdDb250ZXh0IiwiVHJhZGluZ1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJpbml0aWFsaXplU3RhdGUiLCJ1cmxQYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInNlYXJjaCIsImlzTmV3U2Vzc2lvbiIsImdldCIsImN1cnJlbnRTZXNzaW9uSWQiLCJnZXRDdXJyZW50U2Vzc2lvbklkIiwiY3VycmVudFNlc3Npb24iLCJsb2FkU2Vzc2lvbiIsInNlc3Npb25JZCIsIm5hbWUiLCJpc0FjdGl2ZSIsImxlbmd0aCIsImRpc3BhdGNoIiwidG9hc3QiLCJuZXdVcmwiLCJwYXRobmFtZSIsImhpc3RvcnkiLCJyZXBsYWNlU3RhdGUiLCJhdWRpb1JlZiIsInNlbmRUZWxlZ3JhbU5vdGlmaWNhdGlvbiIsIm1lc3NhZ2UiLCJpc0Vycm9yIiwicmV0cnlDb3VudCIsInRlbGVncmFtVG9rZW4iLCJ0ZWxlZ3JhbUNoYXRJZCIsIm5ldHdvcmtNb25pdG9yIiwiZ2V0U3RhdHVzIiwiaXNPbmxpbmUiLCJwdXNoIiwic2hpZnQiLCJjb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImFib3J0IiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJjaGF0X2lkIiwidGV4dCIsInBhcnNlX21vZGUiLCJzaWduYWwiLCJjbGVhclRpbWVvdXQiLCJzdGF0dXNUZXh0Iiwic2VuZFRlbGVncmFtRXJyb3JOb3RpZmljYXRpb24iLCJlcnJvclR5cGUiLCJlcnJvck1lc3NhZ2UiLCJ0b0xvY2FsZVN0cmluZyIsInNlbmRMb3dCYWxhbmNlTm90aWZpY2F0aW9uIiwiY3VycmVudEJhbGFuY2UiLCJyZXF1aXJlZEFtb3VudCIsInNlbmRBUElFcnJvck5vdGlmaWNhdGlvbiIsImFwaU5hbWUiLCJlcnJvckRldGFpbHMiLCJmZXRjaE1hcmtldFByaWNlIiwiY2F0Y2giLCJyZWFsVGltZVByaWNlSW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJjdXJyZW50IiwiQXVkaW8iLCJlbmFibGVBdWRpbyIsInZvbHVtZSIsInBsYXkiLCJ0aGVuIiwiZG9jdW1lbnQiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiYWRkRXZlbnRMaXN0ZW5lciIsInBsYXlTb3VuZCIsInNvdW5kS2V5IiwiYWxhcm1TZXR0aW5ncyIsInNvdW5kQWxlcnRzRW5hYmxlZCIsInNvdW5kUGF0aCIsImFsZXJ0T25PcmRlckV4ZWN1dGlvbiIsInNvdW5kT3JkZXJFeGVjdXRpb24iLCJhbGVydE9uRXJyb3IiLCJzb3VuZEVycm9yIiwicGF1c2UiLCJjdXJyZW50VGltZSIsInNyYyIsImxvYWQiLCJvbmVycm9yIiwiZSIsInBsYXlQcm9taXNlIiwicGF1c2VkIiwiZXJyIiwiU3RyaW5nIiwiaW5jbHVkZXMiLCJzZXRUYXJnZXRQcmljZXMiLCJwcmljZXMiLCJBcnJheSIsImlzQXJyYXkiLCJzb3J0ZWRQcmljZXMiLCJpc05hTiIsImV4aXN0aW5nUm93IiwiZmluZCIsInIiLCJzdGF0dXMiLCJvcmRlckxldmVsIiwidmFsdWVMZXZlbCIsInNvcnRlZFJvd3NGb3JMb2dpYyIsImN1cnJlbnRDcnlwdG8xQmFsYW5jZSIsImN1cnJlbnRDcnlwdG8yQmFsYW5jZSIsImFjdGlvbnNUYWtlbiIsInRhcmdldHNJblJhbmdlIiwiZGlmZlBlcmNlbnQiLCJhYnMiLCJpIiwiYWN0aXZlUm93IiwicHJpY2VEaWZmUGVyY2VudCIsImNvc3RDcnlwdG8yIiwiYW1vdW50Q3J5cHRvMUJvdWdodCIsInVwZGF0ZWRSb3ciLCJwb3ciLCJjcnlwdG8xQW1vdW50SGVsZCIsIm9yaWdpbmFsQ29zdENyeXB0bzIiLCJjcnlwdG8xVmFyIiwiY3J5cHRvMlZhciIsInBhaXIiLCJvcmRlclR5cGUiLCJhbW91bnRDcnlwdG8xIiwiYXZnUHJpY2UiLCJ2YWx1ZUNyeXB0bzIiLCJjcnlwdG8xU3ltYm9sIiwiY3J5cHRvMlN5bWJvbCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkdXJhdGlvbiIsImN1cnJlbnRDb3VudGVyIiwiaW5mZXJpb3JSb3ciLCJhbW91bnRDcnlwdG8xVG9TZWxsIiwiY3J5cHRvMlJlY2VpdmVkIiwicmVhbGl6ZWRQcm9maXQiLCJyZWFsaXplZFByb2ZpdENyeXB0bzEiLCJ1cGRhdGVkSW5mZXJpb3JSb3ciLCJyZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yIiwicmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMSIsInByb2ZpdEVtb2ppIiwiYW1vdW50Q3J5cHRvMlRvVXNlIiwiY3J5cHRvMlN0YWJsZWNvaW5QcmljZSIsInN0YWJsZWNvaW5PYnRhaW5lZCIsImZsdWN0dWF0ZWRDcnlwdG8xUHJpY2UiLCJjcnlwdG8xU3RhYmxlY29pblByaWNlIiwiY3J5cHRvMUJvdWdodCIsIm5ld0xldmVsIiwibmV3VmFsdWUiLCJiYXNlQ3J5cHRvMlByaWNlIiwiZXN0aW1hdGVkUHJvZml0Q3J5cHRvMiIsImVzdGltYXRlZFByb2ZpdENyeXB0bzEiLCJhbW91bnRTb2xkIiwiY3VycmVudFByaWNlIiwiaW5mZXJpb3JSb3dGb3VuZCIsImluZmVyaW9yUm93U3RhdHVzIiwiaW5mZXJpb3JSb3dDcnlwdG8xSGVsZCIsImluZmVyaW9yUm93T3JpZ2luYWxDb3N0IiwiY2FuRXhlY3V0ZVNlbGwiLCJzdGFibGVjb2luRnJvbUMxU2VsbCIsImNyeXB0bzJSZWFjcXVpcmVkIiwib3JpZ2luYWxDb3N0IiwicmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIiLCJnZXREaXNwbGF5T3JkZXJzIiwicGVyY2VudEZyb21BY3R1YWxQcmljZSIsImluY29tZUNyeXB0bzEiLCJpbmNvbWVDcnlwdG8yIiwidG90YWxVbnJlYWxpemVkUHJvZml0SW5DcnlwdG8yIiwicHJpY2VEaWZmZXJlbmNlIiwicHJpY2VEaWZmZXJlbmNlUGVyY2VudCIsInBvdGVudGlhbFByb2ZpdENyeXB0bzEiLCJwb3RlbnRpYWxQcm9maXRDcnlwdG8yIiwic2F2ZUNvbmZpZ1RvQmFja2VuZCIsImNvbmZpZ0RhdGEiLCJ0YXJnZXRQcmljZXMiLCJzYXZlQ29uZmlnIiwidmFyaWFudCIsInN0YXJ0QmFja2VuZEJvdCIsImNvbmZpZ0lkIiwic3RhcnRCb3QiLCJzdG9wQmFja2VuZEJvdCIsInN0b3BCb3QiLCJjaGVja0JhY2tlbmRTdGF0dXMiLCJhcGlVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImhlYWx0aFJlc3BvbnNlIiwicmVzcG9uc2VUZXh0IiwiY2F1c2UiLCJzdGFydFNlc3Npb25SdW50aW1lIiwic3RvcFNlc3Npb25SdW50aW1lIiwiY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZSIsInNldEN1cnJlbnRTZXNzaW9uIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwiaG91cjEyIiwic2F2ZWROYW1lIiwiY3JlYXRlTmV3U2Vzc2lvbiIsInNhdmVkU2Vzc2lvbklkIiwic2F2ZVNlc3Npb24iLCJjdXJyZW50QmFsYW5jZXMiLCJuZXdTZXNzaW9uSWQiLCJhdXRvU2F2ZU1hbmFnZXIiLCJtZW1vcnlNb25pdG9yIiwidW5zdWJzY3JpYmVOZXR3b3JrIiwiYWRkTGlzdGVuZXIiLCJpc0luaXRpYWwiLCJvZmZsaW5lTmFtZSIsImJhY2t1cFNlc3Npb25JZCIsInByb2Nlc3NRdWV1ZWRUZWxlZ3JhbU5vdGlmaWNhdGlvbnMiLCJ1bnN1YnNjcmliZU1lbW9yeSIsIm1lbW9yeSIsInVzZWRNQiIsInVzZWRKU0hlYXBTaXplIiwic2F2ZUZ1bmN0aW9uIiwiZW5hYmxlIiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwiZXZlbnQiLCJyZXR1cm5WYWx1ZSIsImRpc2FibGUiLCJzYXZlTm93Iiwic2F2ZUN1cnJlbnRTZXNzaW9uIiwiY29udGV4dFZhbHVlIiwiaXNCb3RBY3RpdmUiLCJQcm92aWRlciIsInZhbHVlIiwidXNlVHJhZGluZ0NvbnRleHQiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});