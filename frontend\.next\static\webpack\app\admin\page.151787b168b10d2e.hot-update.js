"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSessionForAlarm, setSelectedSessionForAlarm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = await sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance,\n                stablecoin: session.stablecoinBalance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded. Redirecting to dashboard...')\n        });\n        // Redirect to dashboard after loading session\n        setTimeout(()=>{\n            window.location.href = '/dashboard';\n        }, 1000);\n    };\n    const handleDeleteSession = async (sessionId)=>{\n        const success = await sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleOpenAlarmConfig = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (session) {\n            setSelectedSessionForAlarm({\n                id: sessionId,\n                name: session.name,\n                alarmSettings: session.alarmSettings\n            });\n            setAlarmModalOpen(true);\n        }\n    };\n    const handleSaveAlarmSettings = async (sessionId, alarmSettings)=>{\n        const success = await sessionManager.updateSessionAlarmSettings(sessionId, alarmSettings);\n        if (success) {\n            loadSessions();\n            toast({\n                title: \"Alarm Settings Saved\",\n                description: \"Session alarm settings have been updated\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save alarm settings\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        // Show all sessions that are marked as active (including those in other browser tabs)\n        // This allows users to see all currently running sessions across all windows\n        return sessions.filter((s)=>s.isActive);\n    };\n    const getInactiveSessions = ()=>{\n        return sessions.filter((s)=>!s.isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Alarm\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                        title: \"Configure Alarms\",\n                                                        className: \"btn-outline-neo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleSaveCurrentSession,\n                                                        size: \"sm\",\n                                                        className: \"btn-neo\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Save\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, session.id, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Alarm\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                            title: \"Configure Alarms\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 470,\n                columnNumber: 7\n            }, this),\n            selectedSessionForAlarm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__.SessionAlarmConfigModal, {\n                isOpen: alarmModalOpen,\n                onClose: ()=>{\n                    setAlarmModalOpen(false);\n                    setSelectedSessionForAlarm(null);\n                },\n                sessionId: selectedSessionForAlarm.id,\n                sessionName: selectedSessionForAlarm.name,\n                currentAlarmSettings: selectedSessionForAlarm.alarmSettings,\n                onSave: handleSaveAlarmSettings\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 577,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 359,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"qvvjKP1M0ciSaXHnASZ/CLpomBw=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});