"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Define crypto lists locally as fallback\nconst LOCAL_ALLOWED_CRYPTO1 = [\n    \"BTC\",\n    \"ETH\",\n    \"BNB\",\n    \"SOL\",\n    \"LINK\",\n    \"AVAX\",\n    \"DOT\",\n    \"UNI\",\n    \"NEAR\",\n    \"AAVE\",\n    \"ATOM\",\n    \"VET\",\n    \"RENDER\",\n    \"POL\",\n    \"ALGO\",\n    \"ARB\",\n    \"FET\",\n    \"PAXG\",\n    \"GALA\",\n    \"CRV\",\n    \"COMP\",\n    \"ENJ\"\n];\nconst LOCAL_ALLOWED_CRYPTO2 = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2;\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2;\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1,\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== config.crypto1) : ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2,\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"Cx8udRVa9LCP2mV20ZF0LuBIfNk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});