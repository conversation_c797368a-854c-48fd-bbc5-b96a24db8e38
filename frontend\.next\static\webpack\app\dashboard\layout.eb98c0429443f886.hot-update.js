"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Define crypto lists locally as fallback\nconst LOCAL_ALLOWED_CRYPTO1 = [\n    \"BTC\",\n    \"ETH\",\n    \"BNB\",\n    \"SOL\",\n    \"LINK\",\n    \"AVAX\",\n    \"DOT\",\n    \"UNI\",\n    \"NEAR\",\n    \"AAVE\",\n    \"ATOM\",\n    \"VET\",\n    \"RENDER\",\n    \"POL\",\n    \"ALGO\",\n    \"ARB\",\n    \"FET\",\n    \"PAXG\",\n    \"GALA\",\n    \"CRV\",\n    \"COMP\",\n    \"ENJ\"\n];\nconst LOCAL_ALLOWED_CRYPTO2 = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2;\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2;\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1,\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (_lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== config.crypto1) : _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2,\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"Cx8udRVa9LCP2mV20ZF0LuBIfNk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9UcmFkaW5nQ29uZmlnU2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQzRCO0FBRS9CO0FBQ0Y7QUFDQTtBQUNNO0FBQ21EO0FBQ047QUFDeEM7QUFDSDtBQUNrQjtBQUVxQztBQUM5QztBQUNKO0FBRzNELHdDQUF3QztBQUN4QyxNQUFNeUIsMkJBQTJCO0lBQUM7SUFBUTtJQUFRO0NBQU07QUFFeEQsMENBQTBDO0FBQzFDLE1BQU1DLHdCQUF3QjtJQUM1QjtJQUFPO0lBQU87SUFBTztJQUFPO0lBQVE7SUFBUTtJQUFPO0lBQU87SUFBUTtJQUNsRTtJQUFRO0lBQU87SUFBVTtJQUFPO0lBQVE7SUFBTztJQUFPO0lBQVE7SUFDOUQ7SUFBTztJQUFRO0NBQ2hCO0FBRUQsTUFBTUMsd0JBQXdCO0lBQzVCO0lBQVE7SUFBTztJQUFRO0lBQVM7SUFBUTtDQUN6QztBQUNELG9DQUFvQztBQUNwQyxNQUFNQyxtQkFBbUI7SUFBQztJQUFRO0lBQU87SUFBUTtJQUFTO0lBQVE7Q0FBTTtBQUNVO0FBQ3JDO0FBQ1o7QUFHbEIsU0FBU087O0lBQ3RCLGlEQUFpRDtJQUNqRCxJQUFJQztJQUNKLElBQUk7UUFDRkEsaUJBQWlCbEMsMkVBQWlCQTtJQUNwQyxFQUFFLE9BQU9tQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2hELHFCQUNFLDhEQUFDRTtZQUFNQyxXQUFVO3NCQUNmLDRFQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0U7b0JBQUVGLFdBQVU7OEJBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJcEM7SUFFQSxNQUFNLEVBQUVHLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxlQUFlLEVBQUVDLFdBQVcsRUFBRUMsaUJBQWlCQyxzQkFBc0IsRUFBRSxHQUFHWjtJQUNwRyxNQUFNYSxjQUFjSixvQkFBb0I7SUFDeEMsTUFBTUssaUJBQWlCTCxvQkFBb0I7SUFHM0MsTUFBTSxFQUFFTSxLQUFLLEVBQUUsR0FBR2xCLDJEQUFRQTtJQUUxQixNQUFNLENBQUNtQixtQkFBbUJDLHFCQUFxQixHQUFHcEQsK0NBQVFBLENBQUM7SUFLM0QsTUFBTXFELG9CQUFvQixDQUFDQztRQUN6QixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHSixFQUFFSyxNQUFNO1FBQy9DLElBQUlDO1FBRUosSUFBSUgsU0FBUyxZQUFZO1lBQ3ZCRyxNQUFNRjtRQUNSLE9BQU8sSUFBSUQsU0FBUyxVQUFVO1lBQzVCLG9EQUFvRDtZQUNwRCxJQUFJRCxVQUFVLE1BQU1BLFVBQVUsUUFBUUEsVUFBVUssV0FBVztnQkFDekRELE1BQU07WUFDUixPQUFPO2dCQUNMLE1BQU1FLFNBQVNDLFdBQVdQO2dCQUMxQkksTUFBTUksTUFBTUYsVUFBVSxJQUFJQTtZQUM1QjtRQUNGLE9BQU87WUFDTEYsTUFBTUo7UUFDUjtRQUVBYixTQUFTO1lBQUVjLE1BQU07WUFBY1EsU0FBUztnQkFBRSxDQUFDVixLQUFLLEVBQUVLO1lBQUk7UUFBRTtJQUMxRDtJQUVBLE1BQU1NLHFCQUFxQixDQUFDWCxNQUFjQztRQUN4Q2IsU0FBUztZQUFFYyxNQUFNO1lBQWNRLFNBQVM7Z0JBQUUsQ0FBQ1YsS0FBSyxFQUFFQztZQUFNO1FBQUU7UUFDMUQsSUFBSUQsU0FBUyxXQUFXO1lBQ3RCLCtEQUErRDtZQUMvRCxNQUFNWSxjQUFjaEQsZ0VBQXVCLENBQUNxQyxNQUE4QyxJQUFJaEMsNEJBQTRCO2dCQUFDO2dCQUFRO2dCQUFRO2FBQU07WUFDakosSUFBSSxDQUFDa0IsT0FBTzBCLE9BQU8sSUFBSSxDQUFDQyxNQUFNQyxPQUFPLENBQUNILGdCQUFnQixDQUFDQSxZQUFZSSxRQUFRLENBQUM3QixPQUFPMEIsT0FBTyxHQUFHO2dCQUMzRnpCLFNBQVM7b0JBQUVjLE1BQU07b0JBQWNRLFNBQVM7d0JBQUVHLFNBQVNELFdBQVcsQ0FBQyxFQUFFLElBQUk7b0JBQU87Z0JBQUUsSUFBSSxzQ0FBc0M7WUFDMUg7UUFDRjtJQUNGO0lBRUEsTUFBTUsseUJBQXlCLENBQUNDO1FBQzlCOUIsU0FBUztZQUFFYyxNQUFNO1lBQWNRLFNBQVM7Z0JBQUVTLFNBQVNEO1lBQU87UUFBRTtRQUM1RCwyREFBMkQ7UUFDM0QsSUFBSS9CLE9BQU9pQyxXQUFXLEtBQUssa0JBQWtCO1lBQzNDLDZGQUE2RjtZQUM3RixNQUFNQyxpQkFBaUIsQ0FBQ3ZELHdEQUFlQSxJQUFJSSxxQkFBb0IsRUFBR29ELE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTUw7WUFDcEYsSUFBSSxDQUFDRyxlQUFlTCxRQUFRLENBQUM3QixPQUFPMEIsT0FBTyxLQUFLMUIsT0FBTzBCLE9BQU8sS0FBS0ssUUFBUTtnQkFDekU5QixTQUFTO29CQUFFYyxNQUFNO29CQUFjUSxTQUFTO3dCQUFFRyxTQUFTUSxjQUFjLENBQUMsRUFBRTtvQkFBQztnQkFBRTtZQUN6RTtRQUNGLE9BQU87WUFDTCw4REFBOEQ7WUFDOUQsTUFBTUEsaUJBQWlCdEQsd0RBQWVBLElBQUlJO1lBQzFDLElBQUksQ0FBQ2tELGVBQWVMLFFBQVEsQ0FBQzdCLE9BQU8wQixPQUFPLEdBQUc7Z0JBQzVDekIsU0FBUztvQkFBRWMsTUFBTTtvQkFBY1EsU0FBUzt3QkFBRUcsU0FBU1EsY0FBYyxDQUFDLEVBQUU7b0JBQUM7Z0JBQUU7WUFDekU7UUFDRjtJQUNGO0lBRUEsTUFBTUcseUJBQXlCLENBQUNOO1FBQzlCLG9FQUFvRTtRQUNwRSxJQUFJL0IsT0FBT2lDLFdBQVcsS0FBSyxvQkFBb0JGLFdBQVcvQixPQUFPZ0MsT0FBTyxFQUFFO1lBQ3hFLGdFQUFnRTtZQUNoRTtRQUNGO1FBQ0EvQixTQUFTO1lBQUVjLE1BQU07WUFBY1EsU0FBUztnQkFBRUcsU0FBU0s7WUFBTztRQUFFO0lBQzlEO0lBRUEsTUFBTU8sMEJBQTBCLENBQUN0QjtRQUMvQixNQUFNdUIsVUFBdUJ2QixVQUFVLG1CQUFtQjtRQUUxRCw4Q0FBOEM7UUFDOUMsSUFBSXdCO1FBQ0osSUFBSUQsWUFBWSxrQkFBa0I7WUFDaEMsNkZBQTZGO1lBQzdGLE1BQU1MLGlCQUFpQixDQUFDdkQsd0RBQWVBLElBQUlJLHFCQUFvQixFQUFHb0QsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNcEMsT0FBT2dDLE9BQU87WUFDbEdRLGFBQWFOLGNBQWMsQ0FBQyxFQUFFO1FBQ2hDLE9BQU87WUFDTCw4REFBOEQ7WUFDOUQsTUFBTUEsaUJBQWlCdEQsd0RBQWVBLElBQUlJO1lBQzFDd0QsYUFBYU4sY0FBYyxDQUFDLEVBQUU7UUFDaEM7UUFFQWpDLFNBQVM7WUFBRWMsTUFBTTtZQUFjUSxTQUFTO2dCQUFFVSxhQUFhTTtnQkFBU2IsU0FBU2M7WUFBVztRQUFFO0lBQ3hGO0lBRUEsTUFBTUMsMEJBQTBCLENBQUNDLFdBQXNFNUI7UUFDckcsSUFBSTZCLFVBQVV0QixXQUFXUDtRQUN6QixJQUFJUSxNQUFNcUIsVUFBVUEsVUFBVTtRQUM5QixJQUFJQSxVQUFVLEdBQUdBLFVBQVU7UUFDM0IsSUFBSUEsVUFBVSxLQUFLQSxVQUFVO1FBRTdCLElBQUlELGNBQWMsNkJBQTZCO1lBQzdDekMsU0FBUztnQkFBRWMsTUFBTTtnQkFBY1EsU0FBUztvQkFBRXFCLDJCQUEyQkQ7b0JBQVNFLDJCQUEyQixNQUFNRjtnQkFBUTtZQUFFO1FBQzNILE9BQU87WUFDTDFDLFNBQVM7Z0JBQUVjLE1BQU07Z0JBQWNRLFNBQVM7b0JBQUVzQiwyQkFBMkJGO29CQUFTQywyQkFBMkIsTUFBTUQ7Z0JBQVE7WUFBRTtRQUMzSDtJQUNGO0lBU0EsMEJBQTBCO0lBQzFCLE1BQU1HLGlCQUFpQnRFLDBEQUFpQkEsSUFBSSxFQUFFO0lBQzlDLE1BQU11RSxpQkFBaUIvQyxPQUFPaUMsV0FBVyxLQUFLLGVBQ3pDeEQsZ0VBQXVCLENBQUN1QixPQUFPZ0MsT0FBTyxDQUF5QyxJQUFJbEQsNEJBQTRCO1FBQUM7UUFBUTtRQUFRO0tBQU0sR0FDdkksQ0FBQ04sMERBQWlCQSxJQUFJLEVBQUUsRUFBRTJELE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTXBDLE9BQU9nQyxPQUFPLEdBQUcscURBQXFEO0lBRXRILDBDQUEwQztJQUMxQ3JDLFFBQVFxRCxHQUFHLENBQUMsdUNBQXVDeEUsMERBQWlCQSxhQUFqQkEsMERBQWlCQSx1QkFBakJBLDBEQUFpQkEsQ0FBRXlFLE1BQU07SUFDNUV0RCxRQUFRcUQsR0FBRyxDQUFDLG9DQUFvQ0YsZUFBZUcsTUFBTTtJQUNyRXRELFFBQVFxRCxHQUFHLENBQUMsK0JBQStCeEUsMERBQWlCQSxhQUFqQkEsMERBQWlCQSx1QkFBakJBLDBEQUFpQkEsQ0FBRTBFLEtBQUssQ0FBQyxHQUFHO0lBQ3ZFdkQsUUFBUXFELEdBQUcsQ0FBQyw4QkFBOEJyRSx3REFBZUE7SUFDekRnQixRQUFRcUQsR0FBRyxDQUFDLDhCQUE4QnBFLHdEQUFlQTtJQUN6RGUsUUFBUXFELEdBQUcsQ0FBQyxvQ0FBb0N0RSw4REFBcUJBO0lBRXJFLHFCQUNFLDhEQUFDa0I7UUFBTUMsV0FBVTs7MEJBQ2YsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDc0Q7b0JBQUd0RCxXQUFVOzhCQUEwQzs7Ozs7Ozs7Ozs7MEJBRTFELDhEQUFDeEIsa0VBQVVBO2dCQUFDd0IsV0FBVTswQkFDcEIsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQzVCLHFEQUFJQTs0QkFBQzRCLFdBQVU7OzhDQUNkLDhEQUFDMUIsMkRBQVVBOzhDQUNULDRFQUFDQywwREFBU0E7d0NBQUN5QixXQUFVO2tEQUFpQzs7Ozs7Ozs7Ozs7OENBRXhELDhEQUFDM0IsNERBQVdBO29DQUFDMkIsV0FBVTs7c0RBQ3JCLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsQyw2REFBUUE7b0RBQ1B5RixJQUFHO29EQUNIcEMsU0FBU2hCLE9BQU9pQyxXQUFXLEtBQUs7b0RBQ2hDb0IsaUJBQWlCZjs7Ozs7OzhEQUVuQiw4REFBQzVFLHVEQUFLQTtvREFBQzRGLFNBQVE7b0RBQXVCekQsV0FBVTs4REFBNkY7Ozs7Ozs7Ozs7OztzREFJL0ksOERBQUNFOzRDQUFFRixXQUFVOztnREFBZ0M7Z0RBQzVCRyxPQUFPaUMsV0FBVyxLQUFLLGVBQWUsZ0JBQWdCOzs7Ozs7O3dDQUV0RWpDLE9BQU9pQyxXQUFXLEtBQUssa0NBQ3JCLDhEQUFDbkM7OzhEQUNBLDhEQUFDcEMsdURBQUtBO29EQUFDNEYsU0FBUTs4REFBc0I7Ozs7Ozs4REFDckMsOERBQUMxRix5REFBTUE7b0RBQUNpRCxNQUFLO29EQUFzQkMsT0FBT2QsT0FBT3VELG1CQUFtQjtvREFBRUMsZUFBZSxDQUFDdEMsTUFBUU0sbUJBQW1CLHVCQUF1Qk47O3NFQUN0SSw4REFBQ25ELGdFQUFhQTs0REFBQ3FGLElBQUc7c0VBQXNCLDRFQUFDcEYsOERBQVdBO2dFQUFDeUYsYUFBWTs7Ozs7Ozs7Ozs7c0VBQ2pFLDhEQUFDNUYsZ0VBQWFBOzREQUFDZ0MsV0FBVTtzRUFDdEJaLGlCQUFpQnlFLEdBQUcsQ0FBQ0MsQ0FBQUEsbUJBQ3BCLDhEQUFDN0YsNkRBQVVBO29FQUFVZ0QsT0FBTzZDOzhFQUFLQTttRUFBaEJBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVkvQiw4REFBQzFGLHFEQUFJQTs0QkFBQzRCLFdBQVU7OzhDQUNkLDhEQUFDMUIsMkRBQVVBOzhDQUFDLDRFQUFDQywwREFBU0E7d0NBQUN5QixXQUFVO2tEQUFpQzs7Ozs7Ozs7Ozs7OENBQ2xFLDhEQUFDM0IsNERBQVdBO29DQUFDMkIsV0FBVTs7c0RBQ3JCLDhEQUFDaEIscUVBQVdBOzRDQUNWK0UsT0FBTTs0Q0FDTjlDLE9BQU9kLE9BQU9nQyxPQUFPOzRDQUNyQjZCLGdCQUFnQmxGLHdEQUFlQSxJQUFJSTs0Q0FDbkMrRSxlQUFlaEM7NENBQ2YyQixhQUFZOzRDQUNaTSxhQUFZOzs7Ozs7c0RBRWQsOERBQUNsRixxRUFBV0E7NENBQ1YrRSxPQUFPNUQsT0FBT2lDLFdBQVcsS0FBSyxtQkFBbUIsYUFBYTs0Q0FDOURuQixPQUFPZCxPQUFPMEIsT0FBTzs0Q0FDckJtQyxnQkFBZ0I3RCxPQUFPaUMsV0FBVyxLQUFLLG1CQUNuQyxDQUFDdEQsd0RBQWVBLElBQUlJLHFCQUFvQixFQUFHb0QsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNcEMsT0FBT2dDLE9BQU8sSUFDMUVwRCx3REFBZUEsSUFBSUk7NENBRXhCOEUsZUFBZXpCOzRDQUNmb0IsYUFBYXpELE9BQU9pQyxXQUFXLEtBQUssbUJBQW1CLHdCQUF3Qjs0Q0FDL0U4QixhQUFhL0QsT0FBT2lDLFdBQVcsS0FBSyxtQkFBbUIsMkNBQTJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXhHLDhEQUFDaEUscURBQUlBOzRCQUFDNEIsV0FBVTs7OENBQ2QsOERBQUMxQiwyREFBVUE7OENBQUMsNEVBQUNDLDBEQUFTQTt3Q0FBQ3lCLFdBQVU7a0RBQWlDOzs7Ozs7Ozs7Ozs4Q0FDbEUsOERBQUMzQiw0REFBV0E7b0NBQUMyQixXQUFVOzt3Q0FDcEI7NENBQ0M7Z0RBQUVnQixNQUFNO2dEQUFXK0MsT0FBTztnREFBdUI3QyxNQUFNO2dEQUFVaUQsTUFBTTs0Q0FBTzs0Q0FDOUU7Z0RBQUVuRCxNQUFNO2dEQUFjK0MsT0FBTztnREFBYzdDLE1BQU07Z0RBQVVpRCxNQUFNOzRDQUFROzRDQUN6RTtnREFBRW5ELE1BQU07Z0RBQWErQyxPQUFPO2dEQUFrQjdDLE1BQU07Z0RBQVVpRCxNQUFNOzRDQUFJOzRDQUN4RTtnREFBRW5ELE1BQU07Z0RBQW1CK0MsT0FBTztnREFBYzdDLE1BQU07Z0RBQVVpRCxNQUFNOzRDQUFPO3lDQUM5RSxDQUFDTixHQUFHLENBQUNPLENBQUFBLHNCQUNKLDhEQUFDbkU7O2tFQUNDLDhEQUFDcEMsdURBQUtBO3dEQUFDNEYsU0FBU1csTUFBTXBELElBQUk7a0VBQUdvRCxNQUFNTCxLQUFLOzs7Ozs7a0VBQ3hDLDhEQUFDbkcsdURBQUtBO3dEQUNKMkYsSUFBSWEsTUFBTXBELElBQUk7d0RBQ2RBLE1BQU1vRCxNQUFNcEQsSUFBSTt3REFDaEJFLE1BQU1rRCxNQUFNbEQsSUFBSTt3REFDaEJELE9BQU9kLE1BQU0sQ0FBQ2lFLE1BQU1wRCxJQUFJLENBQXdCO3dEQUNoRHFELFVBQVV2RDt3REFDVnFELE1BQU1DLE1BQU1ELElBQUk7d0RBQ2hCRyxLQUFJOzs7Ozs7OytDQVRFRixNQUFNcEQsSUFBSTs7Ozs7c0RBYXRCLDhEQUFDZjs7OERBQ0MsOERBQUNwQyx1REFBS0E7OERBQUM7Ozs7Ozs4REFDUCw4REFBQ29DO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ0M7OzhFQUNDLDhEQUFDcEMsdURBQUtBO29FQUFDNEYsU0FBUTtvRUFBNEJ6RCxXQUFVOzt3RUFBV0csT0FBT2dDLE9BQU8sSUFBSTt3RUFBVzs7Ozs7Ozs4RUFDN0YsOERBQUN2RSx1REFBS0E7b0VBQUMyRixJQUFHO29FQUE0QnJDLE1BQUs7b0VBQVNELE9BQU9kLE9BQU80Qyx5QkFBeUI7b0VBQUVzQixVQUFVLENBQUN0RCxJQUFNNkIsd0JBQXdCLDZCQUE2QjdCLEVBQUVLLE1BQU0sQ0FBQ0gsS0FBSztvRUFBR3FELEtBQUk7b0VBQUlDLEtBQUk7Ozs7Ozs7Ozs7OztzRUFFbE0sOERBQUN0RTs7OEVBQ0MsOERBQUNwQyx1REFBS0E7b0VBQUM0RixTQUFRO29FQUE0QnpELFdBQVU7O3dFQUFXRyxPQUFPMEIsT0FBTyxJQUFJO3dFQUFXOzs7Ozs7OzhFQUM3Riw4REFBQ2pFLHVEQUFLQTtvRUFBQzJGLElBQUc7b0VBQTRCckMsTUFBSztvRUFBU0QsT0FBT2QsT0FBTzZDLHlCQUF5QjtvRUFBRXFCLFVBQVUsQ0FBQ3RELElBQU02Qix3QkFBd0IsNkJBQTZCN0IsRUFBRUssTUFBTSxDQUFDSCxLQUFLO29FQUFHcUQsS0FBSTtvRUFBSUMsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUzlNLDhEQUFDdEU7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDdkIsZ0VBQVNBO3dCQUFDdUIsV0FBVTs7Ozs7O2tDQUVyQiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBV04sK0NBQUVBLENBQ2QsZ0hBQ0FlLGNBQWMseUNBQXlDOztvQ0FHeERBLDRCQUFjLDhEQUFDbkIsNkdBQUtBO3dDQUFDVSxXQUFVOzs7OzsrQ0FBZVUsK0JBQWlCLDhEQUFDckIsNkdBQU9BO3dDQUFDVyxXQUFVOzs7Ozs2REFBaUMsOERBQUNULDZHQUFRQTt3Q0FBQ1MsV0FBVTs7Ozs7O29DQUFhO29DQUV4SVMsY0FBYyxZQUFhQyxpQkFBaUIsZUFBZTs7Ozs7OzswQ0FFMUUsOERBQUMvQyx5REFBTUE7Z0NBQUM2RyxTQUFTLElBQU0zRCxxQkFBcUI7Z0NBQU9iLFdBQVU7MENBQXlCOzs7Ozs7MENBQ3RGLDhEQUFDckMseURBQU1BO2dDQUNMNkcsU0FBUztvQ0FDUCxJQUFJL0QsYUFBYTt3Q0FDZkwsU0FBUzs0Q0FBRWMsTUFBTTt3Q0FBa0I7b0NBQ3JDLE9BQU87d0NBQ0xkLFNBQVM7NENBQUVjLE1BQU07d0NBQTRCO29DQUMvQztnQ0FDRjtnQ0FDQWxCLFdBQVdOLCtDQUFFQSxDQUFDLGtCQUFrQmUsZUFBZUMsaUJBQWlCLDJDQUEyQztnQ0FDM0crRCxVQUFVL0Q7O29DQUVURCw0QkFBYyw4REFBQ25CLDZHQUFLQTt3Q0FBQ1UsV0FBVTs7Ozs7K0NBQWVVLCtCQUFpQiw4REFBQ3JCLDZHQUFPQTt3Q0FBQ1csV0FBVTs7Ozs7NkRBQWlDLDhEQUFDVCw2R0FBUUE7d0NBQUNTLFdBQVU7Ozs7OztvQ0FDdklTLGNBQWMsYUFBY0MsaUJBQWlCLGtCQUFrQjs7Ozs7OzswQ0FFbEUsOERBQUMvQyx5REFBTUE7Z0NBQ0w2RyxTQUFTO29DQUNQcEUsU0FBUzt3Q0FBRWMsTUFBTTtvQ0FBbUI7b0NBQ3BDUCxNQUFNO3dDQUNKK0QsT0FBTzt3Q0FDUFIsYUFBYTt3Q0FDYlMsVUFBVTtvQ0FDWjtnQ0FDRjtnQ0FDQUMsU0FBUTtnQ0FDUjVFLFdBQVU7Z0NBQ1Z5RSxVQUFVL0Q7O2tEQUVWLDhEQUFDbEIsNkdBQVNBO3dDQUFDUSxXQUFVOzs7Ozs7b0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0zQyw4REFBQ3RCLGtGQUFnQkE7Z0JBQUNtRyxRQUFRakU7Z0JBQW1Ca0UsU0FBUyxJQUFNakUscUJBQXFCO2dCQUFRa0UsbUJBQW1CdkU7Ozs7Ozs7Ozs7OztBQUdsSDtHQTVTd0JiOztRQXFCSkYsdURBQVFBOzs7S0FyQkpFIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxsYXlvdXRcXFRyYWRpbmdDb25maWdTaWRlYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFkaW5nQ29udGV4dCwgQm90U3lzdGVtU3RhdHVzIH0gZnJvbSAnQC9jb250ZXh0cy9UcmFkaW5nQ29udGV4dCc7XG5cbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIjtcbmltcG9ydCB7IExhYmVsIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9sYWJlbFwiO1xuaW1wb3J0IHsgQ2hlY2tib3ggfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NoZWNrYm94XCI7XG5pbXBvcnQgeyBTZWxlY3QsIFNlbGVjdENvbnRlbnQsIFNlbGVjdEl0ZW0sIFNlbGVjdFRyaWdnZXIsIFNlbGVjdFZhbHVlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zZWxlY3RcIjtcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiO1xuaW1wb3J0IHsgU2Nyb2xsQXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zY3JvbGwtYXJlYSc7XG5pbXBvcnQgeyBTZXBhcmF0b3IgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VwYXJhdG9yJztcbmltcG9ydCB7IFRhcmdldFByaWNlTW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvbW9kYWxzL1RhcmdldFByaWNlTW9kYWwnO1xuXG5pbXBvcnQgeyBBVkFJTEFCTEVfQ1JZUFRPUywgQVZBSUxBQkxFX1FVT1RFU19TSU1QTEUsIEFWQUlMQUJMRV9TVEFCTEVDT0lOUywgVHJhZGluZ01vZGUgfSBmcm9tICdAL2xpYi90eXBlcyc7XG5pbXBvcnQgeyBBTExPV0VEX0NSWVBUTzEsIEFMTE9XRURfQ1JZUFRPMiB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IENyeXB0b0lucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NyeXB0by1pbnB1dCc7XG5cblxuLy8gRGVmaW5lIGxvY2FsbHkgdG8gYXZvaWQgaW1wb3J0IGlzc3Vlc1xuY29uc3QgREVGQVVMVF9RVU9URV9DVVJSRU5DSUVTID0gW1wiVVNEVFwiLCBcIlVTRENcIiwgXCJCVENcIl07XG5cbi8vIERlZmluZSBjcnlwdG8gbGlzdHMgbG9jYWxseSBhcyBmYWxsYmFja1xuY29uc3QgTE9DQUxfQUxMT1dFRF9DUllQVE8xID0gW1xuICBcIkJUQ1wiLCBcIkVUSFwiLCBcIkJOQlwiLCBcIlNPTFwiLCBcIkxJTktcIiwgXCJBVkFYXCIsIFwiRE9UXCIsIFwiVU5JXCIsIFwiTkVBUlwiLCBcIkFBVkVcIixcbiAgXCJBVE9NXCIsIFwiVkVUXCIsIFwiUkVOREVSXCIsIFwiUE9MXCIsIFwiQUxHT1wiLCBcIkFSQlwiLCBcIkZFVFwiLCBcIlBBWEdcIiwgXCJHQUxBXCIsXG4gIFwiQ1JWXCIsIFwiQ09NUFwiLCBcIkVOSlwiXG5dO1xuXG5jb25zdCBMT0NBTF9BTExPV0VEX0NSWVBUTzIgPSBbXG4gIFwiVVNEQ1wiLCBcIkRBSVwiLCBcIlRVU0RcIiwgXCJGRFVTRFwiLCBcIlVTRFRcIiwgXCJFVVJcIlxuXTtcbi8vIEZvcmNlIHJlZnJlc2ggb2Ygc3RhYmxlY29pbnMgbGlzdFxuY29uc3QgU1RBQkxFQ09JTlNfTElTVCA9IFtcIlVTRENcIiwgXCJEQUlcIiwgXCJUVVNEXCIsIFwiRkRVU0RcIiwgXCJVU0RUXCIsIFwiRVVSXCJdO1xuaW1wb3J0IHsgTG9hZGVyMiwgQWxlcnRUcmlhbmdsZSwgUG93ZXIsIFBvd2VyT2ZmLCBSb3RhdGVDY3cgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2hvb2tzL3VzZS10b2FzdCc7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUcmFkaW5nQ29uZmlnU2lkZWJhcigpIHtcbiAgLy8gU2FmZWx5IGdldCB0cmFkaW5nIGNvbnRleHQgd2l0aCBlcnJvciBoYW5kbGluZ1xuICBsZXQgdHJhZGluZ0NvbnRleHQ7XG4gIHRyeSB7XG4gICAgdHJhZGluZ0NvbnRleHQgPSB1c2VUcmFkaW5nQ29udGV4dCgpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1RyYWRpbmcgY29udGV4dCBub3QgYXZhaWxhYmxlOicsIGVycm9yKTtcbiAgICByZXR1cm4gKFxuICAgICAgPGFzaWRlIGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTgwIGxnOnctOTYgYmctc2lkZWJhciB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCBwLTQgYm9yZGVyLXItMiBib3JkZXItc2lkZWJhci1ib3JkZXIgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPlRyYWRpbmcgY29udGV4dCBub3QgYXZhaWxhYmxlLiBQbGVhc2UgcmVmcmVzaCB0aGUgcGFnZS48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hc2lkZT5cbiAgICApO1xuICB9XG5cbiAgY29uc3QgeyBjb25maWcsIGRpc3BhdGNoLCBib3RTeXN0ZW1TdGF0dXMsIGFwcFNldHRpbmdzLCBzZXRUYXJnZXRQcmljZXM6IGNvbnRleHRTZXRUYXJnZXRQcmljZXMgfSA9IHRyYWRpbmdDb250ZXh0O1xuICBjb25zdCBpc0JvdEFjdGl2ZSA9IGJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnO1xuICBjb25zdCBpc0JvdFdhcm1pbmdVcCA9IGJvdFN5c3RlbVN0YXR1cyA9PT0gJ1dhcm1pbmdVcCc7XG5cblxuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuXG4gIGNvbnN0IFtpc1RhcmdldE1vZGFsT3Blbiwgc2V0SXNUYXJnZXRNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG5cblxuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSwgdHlwZSwgY2hlY2tlZCB9ID0gZS50YXJnZXQ7XG4gICAgbGV0IHZhbDogYW55O1xuXG4gICAgaWYgKHR5cGUgPT09ICdjaGVja2JveCcpIHtcbiAgICAgIHZhbCA9IGNoZWNrZWQ7XG4gICAgfSBlbHNlIGlmICh0eXBlID09PSAnbnVtYmVyJykge1xuICAgICAgLy8gSGFuZGxlIGVtcHR5IHN0cmluZyBvciBpbnZhbGlkIG51bWJlcnMgZ3JhY2VmdWxseVxuICAgICAgaWYgKHZhbHVlID09PSAnJyB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHZhbCA9IDA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBwYXJzZUZsb2F0KHZhbHVlKTtcbiAgICAgICAgdmFsID0gaXNOYU4ocGFyc2VkKSA/IDAgOiBwYXJzZWQ7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHZhbCA9IHZhbHVlO1xuICAgIH1cblxuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IFtuYW1lXTogdmFsIH0gfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VsZWN0Q2hhbmdlID0gKG5hbWU6IHN0cmluZywgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IFtuYW1lXTogdmFsdWUgfSB9KTtcbiAgICBpZiAobmFtZSA9PT0gJ2NyeXB0bzEnKSB7XG4gICAgICAvLyBSZXNldCBjcnlwdG8yIGlmIG5ldyBjcnlwdG8xIGRvZXNuJ3Qgc3VwcG9ydCBjdXJyZW50IGNyeXB0bzJcbiAgICAgIGNvbnN0IHZhbGlkUXVvdGVzID0gQVZBSUxBQkxFX1FVT1RFU19TSU1QTEVbdmFsdWUgYXMga2V5b2YgdHlwZW9mIEFWQUlMQUJMRV9RVU9URVNfU0lNUExFXSB8fCBERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMgfHwgW1wiVVNEVFwiLCBcIlVTRENcIiwgXCJCVENcIl07XG4gICAgICBpZiAoIWNvbmZpZy5jcnlwdG8yIHx8ICFBcnJheS5pc0FycmF5KHZhbGlkUXVvdGVzKSB8fCAhdmFsaWRRdW90ZXMuaW5jbHVkZXMoY29uZmlnLmNyeXB0bzIpKSB7XG4gICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IGNyeXB0bzI6IHZhbGlkUXVvdGVzWzBdIHx8ICdVU0RUJyB9IH0pOyAvLyBFbnN1cmUgY3J5cHRvMiBnZXRzIGEgdmFsaWQgZGVmYXVsdFxuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDcnlwdG8xU2VsZWN0aW9uID0gKGNyeXB0bzogc3RyaW5nKSA9PiB7XG4gICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgY3J5cHRvMTogY3J5cHRvIH0gfSk7XG4gICAgLy8gUmVzZXQgY3J5cHRvMiBiYXNlZCBvbiB0cmFkaW5nIG1vZGUgd2hlbiBjcnlwdG8xIGNoYW5nZXNcbiAgICBpZiAoY29uZmlnLnRyYWRpbmdNb2RlID09PSBcIlN0YWJsZWNvaW5Td2FwXCIpIHtcbiAgICAgIC8vIEluIHN0YWJsZWNvaW4gc3dhcCBtb2RlLCBjcnlwdG8yIHNob3VsZCBiZSBmcm9tIEFMTE9XRURfQ1JZUFRPMSBhbmQgZGlmZmVyZW50IGZyb20gY3J5cHRvMVxuICAgICAgY29uc3QgYWxsb3dlZENyeXB0bzIgPSAoQUxMT1dFRF9DUllQVE8xIHx8IExPQ0FMX0FMTE9XRURfQ1JZUFRPMSkuZmlsdGVyKGMgPT4gYyAhPT0gY3J5cHRvKTtcbiAgICAgIGlmICghYWxsb3dlZENyeXB0bzIuaW5jbHVkZXMoY29uZmlnLmNyeXB0bzIpIHx8IGNvbmZpZy5jcnlwdG8yID09PSBjcnlwdG8pIHtcbiAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgY3J5cHRvMjogYWxsb3dlZENyeXB0bzJbMF0gfSB9KTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gSW4gc2ltcGxlIHNwb3QgbW9kZSwgY3J5cHRvMiBzaG91bGQgYmUgZnJvbSBBTExPV0VEX0NSWVBUTzJcbiAgICAgIGNvbnN0IGFsbG93ZWRDcnlwdG8yID0gQUxMT1dFRF9DUllQVE8yIHx8IExPQ0FMX0FMTE9XRURfQ1JZUFRPMjtcbiAgICAgIGlmICghYWxsb3dlZENyeXB0bzIuaW5jbHVkZXMoY29uZmlnLmNyeXB0bzIpKSB7XG4gICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IGNyeXB0bzI6IGFsbG93ZWRDcnlwdG8yWzBdIH0gfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNyeXB0bzJTZWxlY3Rpb24gPSAoY3J5cHRvOiBzdHJpbmcpID0+IHtcbiAgICAvLyBJbiBzdGFibGVjb2luIHN3YXAgbW9kZSwgZW5zdXJlIGNyeXB0bzIgaXMgZGlmZmVyZW50IGZyb20gY3J5cHRvMVxuICAgIGlmIChjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIiAmJiBjcnlwdG8gPT09IGNvbmZpZy5jcnlwdG8xKSB7XG4gICAgICAvLyBEb24ndCBhbGxvdyBzZWxlY3RpbmcgdGhlIHNhbWUgY3J5cHRvIGFzIGNyeXB0bzEgaW4gc3dhcCBtb2RlXG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IGNyeXB0bzI6IGNyeXB0byB9IH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVRyYWRpbmdNb2RlQ2hhbmdlID0gKGNoZWNrZWQ6IGJvb2xlYW4pID0+IHtcbiAgICBjb25zdCBuZXdNb2RlOiBUcmFkaW5nTW9kZSA9IGNoZWNrZWQgPyBcIlN0YWJsZWNvaW5Td2FwXCIgOiBcIlNpbXBsZVNwb3RcIjtcblxuICAgIC8vIFJlc2V0IGNyeXB0bzIgYmFzZWQgb24gdGhlIG5ldyB0cmFkaW5nIG1vZGVcbiAgICBsZXQgbmV3Q3J5cHRvMjogc3RyaW5nO1xuICAgIGlmIChuZXdNb2RlID09PSBcIlN0YWJsZWNvaW5Td2FwXCIpIHtcbiAgICAgIC8vIEluIHN0YWJsZWNvaW4gc3dhcCBtb2RlLCBjcnlwdG8yIHNob3VsZCBiZSBmcm9tIEFMTE9XRURfQ1JZUFRPMSBhbmQgZGlmZmVyZW50IGZyb20gY3J5cHRvMVxuICAgICAgY29uc3QgYWxsb3dlZENyeXB0bzIgPSAoQUxMT1dFRF9DUllQVE8xIHx8IExPQ0FMX0FMTE9XRURfQ1JZUFRPMSkuZmlsdGVyKGMgPT4gYyAhPT0gY29uZmlnLmNyeXB0bzEpO1xuICAgICAgbmV3Q3J5cHRvMiA9IGFsbG93ZWRDcnlwdG8yWzBdO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBJbiBzaW1wbGUgc3BvdCBtb2RlLCBjcnlwdG8yIHNob3VsZCBiZSBmcm9tIEFMTE9XRURfQ1JZUFRPMlxuICAgICAgY29uc3QgYWxsb3dlZENyeXB0bzIgPSBBTExPV0VEX0NSWVBUTzIgfHwgTE9DQUxfQUxMT1dFRF9DUllQVE8yO1xuICAgICAgbmV3Q3J5cHRvMiA9IGFsbG93ZWRDcnlwdG8yWzBdO1xuICAgIH1cblxuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IHRyYWRpbmdNb2RlOiBuZXdNb2RlLCBjcnlwdG8yOiBuZXdDcnlwdG8yIH0gfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW5jb21lU3BsaXRDaGFuZ2UgPSAoY3J5cHRvS2V5OiAnaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudCcgfCAnaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCcsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBsZXQgcGVyY2VudCA9IHBhcnNlRmxvYXQodmFsdWUpO1xuICAgIGlmIChpc05hTihwZXJjZW50KSkgcGVyY2VudCA9IDA7XG4gICAgaWYgKHBlcmNlbnQgPCAwKSBwZXJjZW50ID0gMDtcbiAgICBpZiAocGVyY2VudCA+IDEwMCkgcGVyY2VudCA9IDEwMDtcblxuICAgIGlmIChjcnlwdG9LZXkgPT09ICdpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50Jykge1xuICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudDogcGVyY2VudCwgaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudDogMTAwIC0gcGVyY2VudCB9IH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQ09ORklHJywgcGF5bG9hZDogeyBpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50OiBwZXJjZW50LCBpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50OiAxMDAgLSBwZXJjZW50IH0gfSk7XG4gICAgfVxuICB9O1xuXG5cblxuXG5cblxuXG5cbiAgLy8gVXNlIHN0YXRpYyBjcnlwdG8gbGlzdHNcbiAgY29uc3QgY3J5cHRvMU9wdGlvbnMgPSBBVkFJTEFCTEVfQ1JZUFRPUyB8fCBbXTtcbiAgY29uc3QgY3J5cHRvMk9wdGlvbnMgPSBjb25maWcudHJhZGluZ01vZGUgPT09IFwiU2ltcGxlU3BvdFwiXG4gICAgPyAoQVZBSUxBQkxFX1FVT1RFU19TSU1QTEVbY29uZmlnLmNyeXB0bzEgYXMga2V5b2YgdHlwZW9mIEFWQUlMQUJMRV9RVU9URVNfU0lNUExFXSB8fCBERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMgfHwgW1wiVVNEVFwiLCBcIlVTRENcIiwgXCJCVENcIl0pXG4gICAgOiAoQVZBSUxBQkxFX0NSWVBUT1MgfHwgW10pLmZpbHRlcihjID0+IGMgIT09IGNvbmZpZy5jcnlwdG8xKTsgLy8gRW5zdXJlIENyeXB0bzIgaXMgbm90IHNhbWUgYXMgQ3J5cHRvMSBpbiBTd2FwIG1vZGVcblxuICAvLyBEZWJ1ZyBsb2cgdG8gc2VlIHdoYXQncyBhY3R1YWxseSBsb2FkZWRcbiAgY29uc29sZS5sb2coJ/CflI0gREVCVUc6IEFWQUlMQUJMRV9DUllQVE9TIGxlbmd0aDonLCBBVkFJTEFCTEVfQ1JZUFRPUz8ubGVuZ3RoKTtcbiAgY29uc29sZS5sb2coJ/CflI0gREVCVUc6IGNyeXB0bzFPcHRpb25zIGxlbmd0aDonLCBjcnlwdG8xT3B0aW9ucy5sZW5ndGgpO1xuICBjb25zb2xlLmxvZygn8J+UjSBERUJVRzogRmlyc3QgMjAgY3J5cHRvczonLCBBVkFJTEFCTEVfQ1JZUFRPUz8uc2xpY2UoMCwgMjApKTtcbiAgY29uc29sZS5sb2coJ/CflI0gREVCVUc6IEFMTE9XRURfQ1JZUFRPMTonLCBBTExPV0VEX0NSWVBUTzEpO1xuICBjb25zb2xlLmxvZygn8J+UjSBERUJVRzogQUxMT1dFRF9DUllQVE8yOicsIEFMTE9XRURfQ1JZUFRPMik7XG4gIGNvbnNvbGUubG9nKCfwn5SNIERFQlVHOiBBVkFJTEFCTEVfU1RBQkxFQ09JTlM6JywgQVZBSUxBQkxFX1NUQUJMRUNPSU5TKTtcblxuICByZXR1cm4gKFxuICAgIDxhc2lkZSBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy04MCBsZzp3LTk2IGJnLXNpZGViYXIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgcC00IGJvcmRlci1yLTIgYm9yZGVyLXNpZGViYXItYm9yZGVyIGZsZXggZmxleC1jb2wgaC1zY3JlZW5cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXNpZGViYXItcHJpbWFyeVwiPlRyYWRpbmcgQ29uZmlndXJhdGlvbjwvaDI+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxTY3JvbGxBcmVhIGNsYXNzTmFtZT1cImZsZXgtMSBwci0yXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgey8qIFRyYWRpbmcgTW9kZSAqL31cbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1zaWRlYmFyLWFjY2VudCBib3JkZXItc2lkZWJhci1ib3JkZXJcIj5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZFwiPlRyYWRpbmcgTW9kZTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja2JveFxuICAgICAgICAgICAgICAgICAgaWQ9XCJlbmFibGVTdGFibGVjb2luU3dhcFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17aGFuZGxlVHJhZGluZ01vZGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImVuYWJsZVN0YWJsZWNvaW5Td2FwXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCI+XG4gICAgICAgICAgICAgICAgICBFbmFibGUgU3RhYmxlY29pbiBTd2FwIE1vZGVcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICBDdXJyZW50IG1vZGU6IHtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU2ltcGxlU3BvdFwiID8gXCJTaW1wbGUgU3BvdFwiIDogXCJTdGFibGVjb2luIFN3YXBcIn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7Y29uZmlnLnRyYWRpbmdNb2RlID09PSBcIlN0YWJsZWNvaW5Td2FwXCIgJiYgKFxuICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwcmVmZXJyZWRTdGFibGVjb2luXCI+UHJlZmVycmVkIFN0YWJsZWNvaW48L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdCBuYW1lPVwicHJlZmVycmVkU3RhYmxlY29pblwiIHZhbHVlPXtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn0gb25WYWx1ZUNoYW5nZT17KHZhbCkgPT4gaGFuZGxlU2VsZWN0Q2hhbmdlKFwicHJlZmVycmVkU3RhYmxlY29pblwiLCB2YWwgYXMgc3RyaW5nKX0+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGlkPVwicHJlZmVycmVkU3RhYmxlY29pblwiPjxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjdCBzdGFibGVjb2luXCIgLz48L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50IGNsYXNzTmFtZT1cIm1heC1oLVszMDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAge1NUQUJMRUNPSU5TX0xJU1QubWFwKHNjID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17c2N9IHZhbHVlPXtzY30+e3NjfTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuXG5cbiAgICAgICAgICB7LyogVHJhZGluZyBQYWlyICovfVxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXNpZGViYXItYWNjZW50IGJvcmRlci1zaWRlYmFyLWJvcmRlclwiPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+PENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmRcIj5UcmFkaW5nIFBhaXI8L0NhcmRUaXRsZT48L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxDcnlwdG9JbnB1dFxuICAgICAgICAgICAgICAgIGxhYmVsPVwiQ3J5cHRvIDEgKEJhc2UpXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Y29uZmlnLmNyeXB0bzF9XG4gICAgICAgICAgICAgICAgYWxsb3dlZENyeXB0b3M9e0FMTE9XRURfQ1JZUFRPMSB8fCBMT0NBTF9BTExPV0VEX0NSWVBUTzF9XG4gICAgICAgICAgICAgICAgb25WYWxpZENyeXB0bz17aGFuZGxlQ3J5cHRvMVNlbGVjdGlvbn1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIEJUQywgRVRILCBTT0xcIlxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiRW50ZXIgdGhlIGJhc2UgY3J5cHRvY3VycmVuY3kgc3ltYm9sXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPENyeXB0b0lucHV0XG4gICAgICAgICAgICAgICAgbGFiZWw9e2NvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiID8gXCJDcnlwdG8gMlwiIDogXCJDcnlwdG8gMiAoU3RhYmxlY29pbilcIn1cbiAgICAgICAgICAgICAgICB2YWx1ZT17Y29uZmlnLmNyeXB0bzJ9XG4gICAgICAgICAgICAgICAgYWxsb3dlZENyeXB0b3M9e2NvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiXG4gICAgICAgICAgICAgICAgICA/IChBTExPV0VEX0NSWVBUTzEgfHwgTE9DQUxfQUxMT1dFRF9DUllQVE8xKS5maWx0ZXIoYyA9PiBjICE9PSBjb25maWcuY3J5cHRvMSlcbiAgICAgICAgICAgICAgICAgIDogKEFMTE9XRURfQ1JZUFRPMiB8fCBMT0NBTF9BTExPV0VEX0NSWVBUTzIpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIG9uVmFsaWRDcnlwdG89e2hhbmRsZUNyeXB0bzJTZWxlY3Rpb259XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2NvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiID8gXCJlLmcuLCBCVEMsIEVUSCwgU09MXCIgOiBcImUuZy4sIFVTRFQsIFVTREMsIERBSVwifVxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIiA/IFwiRW50ZXIgdGhlIHNlY29uZCBjcnlwdG9jdXJyZW5jeSBzeW1ib2xcIiA6IFwiRW50ZXIgdGhlIHF1b3RlL3N0YWJsZWNvaW4gc3ltYm9sXCJ9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIHsvKiBQYXJhbWV0ZXJzICovfVxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXNpZGViYXItYWNjZW50IGJvcmRlci1zaWRlYmFyLWJvcmRlclwiPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+PENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmRcIj5QYXJhbWV0ZXJzPC9DYXJkVGl0bGU+PC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICB7W1xuICAgICAgICAgICAgICAgIHsgbmFtZTogXCJiYXNlQmlkXCIsIGxhYmVsOiBcIkJhc2UgQmlkIChDcnlwdG8gMilcIiwgdHlwZTogXCJudW1iZXJcIiwgc3RlcDogXCIwLjAxXCIgfSxcbiAgICAgICAgICAgICAgICB7IG5hbWU6IFwibXVsdGlwbGllclwiLCBsYWJlbDogXCJNdWx0aXBsaWVyXCIsIHR5cGU6IFwibnVtYmVyXCIsIHN0ZXA6IFwiMC4wMDFcIiB9LFxuICAgICAgICAgICAgICAgIHsgbmFtZTogXCJudW1EaWdpdHNcIiwgbGFiZWw6IFwiRGlzcGxheSBEaWdpdHNcIiwgdHlwZTogXCJudW1iZXJcIiwgc3RlcDogXCIxXCIgfSxcbiAgICAgICAgICAgICAgICB7IG5hbWU6IFwic2xpcHBhZ2VQZXJjZW50XCIsIGxhYmVsOiBcIlNsaXBwYWdlICVcIiwgdHlwZTogXCJudW1iZXJcIiwgc3RlcDogXCIwLjAxXCIgfSxcbiAgICAgICAgICAgICAgXS5tYXAoZmllbGQgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtmaWVsZC5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPXtmaWVsZC5uYW1lfT57ZmllbGQubGFiZWx9PC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD17ZmllbGQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgbmFtZT17ZmllbGQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgdHlwZT17ZmllbGQudHlwZX1cbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZ1tmaWVsZC5uYW1lIGFzIGtleW9mIHR5cGVvZiBjb25maWddIGFzIHN0cmluZyB8IG51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBzdGVwPXtmaWVsZC5zdGVwfVxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMYWJlbD5Db3VwbGUgSW5jb21lICUgU3BsaXQgKG11c3Qgc3VtIHRvIDEwMCk8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50XCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPntjb25maWcuY3J5cHRvMSB8fCBcIkNyeXB0byAxXCJ9JTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dCBpZD1cImluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnRcIiB0eXBlPVwibnVtYmVyXCIgdmFsdWU9e2NvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50fSBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUluY29tZVNwbGl0Q2hhbmdlKCdpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50JywgZS50YXJnZXQudmFsdWUpfSBtaW49XCIwXCIgbWF4PVwiMTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50XCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPntjb25maWcuY3J5cHRvMiB8fCBcIkNyeXB0byAyXCJ9JTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dCBpZD1cImluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnRcIiB0eXBlPVwibnVtYmVyXCIgdmFsdWU9e2NvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50fSBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUluY29tZVNwbGl0Q2hhbmdlKCdpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50JywgZS50YXJnZXQudmFsdWUpfSBtaW49XCIwXCIgbWF4PVwiMTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvU2Nyb2xsQXJlYT5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIG10LTRcIj5cbiAgICAgICAgPFNlcGFyYXRvciBjbGFzc05hbWU9XCJtYi00IGJnLXNpZGViYXItYm9yZGVyXCIgLz5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgXCJ0ZXh0LWNlbnRlciB0ZXh0LXNtIGZvbnQtbWVkaXVtIHAtMiByb3VuZGVkLW1kIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIGJvcmRlci0yIGJvcmRlci1ib3JkZXJcIixcbiAgICAgICAgICAgICAgaXNCb3RBY3RpdmUgPyBcImJnLWdyZWVuLTYwMCB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZFwiIDogXCJiZy1tdXRlZCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNCb3RBY3RpdmUgPyA8UG93ZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiLz4gOiAoaXNCb3RXYXJtaW5nVXAgPyA8TG9hZGVyMiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz4gOiA8UG93ZXJPZmYgY2xhc3NOYW1lPVwiaC00IHctNFwiLz4pfVxuXG4gICAgICAgICAgICBCb3QgU3RhdHVzOiB7aXNCb3RBY3RpdmUgPyAnUnVubmluZycgOiAoaXNCb3RXYXJtaW5nVXAgPyAnV2FybWluZyBVcCcgOiAnU3RvcHBlZCcpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gc2V0SXNUYXJnZXRNb2RhbE9wZW4odHJ1ZSl9IGNsYXNzTmFtZT1cInctZnVsbCBidG4tb3V0bGluZS1uZW9cIj5TZXQgVGFyZ2V0IFByaWNlczwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgaWYgKGlzQm90QWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU1lTVEVNX1NUT1BfQk9UJyB9KTtcbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTWVNURU1fU1RBUlRfQk9UX0lOSVRJQVRFJyB9KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oJ3ctZnVsbCBidG4tbmVvJywgaXNCb3RBY3RpdmUgfHwgaXNCb3RXYXJtaW5nVXAgPyAnYmctZGVzdHJ1Y3RpdmUgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTAnIDogJ2JnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi02MDAvOTAnKX1cbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0JvdFdhcm1pbmdVcH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNCb3RBY3RpdmUgPyA8UG93ZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiLz4gOiAoaXNCb3RXYXJtaW5nVXAgPyA8TG9hZGVyMiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz4gOiA8UG93ZXJPZmYgY2xhc3NOYW1lPVwiaC00IHctNFwiLz4pfVxuICAgICAgICAgICAge2lzQm90QWN0aXZlID8gJ1N0b3AgQm90JyA6IChpc0JvdFdhcm1pbmdVcCA/ICdXYXJtaW5nIFVwLi4uJyA6ICdTdGFydCBCb3QnKX1cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NZU1RFTV9SRVNFVF9CT1QnIH0pO1xuICAgICAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICAgICAgdGl0bGU6IFwiQm90IFJlc2V0XCIsXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiVHJhZGluZyBib3QgaGFzIGJlZW4gcmVzZXQgdG8gZnJlc2ggc3RhdGUuIEFsbCBwb3NpdGlvbnMgY2xlYXJlZC4gU2F2ZWQgc2Vzc2lvbnMgYXJlIHByZXNlcnZlZC5cIixcbiAgICAgICAgICAgICAgICBkdXJhdGlvbjogNDAwMFxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLW91dGxpbmUtbmVvXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0JvdFdhcm1pbmdVcH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Um90YXRlQ2N3IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiLz5cbiAgICAgICAgICAgIFJlc2V0IEJvdFxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8VGFyZ2V0UHJpY2VNb2RhbCBpc09wZW49e2lzVGFyZ2V0TW9kYWxPcGVufSBvbkNsb3NlPXsoKSA9PiBzZXRJc1RhcmdldE1vZGFsT3BlbihmYWxzZSl9IG9uU2V0VGFyZ2V0UHJpY2VzPXtjb250ZXh0U2V0VGFyZ2V0UHJpY2VzfSAvPlxuICAgIDwvYXNpZGU+XG4gICk7XG59XG5cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlVHJhZGluZ0NvbnRleHQiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiQ2hlY2tib3giLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJTY3JvbGxBcmVhIiwiU2VwYXJhdG9yIiwiVGFyZ2V0UHJpY2VNb2RhbCIsIkFWQUlMQUJMRV9DUllQVE9TIiwiQVZBSUxBQkxFX1FVT1RFU19TSU1QTEUiLCJBVkFJTEFCTEVfU1RBQkxFQ09JTlMiLCJBTExPV0VEX0NSWVBUTzEiLCJBTExPV0VEX0NSWVBUTzIiLCJDcnlwdG9JbnB1dCIsIkRFRkFVTFRfUVVPVEVfQ1VSUkVOQ0lFUyIsIkxPQ0FMX0FMTE9XRURfQ1JZUFRPMSIsIkxPQ0FMX0FMTE9XRURfQ1JZUFRPMiIsIlNUQUJMRUNPSU5TX0xJU1QiLCJMb2FkZXIyIiwiUG93ZXIiLCJQb3dlck9mZiIsIlJvdGF0ZUNjdyIsInVzZVRvYXN0IiwiY24iLCJUcmFkaW5nQ29uZmlnU2lkZWJhciIsInRyYWRpbmdDb250ZXh0IiwiZXJyb3IiLCJjb25zb2xlIiwiYXNpZGUiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwiY29uZmlnIiwiZGlzcGF0Y2giLCJib3RTeXN0ZW1TdGF0dXMiLCJhcHBTZXR0aW5ncyIsInNldFRhcmdldFByaWNlcyIsImNvbnRleHRTZXRUYXJnZXRQcmljZXMiLCJpc0JvdEFjdGl2ZSIsImlzQm90V2FybWluZ1VwIiwidG9hc3QiLCJpc1RhcmdldE1vZGFsT3BlbiIsInNldElzVGFyZ2V0TW9kYWxPcGVuIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJlIiwibmFtZSIsInZhbHVlIiwidHlwZSIsImNoZWNrZWQiLCJ0YXJnZXQiLCJ2YWwiLCJ1bmRlZmluZWQiLCJwYXJzZWQiLCJwYXJzZUZsb2F0IiwiaXNOYU4iLCJwYXlsb2FkIiwiaGFuZGxlU2VsZWN0Q2hhbmdlIiwidmFsaWRRdW90ZXMiLCJjcnlwdG8yIiwiQXJyYXkiLCJpc0FycmF5IiwiaW5jbHVkZXMiLCJoYW5kbGVDcnlwdG8xU2VsZWN0aW9uIiwiY3J5cHRvIiwiY3J5cHRvMSIsInRyYWRpbmdNb2RlIiwiYWxsb3dlZENyeXB0bzIiLCJmaWx0ZXIiLCJjIiwiaGFuZGxlQ3J5cHRvMlNlbGVjdGlvbiIsImhhbmRsZVRyYWRpbmdNb2RlQ2hhbmdlIiwibmV3TW9kZSIsIm5ld0NyeXB0bzIiLCJoYW5kbGVJbmNvbWVTcGxpdENoYW5nZSIsImNyeXB0b0tleSIsInBlcmNlbnQiLCJpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50IiwiaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCIsImNyeXB0bzFPcHRpb25zIiwiY3J5cHRvMk9wdGlvbnMiLCJsb2ciLCJsZW5ndGgiLCJzbGljZSIsImgyIiwiaWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJodG1sRm9yIiwicHJlZmVycmVkU3RhYmxlY29pbiIsIm9uVmFsdWVDaGFuZ2UiLCJwbGFjZWhvbGRlciIsIm1hcCIsInNjIiwibGFiZWwiLCJhbGxvd2VkQ3J5cHRvcyIsIm9uVmFsaWRDcnlwdG8iLCJkZXNjcmlwdGlvbiIsInN0ZXAiLCJmaWVsZCIsIm9uQ2hhbmdlIiwibWluIiwibWF4Iiwib25DbGljayIsImRpc2FibGVkIiwidGl0bGUiLCJkdXJhdGlvbiIsInZhcmlhbnQiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TZXRUYXJnZXRQcmljZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});