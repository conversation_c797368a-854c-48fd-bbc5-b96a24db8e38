"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSessionForAlarm, setSelectedSessionForAlarm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = await sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance,\n                stablecoin: session.stablecoinBalance\n            }\n        });\n        // Set bot status based on session's active state\n        if (session.isActive) {\n            dispatch({\n                type: 'SYSTEM_START_BOT_INITIATE'\n            });\n        // The warmup will complete immediately due to the useEffect in TradingContext\n        } else {\n            dispatch({\n                type: 'SYSTEM_STOP_BOT'\n            });\n        }\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded. Redirecting to dashboard...')\n        });\n        // Redirect to dashboard after loading session\n        setTimeout(()=>{\n            window.location.href = '/dashboard';\n        }, 1000);\n    };\n    const handleDeleteSession = async (sessionId)=>{\n        const success = await sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleOpenAlarmConfig = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (session) {\n            setSelectedSessionForAlarm({\n                id: sessionId,\n                name: session.name,\n                alarmSettings: session.alarmSettings\n            });\n            setAlarmModalOpen(true);\n        }\n    };\n    const handleSaveAlarmSettings = async (sessionId, alarmSettings)=>{\n        const success = await sessionManager.updateSessionAlarmSettings(sessionId, alarmSettings);\n        if (success) {\n            loadSessions();\n            toast({\n                title: \"Alarm Settings Saved\",\n                description: \"Session alarm settings have been updated\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save alarm settings\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getActiveSessions = ()=>{\n        // Show all sessions that are marked as active (including those in other browser tabs)\n        // This allows users to see all currently running sessions across all windows\n        return sessions.filter((s)=>s.isActive);\n    };\n    const getInactiveSessions = ()=>{\n        return sessions.filter((s)=>!s.isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Alarm\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyDown: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                        title: \"Configure Alarms\",\n                                                        className: \"btn-outline-neo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleSaveCurrentSession,\n                                                        size: \"sm\",\n                                                        className: \"btn-neo\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Save\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, \"active-\".concat(session.id, \"-\").concat(index), true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Alarm\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyDown: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 533,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                            title: \"Configure Alarms\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"inactive-\".concat(session.id, \"-\").concat(index), true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this),\n            selectedSessionForAlarm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__.SessionAlarmConfigModal, {\n                isOpen: alarmModalOpen,\n                onClose: ()=>{\n                    setAlarmModalOpen(false);\n                    setSelectedSessionForAlarm(null);\n                },\n                sessionId: selectedSessionForAlarm.id,\n                sessionName: selectedSessionForAlarm.name,\n                currentAlarmSettings: selectedSessionForAlarm.alarmSettings,\n                onSave: handleSaveAlarmSettings\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 583,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"qvvjKP1M0ciSaXHnASZ/CLpomBw=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});