"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // Try multiple API endpoints for better coverage\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 60000; // 1 minute cache\n// Helper function to get real-time USD price from API with fallback (async version)\nconst getRealTimeUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Try to fetch real-time price from CoinGecko API\n        const coinGeckoId = getCoinGeckoId(crypto);\n        if (coinGeckoId) {\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"));\n            if (response.ok) {\n                var _data_coinGeckoId;\n                const data = await response.json();\n                const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                if (price && price > 0) {\n                    priceCache[cacheKey] = price;\n                    lastPriceUpdate = now;\n                    console.log(\"\\uD83D\\uDCCA Real-time price fetched for \".concat(crypto, \": $\").concat(price));\n                    return price;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn(\"⚠️ Failed to fetch real-time price for \".concat(crypto, \", using fallback:\"), error);\n    }\n    // Fallback to static prices if API fails\n    return getUSDPrice(crypto);\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data with balances:', {\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance\n            });\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                // For StablecoinSwap mode, use real-time prices for accurate market calculation\n                if (state.config.tradingMode === \"StablecoinSwap\") {\n                    // Try to get real-time prices first\n                    try {\n                        const crypto1USDPrice = await getRealTimeUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = await getRealTimeUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Real-time):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6), \"\\n            - Calculation: \").concat(crypto1USDPrice, \" \\xf7 \").concat(crypto2USDPrice, \" = \").concat(price.toFixed(6)));\n                    } catch (error) {\n                        // Fallback to cached/static prices\n                        const crypto1USDPrice = getUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = getUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Fallback):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    }\n                } else {\n                    // For SimpleSpot mode, use the existing API-based approach\n                    price = await getMarketPriceFromAPI(state.config);\n                }\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                sendAPIErrorNotification('Price API', \"Failed to fetch market price: \".concat(error)).catch(console.error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 1000); // Update every 1 second (1000ms) as requested\n            // Set up real-time price cache update (every 2 minutes)\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline && state.config.tradingMode === \"StablecoinSwap\") {\n                        try {\n                            // Update cache for current trading pair\n                            await getRealTimeUSDPrice(state.config.crypto1);\n                            await getRealTimeUSDPrice(state.config.crypto2);\n                            console.log('📊 Real-time price cache updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update price cache:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 120000); // Update every 2 minutes\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1924,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"Dul/342NNfDBwKH7syILMVemn7Y=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});