"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\n// Helper function to get real-time market price from Binance API\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // Primary: Try Binance API with multiple symbol formats\n        const symbol1 = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        const symbol2 = \"\".concat(config.crypto1, \"USDT\").toUpperCase(); // For cross-calculation\n        const symbol3 = \"\".concat(config.crypto2, \"USDT\").toUpperCase(); // For cross-calculation\n        // Try direct pair first\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol1));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Direct price from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (error) {\n            console.warn(\"Direct pair \".concat(symbol1, \" not found on Binance, trying cross-calculation...\"));\n        }\n        // Try cross-calculation via USDT if direct pair not available\n        try {\n            const [response1, response2] = await Promise.all([\n                fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol2)),\n                fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol3))\n            ]);\n            if (response1.ok && response2.ok) {\n                const [data1, data2] = await Promise.all([\n                    response1.json(),\n                    response2.json()\n                ]);\n                const price1 = parseFloat(data1.price); // crypto1/USDT\n                const price2 = parseFloat(data2.price); // crypto2/USDT\n                if (price1 > 0 && price2 > 0) {\n                    const crossPrice = price1 / price2; // crypto1/crypto2\n                    console.log(\"✅ Cross-calculated price from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(crossPrice, \" (\").concat(price1, \"/\").concat(price2, \")\"));\n                    return crossPrice;\n                }\n            }\n        } catch (error) {\n            console.warn('Binance cross-calculation failed, trying CoinGecko...', error);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using USD calculation...', geckoError);\n        }\n        // Final fallback - calculate from real-time USD prices\n        try {\n            const crypto1USDPrice = await getRealTimeUSDPrice(config.crypto1);\n            const crypto2USDPrice = await getRealTimeUSDPrice(config.crypto2);\n            const calculatedPrice = crypto1USDPrice / crypto2USDPrice;\n            console.log(\"\\uD83D\\uDCCA Calculated price from real-time USD rates: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(calculatedPrice));\n            return calculatedPrice;\n        } catch (error) {\n            console.warn('Real-time USD price calculation failed, using static fallback...', error);\n        }\n        // Final fallback to static calculation\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using static fallback price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache - shorter duration for more frequent updates\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 5000; // 5 seconds cache for real-time updates\n// Telegram notification queue for offline periods\nlet telegramNotificationQueue = [];\nconst MAX_QUEUE_SIZE = 10; // Limit queue size to prevent memory issues\n// Helper function to get real-time USD price from API with fallback (async version)\nconst getRealTimeUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Try to fetch real-time price from CoinGecko API\n        const coinGeckoId = getCoinGeckoId(crypto);\n        if (coinGeckoId) {\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"));\n            if (response.ok) {\n                var _data_coinGeckoId;\n                const data = await response.json();\n                const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                if (price && price > 0) {\n                    priceCache[cacheKey] = price;\n                    lastPriceUpdate = now;\n                    console.log(\"\\uD83D\\uDCCA Real-time price fetched for \".concat(crypto, \": $\").concat(price));\n                    return price;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn(\"⚠️ Failed to fetch real-time price for \".concat(crypto, \", using fallback:\"), error);\n    }\n    // Fallback to static prices if API fails\n    return getUSDPrice(crypto);\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // CRITICAL SAFETY CHECK: Only start bot if target prices are set\n            if (!state.targetPriceRows || state.targetPriceRows.length === 0) {\n                console.error('🚨 SAFETY BLOCK: Cannot start bot without target prices!');\n                return {\n                    ...state,\n                    botSystemStatus: 'Stopped' // Force stop if no target prices\n                };\n            }\n            // Additional safety checks\n            if (!state.config.crypto1 || !state.config.crypto2) {\n                console.error('🚨 SAFETY BLOCK: Cannot start bot without valid crypto pair configuration!');\n                return {\n                    ...state,\n                    botSystemStatus: 'Stopped'\n                };\n            }\n            if (state.currentMarketPrice <= 0) {\n                console.error('🚨 SAFETY BLOCK: Cannot start bot without valid market price!');\n                return {\n                    ...state,\n                    botSystemStatus: 'Stopped'\n                };\n            }\n            console.log('✅ SAFETY CHECK PASSED: Starting bot with', state.targetPriceRows.length, 'target prices');\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            // CRITICAL SAFETY CHECK: Only complete warmup if target prices are still set\n            if (!state.targetPriceRows || state.targetPriceRows.length === 0) {\n                console.error('🚨 SAFETY BLOCK: Cannot complete warmup without target prices! Stopping bot.');\n                return {\n                    ...state,\n                    botSystemStatus: 'Stopped'\n                };\n            }\n            console.log('✅ WARMUP COMPLETE: Bot is now running with', state.targetPriceRows.length, 'target prices');\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            // Safety check: If bot is running and target prices are being cleared, stop the bot\n            if (state.botSystemStatus === 'Running' && (!action.payload || action.payload.length === 0)) {\n                console.warn('🚨 SAFETY AUTO-STOP: Target prices cleared while bot was running. Stopping bot.');\n                return {\n                    ...state,\n                    targetPriceRows: action.payload,\n                    botSystemStatus: 'Stopped'\n                };\n            }\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const currentSession = sessionManager.loadSession(currentSessionId);\n        if (currentSession) {\n            console.log('🔄 Restoring active session after page refresh:', {\n                sessionId: currentSessionId,\n                name: currentSession.name,\n                isActive: currentSession.isActive,\n                crypto1: currentSession.crypto1Balance,\n                crypto2: currentSession.crypto2Balance,\n                stablecoin: currentSession.stablecoinBalance,\n                targetPriceRows: currentSession.targetPriceRows.length,\n                orderHistory: currentSession.orderHistory.length\n            });\n            // Ensure the session remains active if it was active before refresh\n            if (currentSession.isActive) {\n                console.log('✅ Maintaining active session state after page refresh');\n            }\n            return {\n                ...initialTradingState,\n                config: currentSession.config,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: currentSession.isActive ? 'Running' : 'Stopped'\n            };\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Enhanced Telegram notification function with error handling, retry logic, and offline queue\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                // Check if we're online before attempting to send\n                const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                if (!networkMonitor.getStatus().isOnline) {\n                    // Queue the notification for when we're back online\n                    if (telegramNotificationQueue.length < MAX_QUEUE_SIZE) {\n                        telegramNotificationQueue.push({\n                            message,\n                            timestamp: Date.now(),\n                            isError\n                        });\n                        console.log('📤 Queued telegram notification for when online (queue size:', telegramNotificationQueue.length, ')');\n                    } else {\n                        console.warn('⚠️ Telegram notification queue is full, dropping oldest notification');\n                        telegramNotificationQueue.shift(); // Remove oldest\n                        telegramNotificationQueue.push({\n                            message,\n                            timestamp: Date.now(),\n                            isError\n                        });\n                    }\n                    return;\n                }\n                // Add timeout to prevent hanging requests\n                const controller = new AbortController();\n                const timeoutId = setTimeout({\n                    \"TradingProvider.useCallback[sendTelegramNotification].timeoutId\": ()=>controller.abort()\n                }[\"TradingProvider.useCallback[sendTelegramNotification].timeoutId\"], 10000); // 10 second timeout\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Retry logic for non-error notifications\n                    if (!isError && retryCount < 2) {\n                        console.log(\"\\uD83D\\uDD04 Retrying telegram notification (attempt \".concat(retryCount + 1, \"/3)...\"));\n                        setTimeout({\n                            \"TradingProvider.useCallback[sendTelegramNotification]\": ()=>{\n                                sendTelegramNotification(message, isError, retryCount + 1);\n                            }\n                        }[\"TradingProvider.useCallback[sendTelegramNotification]\"], 5000 * (retryCount + 1)); // Exponential backoff: 5s, 10s\n                        return;\n                    }\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError && retryCount >= 2) {\n                        console.error('❌ All telegram notification retry attempts failed');\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Retry logic for network errors (but not for error notifications to avoid loops)\n                if (!isError && retryCount < 2) {\n                    console.log(\"\\uD83D\\uDD04 Retrying telegram notification after network error (attempt \".concat(retryCount + 1, \"/3)...\"));\n                    setTimeout({\n                        \"TradingProvider.useCallback[sendTelegramNotification]\": ()=>{\n                            sendTelegramNotification(message, isError, retryCount + 1);\n                        }\n                    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], 10000 * (retryCount + 1)); // Longer backoff for network errors: 10s, 20s\n                    return;\n                }\n                if (!isError && retryCount >= 2) {\n                    console.error('❌ All telegram notification retry attempts failed due to network errors');\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Initialize fetchMarketPrice after telegram functions\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                // For StablecoinSwap mode, use real-time prices for accurate market calculation\n                if (state.config.tradingMode === \"StablecoinSwap\") {\n                    // Try to get real-time prices first\n                    try {\n                        const crypto1USDPrice = await getRealTimeUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = await getRealTimeUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Real-time):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6), \"\\n            - Calculation: \").concat(crypto1USDPrice, \" \\xf7 \").concat(crypto2USDPrice, \" = \").concat(price.toFixed(6)));\n                    } catch (error) {\n                        // Fallback to cached/static prices\n                        const crypto1USDPrice = getUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = getUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Fallback):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    }\n                } else {\n                    // For SimpleSpot mode, use the existing API-based approach\n                    price = await getMarketPriceFromAPI(state.config);\n                }\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                sendAPIErrorNotification('Price API', \"Failed to fetch market price: \".concat(error)).catch(console.error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Real-time price update effect - fetches live prices from Binance API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price update interval (every 2 seconds as requested)\n            const realTimePriceInterval = setInterval({\n                \"TradingProvider.useEffect.realTimePriceInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            // Fetch fresh price from API instead of using mock fluctuation\n                            await fetchMarketPrice();\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update real-time price:', error);\n                            // Send error notification for price fetch failures\n                            await sendTelegramErrorNotification('Price Update Error', \"Failed to fetch real-time price: \".concat(error));\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.realTimePriceInterval\"], 2000); // Update every 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(realTimePriceInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        sendTelegramErrorNotification\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Safety effect: Auto-stop bot if target prices are cleared while running\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'Running' && (!state.targetPriceRows || state.targetPriceRows.length === 0)) {\n                console.warn('🚨 SAFETY AUTO-STOP: Bot was running but target prices were cleared. Stopping bot automatically.');\n                dispatch({\n                    type: 'SYSTEM_STOP_BOT'\n                });\n                toast({\n                    title: \"Bot Auto-Stopped\",\n                    description: \"Bot was automatically stopped because target prices were cleared. This is a safety feature.\",\n                    variant: \"destructive\",\n                    duration: 5000\n                });\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.targetPriceRows,\n        dispatch,\n        toast\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(config.preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        realizedProfitLossCrypto2: estimatedProfitCrypto2,\n                                        realizedProfitLossCrypto1: estimatedProfitCrypto1\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const stablecoinUSDPrice = getUSDPrice(config.preferredStablecoin || 'USDT');\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: config.preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        // Send telegram notification for network disconnection\n                        sendTelegramErrorNotification('Network Disconnection', 'Internet connection lost. Bot has been stopped and session saved automatically. Trading will resume when connection is restored.').catch(console.error);\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        // Process any queued telegram notifications first\n                        processQueuedTelegramNotifications().catch(console.error);\n                        // Send delayed notification about the previous disconnection now that we're back online\n                        sendTelegramErrorNotification('Network Reconnected', 'Internet connection has been restored. The bot was automatically stopped during the disconnection to prevent trading errors. You can now resume trading safely.').catch(console.error);\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 2094,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"P49OsgzI8BFbPvHbW9UcU21HiOg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});