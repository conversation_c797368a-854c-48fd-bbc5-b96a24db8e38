"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Define crypto lists locally as fallback\nconst LOCAL_ALLOWED_CRYPTO1 = [\n    \"BTC\",\n    \"ETH\",\n    \"BNB\",\n    \"SOL\",\n    \"LINK\",\n    \"AVAX\",\n    \"DOT\",\n    \"UNI\",\n    \"NEAR\",\n    \"AAVE\",\n    \"ATOM\",\n    \"VET\",\n    \"RENDER\",\n    \"POL\",\n    \"ALGO\",\n    \"ARB\",\n    \"FET\",\n    \"PAXG\",\n    \"GALA\",\n    \"CRV\",\n    \"COMP\",\n    \"ENJ\"\n];\nconst LOCAL_ALLOWED_CRYPTO2 = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from LOCAL_ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = LOCAL_ALLOWED_CRYPTO1.filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from LOCAL_ALLOWED_CRYPTO2\n            const allowedCrypto2 = LOCAL_ALLOWED_CRYPTO2;\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from LOCAL_ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = LOCAL_ALLOWED_CRYPTO1.filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from LOCAL_ALLOWED_CRYPTO2\n            const allowedCrypto2 = LOCAL_ALLOWED_CRYPTO2;\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1,\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (ALLOWED_CRYPTO1 || LOCAL_ALLOWED_CRYPTO1).filter((c)=>c !== config.crypto1) : ALLOWED_CRYPTO2 || LOCAL_ALLOWED_CRYPTO2,\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"Cx8udRVa9LCP2mV20ZF0LuBIfNk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});