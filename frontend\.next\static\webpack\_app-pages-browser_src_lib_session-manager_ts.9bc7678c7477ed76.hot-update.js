"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_lib_session-manager_ts",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    isSessionUsedByOtherWindows(sessionId) {\n        if (false) {}\n        // Check all window-specific current session keys in localStorage\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        // Check if any other window (not this one) is using this session\n        for (const key of currentSessionKeys){\n            if (key !== this.getWindowSpecificKey(CURRENT_SESSION_KEY)) {\n                const otherWindowSessionId = localStorage.getItem(key);\n                if (otherWindowSessionId === sessionId) {\n                    console.log(\"\\uD83D\\uDD0D Session \".concat(sessionId, \" is being used by another window (\").concat(key, \")\"));\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    getActiveWindowsForSession(sessionId) {\n        if (false) {}\n        const activeWindows = [];\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        for (const key of currentSessionKeys){\n            const windowSessionId = localStorage.getItem(key);\n            if (windowSessionId === sessionId) {\n                // Extract window ID from key\n                const windowId = key.replace(CURRENT_SESSION_KEY + '_', '');\n                activeWindows.push(windowId);\n            }\n        }\n        return activeWindows;\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    const sessionData = session;\n                    return sessionData.isActive && sessionData.lastModified && Date.now() - sessionData.lastModified < 30 * 60 * 1000; // Active within last 30 minutes\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>{\n                        const latestSession = latest[1];\n                        const currentSession = current[1];\n                        return currentSession.lastModified > latestSession.lastModified ? current : latest;\n                    });\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // If this session is active, ensure it has a start time for runtime calculation\n                const session = this.sessions.get(currentSessionId);\n                if (session && session.isActive && !this.sessionStartTimes.has(currentSessionId)) {\n                    // Restore start time based on when the session was last modified\n                    const estimatedStartTime = Date.now() - (session.runtime || 0);\n                    this.sessionStartTimes.set(currentSessionId, estimatedStartTime);\n                    console.log(\"⏰ Restored session start time for active session: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Validate config before creating session\n        if (!config.crypto1 || !config.crypto2) {\n            console.error('🚨 Cannot create session with empty crypto configuration:', config);\n            throw new Error('Invalid configuration: crypto1 and crypto2 must be set');\n        }\n        console.log('✅ Creating session with config:', config);\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config: {\n                        ...config\n                    },\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                console.log('✅ Session created with config:', newSession.config);\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config: {\n                ...config\n            },\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        console.log('✅ Session created in localStorage with config:', newSession.config);\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            // Safety check for undefined or invalid session ID\n            if (!sessionId || sessionId === 'undefined' || sessionId.length === 0) {\n                console.warn('⚠️ Cannot save session with invalid ID:', sessionId);\n                return false;\n            }\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // For now, focus on localStorage persistence to avoid backend API issues\n            // Backend integration can be enabled later when backend is properly configured\n            console.log('💾 Saving session to localStorage (backend disabled for stability):', sessionId);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        const session = this.sessions.get(sessionId) || null;\n        if (session) {\n            console.log('📂 Loading session config:', session.config);\n        } else {\n            console.warn('⚠️ Session not found:', sessionId);\n        }\n        return session;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // But don't deactivate other sessions - multiple sessions can be active in different tabs\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Only mark session as inactive if no other windows are using it\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                // Check if any other windows are using this session\n                const isUsedByOtherWindows = this.isSessionUsedByOtherWindows(this.currentSessionId);\n                if (!isUsedByOtherWindows) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                } else {\n                    console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" remains active (used by other windows)\"));\n                }\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});