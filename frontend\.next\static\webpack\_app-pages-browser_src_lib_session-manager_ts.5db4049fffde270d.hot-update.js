"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_lib_session-manager_ts",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    isSessionUsedByOtherWindows(sessionId) {\n        if (false) {}\n        // Check all window-specific current session keys in localStorage\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        // Check if any other window (not this one) is using this session\n        for (const key of currentSessionKeys){\n            if (key !== this.getWindowSpecificKey(CURRENT_SESSION_KEY)) {\n                const otherWindowSessionId = localStorage.getItem(key);\n                if (otherWindowSessionId === sessionId) {\n                    console.log(\"\\uD83D\\uDD0D Session \".concat(sessionId, \" is being used by another window (\").concat(key, \")\"));\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    getActiveWindowsForSession(sessionId) {\n        if (false) {}\n        const activeWindows = [];\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        for (const key of currentSessionKeys){\n            const windowSessionId = localStorage.getItem(key);\n            if (windowSessionId === sessionId) {\n                // Extract window ID from key\n                const windowId = key.replace(CURRENT_SESSION_KEY + '_', '');\n                activeWindows.push(windowId);\n            }\n        }\n        return activeWindows;\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    const sessionData = session;\n                    return sessionData.isActive && sessionData.lastModified && Date.now() - sessionData.lastModified < 30 * 60 * 1000; // Active within last 30 minutes\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>{\n                        const latestSession = latest[1];\n                        const currentSession = current[1];\n                        return currentSession.lastModified > latestSession.lastModified ? current : latest;\n                    });\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // If this session is active, ensure it has a start time for runtime calculation\n                const session = this.sessions.get(currentSessionId);\n                if (session && session.isActive && !this.sessionStartTimes.has(currentSessionId)) {\n                    // Restore start time based on when the session was last modified\n                    const estimatedStartTime = Date.now() - (session.runtime || 0);\n                    this.sessionStartTimes.set(currentSessionId, estimatedStartTime);\n                    console.log(\"⏰ Restored session start time for active session: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Validate config before creating session\n        if (!config.crypto1 || !config.crypto2) {\n            console.error('🚨 Cannot create session with empty crypto configuration:', config);\n            throw new Error('Invalid configuration: crypto1 and crypto2 must be set');\n        }\n        console.log('✅ Creating session with config:', config);\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config: {\n                        ...config\n                    },\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                console.log('✅ Session created with config:', newSession.config);\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            // Safety check for undefined or invalid session ID\n            if (!sessionId || sessionId === 'undefined' || sessionId.length === 0) {\n                console.warn('⚠️ Cannot save session with invalid ID:', sessionId);\n                return false;\n            }\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // For now, focus on localStorage persistence to avoid backend API issues\n            // Backend integration can be enabled later when backend is properly configured\n            console.log('💾 Saving session to localStorage (backend disabled for stability):', sessionId);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // But don't deactivate other sessions - multiple sessions can be active in different tabs\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Only mark session as inactive if no other windows are using it\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                // Check if any other windows are using this session\n                const isUsedByOtherWindows = this.isSessionUsedByOtherWindows(this.currentSessionId);\n                if (!isUsedByOtherWindows) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                } else {\n                    console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" remains active (used by other windows)\"));\n                }\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});