"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    isSessionUsedByOtherWindows(sessionId) {\n        if (false) {}\n        // Check all window-specific current session keys in localStorage\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        // Check if any other window (not this one) is using this session\n        for (const key of currentSessionKeys){\n            if (key !== this.getWindowSpecificKey(CURRENT_SESSION_KEY)) {\n                const otherWindowSessionId = localStorage.getItem(key);\n                if (otherWindowSessionId === sessionId) {\n                    console.log(\"\\uD83D\\uDD0D Session \".concat(sessionId, \" is being used by another window (\").concat(key, \")\"));\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    getActiveWindowsForSession(sessionId) {\n        if (false) {}\n        const activeWindows = [];\n        const allKeys = Object.keys(localStorage);\n        const currentSessionKeys = allKeys.filter((key)=>key.startsWith(CURRENT_SESSION_KEY + '_'));\n        for (const key of currentSessionKeys){\n            const windowSessionId = localStorage.getItem(key);\n            if (windowSessionId === sessionId) {\n                // Extract window ID from key\n                const windowId = key.replace(CURRENT_SESSION_KEY + '_', '');\n                activeWindows.push(windowId);\n            }\n        }\n        return activeWindows;\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    const sessionData = session;\n                    return sessionData.isActive && sessionData.lastModified && Date.now() - sessionData.lastModified < 30 * 60 * 1000; // Active within last 30 minutes\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>{\n                        const latestSession = latest[1];\n                        const currentSession = current[1];\n                        return currentSession.lastModified > latestSession.lastModified ? current : latest;\n                    });\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // If this session is active, ensure it has a start time for runtime calculation\n                const session = this.sessions.get(currentSessionId);\n                if (session && session.isActive && !this.sessionStartTimes.has(currentSessionId)) {\n                    // Restore start time based on when the session was last modified\n                    const estimatedStartTime = Date.now() - (session.runtime || 0);\n                    this.sessionStartTimes.set(currentSessionId, estimatedStartTime);\n                    console.log(\"⏰ Restored session start time for active session: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                }\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Validate config before creating session\n        if (!config.crypto1 || !config.crypto2) {\n            console.error('🚨 Cannot create session with empty crypto configuration:', config);\n            throw new Error('Invalid configuration: crypto1 and crypto2 must be set');\n        }\n        console.log('✅ Creating session with config:', config);\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            // Safety check for undefined or invalid session ID\n            if (!sessionId || sessionId === 'undefined' || sessionId.length === 0) {\n                console.warn('⚠️ Cannot save session with invalid ID:', sessionId);\n                return false;\n            }\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // For now, focus on localStorage persistence to avoid backend API issues\n            // Backend integration can be enabled later when backend is properly configured\n            console.log('💾 Saving session to localStorage (backend disabled for stability):', sessionId);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // But don't deactivate other sessions - multiple sessions can be active in different tabs\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Only mark session as inactive if no other windows are using it\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                // Check if any other windows are using this session\n                const isUsedByOtherWindows = this.isSessionUsedByOtherWindows(this.currentSessionId);\n                if (!isUsedByOtherWindows) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                } else {\n                    console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" remains active (used by other windows)\"));\n                }\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2Vzc2lvbi1tYW5hZ2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUNvQztBQUNHO0FBRXZDLE1BQU1HLHVCQUF1QjtBQUM3QixNQUFNQyxzQkFBc0I7QUFFNUIsMERBQTBEO0FBQzFELE1BQU1DLG1CQUFtQjtJQUN2QixPQUFPLFVBQXdCQyxPQUFkQyxLQUFLQyxHQUFHLElBQUcsS0FBMkMsT0FBeENGLEtBQUtHLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHO0FBQ3RFO0FBRUEsdUNBQXVDO0FBQ3ZDLE1BQU1DLGNBQWM7SUFDbEIsSUFBSSxLQUE2QixFQUFFLEVBQWdCO0lBRW5ELGlGQUFpRjtJQUNqRixJQUFJQyxXQUFXQyxlQUFlQyxPQUFPLENBQUM7SUFDdEMsSUFBSSxDQUFDRixVQUFVO1FBQ2JBLFdBQVdSO1FBQ1hTLGVBQWVFLE9BQU8sQ0FBQyxtQkFBbUJIO1FBQzFDSSxRQUFRQyxHQUFHLENBQUMsdUNBQXNDLE9BQVRMO0lBQzNDLE9BQU87UUFDTEksUUFBUUMsR0FBRyxDQUFDLDBDQUF5QyxPQUFUTDtJQUM5QztJQUNBLE9BQU9BO0FBQ1Q7QUFFTyxNQUFNTTtJQStCWCxPQUFjQyxjQUE4QjtRQUMxQyxJQUFJLENBQUNELGVBQWVFLFFBQVEsRUFBRTtZQUM1QkYsZUFBZUUsUUFBUSxHQUFHLElBQUlGO1FBQ2hDO1FBQ0EsT0FBT0EsZUFBZUUsUUFBUTtJQUNoQztJQUVRQyxvQkFBb0JDLE1BQXFCLEVBQVU7UUFDekQsTUFBTUMsVUFBVUQsT0FBT0MsT0FBTyxJQUFJO1FBQ2xDLE1BQU1DLFVBQVVGLE9BQU9FLE9BQU8sSUFBSTtRQUNsQyxNQUFNQyxjQUFjSCxPQUFPRyxXQUFXLElBQUk7UUFFMUMsTUFBTUMsV0FBVyxHQUFjRixPQUFYRCxTQUFRLEtBQWNFLE9BQVhELFNBQVEsS0FBZSxPQUFaQztRQUUxQyxzREFBc0Q7UUFDdEQsTUFBTUUsbUJBQW1CQyxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDQyxRQUFRLENBQUNDLE1BQU07UUFDeEQsTUFBTUMsa0JBQWtCTCxpQkFBaUJNLE1BQU0sQ0FBQ0MsQ0FBQUEsVUFDOUNBLFFBQVFDLElBQUksQ0FBQ0MsVUFBVSxDQUFDVjtRQUcxQixJQUFJTSxnQkFBZ0JLLE1BQU0sS0FBSyxHQUFHO1lBQ2hDLE9BQU9YO1FBQ1Q7UUFFQSxrQ0FBa0M7UUFDbEMsSUFBSVksWUFBWTtRQUNoQk4sZ0JBQWdCTyxPQUFPLENBQUNMLENBQUFBO1lBQ3RCLE1BQU1NLFFBQVFOLFFBQVFDLElBQUksQ0FBQ0ssS0FBSyxDQUFDLElBQUlDLE9BQU8sSUFBb0QsT0FBaERmLFNBQVNnQixPQUFPLENBQUMsdUJBQXVCLFNBQVE7WUFDaEcsSUFBSUYsT0FBTztnQkFDVCxNQUFNRyxTQUFTQyxTQUFTSixLQUFLLENBQUMsRUFBRSxFQUFFO2dCQUNsQyxJQUFJRyxTQUFTTCxXQUFXO29CQUN0QkEsWUFBWUs7Z0JBQ2Q7WUFDRixPQUFPLElBQUlULFFBQVFDLElBQUksS0FBS1QsVUFBVTtnQkFDcEMscURBQXFEO2dCQUNyRFksWUFBWWpDLEtBQUt3QyxHQUFHLENBQUNQLFdBQVc7WUFDbEM7UUFDRjtRQUVBLE9BQU8sR0FBdUJBLE9BQXBCWixVQUFTLGFBQXlCLE9BQWRZLFlBQVk7SUFDNUM7SUFFQSxNQUFjUSw4QkFBNkM7UUFDekQsMkNBQTJDO1FBQzNDLElBQUksSUFBSSxDQUFDQyxjQUFjLEVBQUU7WUFDdkIvQixRQUFRQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSSxDQUFDOEIsY0FBYyxHQUFHO1FBRXRCLElBQUk7WUFDRixpQ0FBaUM7WUFDakMsTUFBTUMsWUFBWUMsYUFBYW5DLE9BQU8sQ0FBQztZQUN2QyxJQUFJLENBQUNrQyxXQUFXO2dCQUNkaEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRztnQkFDbEIsSUFBSSxDQUFDQyx1QkFBdUI7Z0JBQzVCO1lBQ0Y7WUFFQSw4REFBOEQ7WUFDOUQsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGlDQUFpQztnQkFDNURDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtZQUNGO1lBRUEsSUFBSUgsU0FBU0ksRUFBRSxFQUFFO2dCQUNmeEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRztnQkFDbEIsNERBQTREO2dCQUM1RCxNQUFNLElBQUksQ0FBQ08sdUJBQXVCO1lBQ3BDLE9BQU87Z0JBQ0wsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2QzQyxRQUFRQyxHQUFHLENBQUMsc0RBQXNEMEM7WUFDbEUsSUFBSSxDQUFDVCxVQUFVLEdBQUc7WUFDbEIsSUFBSSxDQUFDQyx1QkFBdUI7UUFDOUIsU0FBVTtZQUNSLElBQUksQ0FBQ0osY0FBYyxHQUFHO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFjYSx5QkFBMkM7UUFDdkQsSUFBSTtZQUNGLE1BQU1aLFlBQVlDLGFBQWFuQyxPQUFPLENBQUM7WUFDdkMsSUFBSSxDQUFDa0MsV0FBVztnQkFDZCxPQUFPO1lBQ1Q7WUFFQSxNQUFNSSxXQUFXLE1BQU1DLE1BQU0saUNBQWlDO2dCQUM1REMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQixVQUFvQixPQUFWUDtnQkFDN0I7WUFDRjtZQUVBLE9BQU9JLFNBQVNJLEVBQUU7UUFDcEIsRUFBRSxPQUFPRyxPQUFPO1lBQ2QzQyxRQUFRQyxHQUFHLENBQUMsdUNBQXVDMEM7WUFDbkQsT0FBTztRQUNUO0lBQ0Y7SUFFUUUscUJBQXFCQyxPQUFlLEVBQVU7UUFDcEQsT0FBTyxHQUFjLE9BQVhBLFNBQVEsS0FBaUIsT0FBZCxJQUFJLENBQUNsRCxRQUFRO0lBQ3BDO0lBRVFtRCw0QkFBNEJDLFNBQWlCLEVBQVc7UUFDOUQsSUFBSSxLQUE2QixFQUFFLEVBQWE7UUFFaEQsaUVBQWlFO1FBQ2pFLE1BQU1DLFVBQVVDLE9BQU9DLElBQUksQ0FBQ2xCO1FBQzVCLE1BQU1tQixxQkFBcUJILFFBQVFoQyxNQUFNLENBQUNvQyxDQUFBQSxNQUFPQSxJQUFJakMsVUFBVSxDQUFDakMsc0JBQXNCO1FBRXRGLGlFQUFpRTtRQUNqRSxLQUFLLE1BQU1rRSxPQUFPRCxtQkFBb0I7WUFDcEMsSUFBSUMsUUFBUSxJQUFJLENBQUNSLG9CQUFvQixDQUFDMUQsc0JBQXNCO2dCQUMxRCxNQUFNbUUsdUJBQXVCckIsYUFBYW5DLE9BQU8sQ0FBQ3VEO2dCQUNsRCxJQUFJQyx5QkFBeUJOLFdBQVc7b0JBQ3RDaEQsUUFBUUMsR0FBRyxDQUFDLHdCQUE0RG9ELE9BQTlDTCxXQUFVLHNDQUF3QyxPQUFKSyxLQUFJO29CQUM1RSxPQUFPO2dCQUNUO1lBQ0Y7UUFDRjtRQUVBLE9BQU87SUFDVDtJQUVPRSwyQkFBMkJQLFNBQWlCLEVBQVk7UUFDN0QsSUFBSSxLQUE2QixFQUFFLEVBQVU7UUFFN0MsTUFBTVEsZ0JBQTBCLEVBQUU7UUFDbEMsTUFBTVAsVUFBVUMsT0FBT0MsSUFBSSxDQUFDbEI7UUFDNUIsTUFBTW1CLHFCQUFxQkgsUUFBUWhDLE1BQU0sQ0FBQ29DLENBQUFBLE1BQU9BLElBQUlqQyxVQUFVLENBQUNqQyxzQkFBc0I7UUFFdEYsS0FBSyxNQUFNa0UsT0FBT0QsbUJBQW9CO1lBQ3BDLE1BQU1LLGtCQUFrQnhCLGFBQWFuQyxPQUFPLENBQUN1RDtZQUM3QyxJQUFJSSxvQkFBb0JULFdBQVc7Z0JBQ2pDLDZCQUE2QjtnQkFDN0IsTUFBTXBELFdBQVd5RCxJQUFJM0IsT0FBTyxDQUFDdkMsc0JBQXNCLEtBQUs7Z0JBQ3hEcUUsY0FBY0UsSUFBSSxDQUFDOUQ7WUFDckI7UUFDRjtRQUVBLE9BQU80RDtJQUNUO0lBRVFHLHVCQUE2QjtRQUNuQyxJQUFJLEtBQTZCLEVBQUUsRUFBTztRQUUxQyxnREFBZ0Q7UUFDaERDLE9BQU9DLGdCQUFnQixDQUFDLFdBQVcsQ0FBQ0M7WUFDbEMsSUFBSUEsTUFBTVQsR0FBRyxLQUFLbkUsd0JBQXdCNEUsTUFBTUMsUUFBUSxFQUFFO2dCQUN4RCxJQUFJO29CQUNGLHFEQUFxRDtvQkFDckQsTUFBTUMsaUJBQWlCQyxLQUFLQyxLQUFLLENBQUNKLE1BQU1DLFFBQVE7b0JBQ2hELElBQUksQ0FBQ2pELFFBQVEsR0FBRyxJQUFJcUQsSUFBSWpCLE9BQU9rQixPQUFPLENBQUNKO29CQUN2Q2hFLFFBQVFDLEdBQUcsQ0FBQyxxREFBOEQsT0FBbkIsSUFBSSxDQUFDYSxRQUFRLENBQUN1RCxJQUFJLEVBQUM7Z0JBQzVFLEVBQUUsT0FBTzFCLE9BQU87b0JBQ2QzQyxRQUFRMkMsS0FBSyxDQUFDLCtDQUErQ0E7Z0JBQy9EO1lBQ0Y7UUFDRjtJQUNGO0lBRVFSLDBCQUFnQztRQUN0QyxJQUFJO1lBQ0YsSUFBSSxLQUE2QixFQUFFLEVBQU87WUFFMUMsb0VBQW9FO1lBQ3BFLE1BQU1tQyxlQUFlckMsYUFBYW5DLE9BQU8sQ0FBQ1o7WUFFMUMsMkVBQTJFO1lBQzNFLElBQUlxRixtQkFBa0M7WUFFdEMsc0RBQXNEO1lBQ3RELE1BQU1DLG9CQUFvQixJQUFJLENBQUMzQixvQkFBb0IsQ0FBQzFEO1lBQ3BEb0YsbUJBQW1CdEMsYUFBYW5DLE9BQU8sQ0FBQzBFO1lBRXhDLDhEQUE4RDtZQUM5RCxJQUFJLENBQUNELGtCQUFrQjtnQkFDckJBLG1CQUFtQjFFLGVBQWVDLE9BQU8sQ0FBQ1g7WUFDNUM7WUFFQSxnRUFBZ0U7WUFDaEUsSUFBSSxDQUFDb0Ysb0JBQW9CRCxjQUFjO2dCQUNyQyxNQUFNTixpQkFBaUJDLEtBQUtDLEtBQUssQ0FBQ0k7Z0JBQ2xDLE1BQU1HLGlCQUFpQnZCLE9BQU9rQixPQUFPLENBQUNKLGdCQUFnQi9DLE1BQU0sQ0FBQzt3QkFBQyxDQUFDeUQsR0FBR3hELFFBQVE7b0JBQ3hFLE1BQU15RCxjQUFjekQ7b0JBQ3BCLE9BQU95RCxZQUFZQyxRQUFRLElBQUlELFlBQVlFLFlBQVksSUFBSSxLQUFNdEYsR0FBRyxLQUFLb0YsWUFBWUUsWUFBWSxHQUFJLEtBQUssS0FBSyxNQUFNLGdDQUFnQztnQkFDdko7Z0JBQ0EsSUFBSUosZUFBZXBELE1BQU0sR0FBRyxHQUFHO29CQUM3Qix1Q0FBdUM7b0JBQ3ZDLE1BQU15RCxvQkFBb0JMLGVBQWVNLE1BQU0sQ0FBQyxDQUFDQyxRQUFRQzt3QkFDdkQsTUFBTUMsZ0JBQWdCRixNQUFNLENBQUMsRUFBRTt3QkFDL0IsTUFBTUcsaUJBQWlCRixPQUFPLENBQUMsRUFBRTt3QkFDakMsT0FBT0UsZUFBZU4sWUFBWSxHQUFHSyxjQUFjTCxZQUFZLEdBQUdJLFVBQVVEO29CQUM5RTtvQkFDQVQsbUJBQW1CTyxpQkFBaUIsQ0FBQyxFQUFFO29CQUN2QzlFLFFBQVFDLEdBQUcsQ0FBQyxxREFBNEQsT0FBakJzRTtnQkFDekQ7WUFDRjtZQUVBLElBQUlELGNBQWM7Z0JBQ2hCLE1BQU1OLGlCQUFpQkMsS0FBS0MsS0FBSyxDQUFDSTtnQkFDbEMsSUFBSSxDQUFDeEQsUUFBUSxHQUFHLElBQUlxRCxJQUFJakIsT0FBT2tCLE9BQU8sQ0FBQ0o7WUFDekM7WUFFQSxJQUFJLENBQUNPLGdCQUFnQixHQUFHQTtZQUV4Qix1RUFBdUU7WUFDdkUsSUFBSUEsa0JBQWtCO2dCQUNwQjFFLGVBQWVFLE9BQU8sQ0FBQ1oscUJBQXFCb0Y7Z0JBRTVDLGdGQUFnRjtnQkFDaEYsTUFBTXJELFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUNzRSxHQUFHLENBQUNiO2dCQUNsQyxJQUFJckQsV0FBV0EsUUFBUTBELFFBQVEsSUFBSSxDQUFDLElBQUksQ0FBQ1MsaUJBQWlCLENBQUNDLEdBQUcsQ0FBQ2YsbUJBQW1CO29CQUNoRixpRUFBaUU7b0JBQ2pFLE1BQU1nQixxQkFBcUJqRyxLQUFLQyxHQUFHLEtBQU0yQixDQUFBQSxRQUFRc0UsT0FBTyxJQUFJO29CQUM1RCxJQUFJLENBQUNILGlCQUFpQixDQUFDSSxHQUFHLENBQUNsQixrQkFBa0JnQjtvQkFDN0N2RixRQUFRQyxHQUFHLENBQUMscURBQXNFLE9BQWpCc0U7Z0JBQ25FO1lBQ0Y7WUFFQXZFLFFBQVFDLEdBQUcsQ0FBQyx1QkFBOEQsT0FBakQsSUFBSSxDQUFDYSxRQUFRLENBQUN1RCxJQUFJLEVBQUMsZ0NBQXlERSxPQUEzQixJQUFJLENBQUMzRSxRQUFRLEVBQUMsZUFBOEIsT0FBakIyRTtRQUN2RyxFQUFFLE9BQU81QixPQUFPO1lBQ2QzQyxRQUFRMkMsS0FBSyxDQUFDLHlDQUF5Q0E7UUFDekQ7SUFDRjtJQUVBLE1BQWNGLDBCQUF5QztRQUNyRCxJQUFJO1lBQ0YseURBQXlEO1lBQ3pELE1BQU1pRCxRQUFRLEtBQTZCLEdBQUd6RCxhQUFhbkMsT0FBTyxDQUFDLG9CQUFvQixDQUFJO1lBQzNGLElBQUksQ0FBQzRGLFNBQVNBLE1BQU1yRSxNQUFNLEdBQUcsSUFBSTtnQkFDL0JyQixRQUFRQyxHQUFHLENBQUM7Z0JBQ1osSUFBSSxDQUFDaUMsVUFBVSxHQUFHLE9BQU8sdUJBQXVCO2dCQUNoRCxJQUFJLENBQUNDLHVCQUF1QjtnQkFDNUI7WUFDRjtZQUVBbkMsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTSxFQUFFaEIsVUFBVSxFQUFFLEdBQUcsTUFBTSw2SUFBbUI7WUFDaEQsTUFBTW1ELFdBQVcsTUFBTW5ELFdBQVcwRyxjQUFjLENBQUM7WUFDakQzRixRQUFRQyxHQUFHLENBQUMsZ0NBQWdDbUM7WUFFNUMsSUFBSUEsWUFBWUEsU0FBU3RCLFFBQVEsRUFBRTtnQkFDakMsa0RBQWtEO2dCQUNsRCxJQUFJLENBQUNBLFFBQVEsQ0FBQzhFLEtBQUs7Z0JBQ25CeEQsU0FBU3RCLFFBQVEsQ0FBQ1MsT0FBTyxDQUFDLENBQUNMO29CQUN6QixNQUFNeUQsY0FBOEI7d0JBQ2xDa0IsSUFBSTNFLFFBQVE0RSxZQUFZO3dCQUN4QjNFLE1BQU1ELFFBQVFDLElBQUk7d0JBQ2xCYixRQUFRMkQsS0FBS0MsS0FBSyxDQUFDaEQsUUFBUTZFLGVBQWUsSUFBSTt3QkFDOUNDLFdBQVcsSUFBSTFHLEtBQUs0QixRQUFRK0UsVUFBVSxFQUFFQyxPQUFPO3dCQUMvQ3JCLGNBQWMsSUFBSXZGLEtBQUs0QixRQUFRaUYsYUFBYSxFQUFFRCxPQUFPO3dCQUNyRHRCLFVBQVUxRCxRQUFRa0YsU0FBUzt3QkFDM0JaLFNBQVN0RSxRQUFRc0UsT0FBTyxJQUFJO3dCQUM1QmEsaUJBQWlCbkYsUUFBUW9GLGlCQUFpQixHQUFHckMsS0FBS0MsS0FBSyxDQUFDaEQsUUFBUW9GLGlCQUFpQixJQUFJLEVBQUU7d0JBQ3ZGQyxjQUFjckYsUUFBUXNGLGFBQWEsR0FBR3ZDLEtBQUtDLEtBQUssQ0FBQ2hELFFBQVFzRixhQUFhLElBQUksRUFBRTt3QkFDNUVDLG9CQUFvQnZGLFFBQVF3RixvQkFBb0IsSUFBSTt3QkFDcERDLGdCQUFnQnpGLFFBQVEwRixlQUFlLElBQUk7d0JBQzNDQyxnQkFBZ0IzRixRQUFRNEYsZUFBZSxJQUFJO3dCQUMzQ0MsbUJBQW1CN0YsUUFBUThGLGtCQUFrQixJQUFJO3dCQUNqREMsZUFBZS9GLFFBQVFnRyxjQUFjLEdBQUdqRCxLQUFLQyxLQUFLLENBQUNoRCxRQUFRZ0csY0FBYyxJQUFJQztvQkFDL0U7b0JBQ0EsSUFBSSxDQUFDckcsUUFBUSxDQUFDMkUsR0FBRyxDQUFDdkUsUUFBUTRFLFlBQVksRUFBRW5CO2dCQUMxQztnQkFFQSxzQkFBc0I7Z0JBQ3RCLE1BQU15QyxnQkFBZ0JoRixTQUFTdEIsUUFBUSxDQUFDdUcsSUFBSSxDQUFDLENBQUNDLElBQVdBLEVBQUVsQixTQUFTO2dCQUNwRSxJQUFJZ0IsZUFBZTtvQkFDakIsSUFBSSxDQUFDN0MsZ0JBQWdCLEdBQUc2QyxjQUFjdEIsWUFBWTtnQkFDcEQ7Z0JBRUE5RixRQUFRQyxHQUFHLENBQUMsdUJBQWdDLE9BQW5CLElBQUksQ0FBQ2EsUUFBUSxDQUFDdUQsSUFBSSxFQUFDO1lBQzlDO1FBQ0YsRUFBRSxPQUFPMUIsT0FBTztZQUNkLE1BQU00RSxlQUFlNUUsaUJBQWlCRCxRQUFRQyxNQUFNNkUsT0FBTyxHQUFHQyxPQUFPOUU7WUFFckUsMENBQTBDO1lBQzFDLElBQUk0RSxhQUFhRyxRQUFRLENBQUMscUJBQXFCSCxhQUFhRyxRQUFRLENBQUMsVUFBVUgsYUFBYUcsUUFBUSxDQUFDLFFBQVE7Z0JBQzNHMUgsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRyxPQUFPLG1EQUFtRDtZQUM5RSxPQUFPLElBQUlxRixhQUFhRyxRQUFRLENBQUMsNkJBQTZCO2dCQUM1RDFILFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0wsaURBQWlEO2dCQUNqREQsUUFBUTJILElBQUksQ0FBQyxxRUFBcUVKO1lBQ3BGO1lBRUEsMkJBQTJCO1lBQzNCLElBQUksQ0FBQ3BGLHVCQUF1QjtRQUM5QjtJQUNGO0lBRVF5Rix3QkFBOEI7UUFDcEMsSUFBSTtZQUNGLElBQUksS0FBNkIsRUFBRSxFQUFPO1lBRTFDLGtFQUFrRTtZQUNsRSxNQUFNQyxpQkFBaUIzRSxPQUFPNEUsV0FBVyxDQUFDLElBQUksQ0FBQ2hILFFBQVE7WUFDdkRtQixhQUFhbEMsT0FBTyxDQUFDYixzQkFBc0IrRSxLQUFLOEQsU0FBUyxDQUFDRjtZQUUxRCxrREFBa0Q7WUFDbEQsTUFBTUcsb0JBQW9CLElBQUksQ0FBQ25GLG9CQUFvQixDQUFDMUQ7WUFDcEQsSUFBSSxJQUFJLENBQUNvRixnQkFBZ0IsRUFBRTtnQkFDekJ0QyxhQUFhbEMsT0FBTyxDQUFDaUksbUJBQW1CLElBQUksQ0FBQ3pELGdCQUFnQjtZQUMvRDtRQUNGLEVBQUUsT0FBTzVCLE9BQU87WUFDZDNDLFFBQVEyQyxLQUFLLENBQUMsdUNBQXVDQTtRQUN2RDtJQUNGO0lBRUEsTUFBYXNGLDZCQUE2QjNILE1BQXFCLEVBQUU0SCxVQUFtQixFQUFFQyxlQUEwRSxFQUFtQjtRQUNqTCxNQUFNQyxjQUFjRixjQUFjLElBQUksQ0FBQzdILG1CQUFtQixDQUFDQztRQUMzRCxPQUFPLElBQUksQ0FBQytILGdCQUFnQixDQUFDRCxhQUFhOUgsUUFBUTZIO0lBQ3BEO0lBRUEsTUFBYUUsaUJBQWlCbEgsSUFBWSxFQUFFYixNQUFxQixFQUFFNkgsZUFBMEUsRUFBbUI7UUFDOUosMENBQTBDO1FBQzFDLElBQUksQ0FBQzdILE9BQU9DLE9BQU8sSUFBSSxDQUFDRCxPQUFPRSxPQUFPLEVBQUU7WUFDdENSLFFBQVEyQyxLQUFLLENBQUMsNkRBQTZEckM7WUFDM0UsTUFBTSxJQUFJb0MsTUFBTTtRQUNsQjtRQUVBMUMsUUFBUUMsR0FBRyxDQUFDLG1DQUFtQ0s7UUFFL0MsMENBQTBDO1FBQzFDLE1BQU1nSSxXQUFXSCxtQkFBbUI7WUFBRTVILFNBQVM7WUFBSUMsU0FBUztZQUFRK0gsWUFBWTtRQUFFO1FBRWxGLElBQUksSUFBSSxDQUFDckcsVUFBVSxFQUFFO1lBQ25CLElBQUk7Z0JBQ0YsTUFBTXlDLGNBQWM7b0JBQ2xCeEQ7b0JBQ0FiLFFBQVFBO29CQUNSK0YsaUJBQWlCLEVBQUU7b0JBQ25CSSxvQkFBb0I7b0JBQ3BCRSxnQkFBZ0IyQixTQUFTL0gsT0FBTztvQkFDaENzRyxnQkFBZ0J5QixTQUFTOUgsT0FBTztvQkFDaEN1RyxtQkFBbUJ1QixTQUFTQyxVQUFVO2dCQUN4QztnQkFFQSxNQUFNbkcsV0FBVyxNQUFNbkQsZ0RBQVVBLENBQUN1SixhQUFhLENBQUM3RDtnQkFFaEQscUNBQXFDO2dCQUNyQyxNQUFNOEQsYUFBNkI7b0JBQ2pDNUMsSUFBSXpELFNBQVNsQixPQUFPLENBQUM0RSxZQUFZO29CQUNqQzNFLE1BQU1pQixTQUFTbEIsT0FBTyxDQUFDQyxJQUFJO29CQUMzQmI7b0JBQ0EwRixXQUFXLElBQUkxRyxLQUFLOEMsU0FBU2xCLE9BQU8sQ0FBQytFLFVBQVUsRUFBRUMsT0FBTztvQkFDeERyQixjQUFjLElBQUl2RixLQUFLOEMsU0FBU2xCLE9BQU8sQ0FBQ2lGLGFBQWEsRUFBRUQsT0FBTztvQkFDOUR0QixVQUFVeEMsU0FBU2xCLE9BQU8sQ0FBQ2tGLFNBQVM7b0JBQ3BDWixTQUFTcEQsU0FBU2xCLE9BQU8sQ0FBQ3NFLE9BQU8sSUFBSTtvQkFDckNhLGlCQUFpQixFQUFFO29CQUNuQkUsY0FBYyxFQUFFO29CQUNoQkUsb0JBQW9CO29CQUNwQkUsZ0JBQWdCMkIsU0FBUy9ILE9BQU87b0JBQ2hDc0csZ0JBQWdCeUIsU0FBUzlILE9BQU87b0JBQ2hDdUcsbUJBQW1CdUIsU0FBU0MsVUFBVTtnQkFDeEM7Z0JBRUEsSUFBSSxDQUFDekgsUUFBUSxDQUFDMkUsR0FBRyxDQUFDckQsU0FBU2xCLE9BQU8sQ0FBQzRFLFlBQVksRUFBRTJDO2dCQUNqRHpJLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNtQyxTQUFTbEIsT0FBTyxDQUFDNEUsWUFBWTtnQkFDMUUsT0FBTzFELFNBQVNsQixPQUFPLENBQUM0RSxZQUFZO1lBQ3RDLEVBQUUsT0FBT25ELE9BQU87Z0JBQ2QzQyxRQUFRMkMsS0FBSyxDQUFDLHdFQUF3RUE7Z0JBQ3RGLElBQUksQ0FBQ1QsVUFBVSxHQUFHO1lBQ3BCO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTWMsWUFBWWhFLGdEQUFNQTtRQUN4QixNQUFNTyxNQUFNRCxLQUFLQyxHQUFHO1FBRXBCLE1BQU1rSixhQUE2QjtZQUNqQzVDLElBQUk3QztZQUNKN0I7WUFDQWI7WUFDQStGLGlCQUFpQixFQUFFO1lBQ25CRSxjQUFjLEVBQUU7WUFDaEJFLG9CQUFvQjtZQUNwQkUsZ0JBQWdCMkIsU0FBUy9ILE9BQU87WUFDaENzRyxnQkFBZ0J5QixTQUFTOUgsT0FBTztZQUNoQ3VHLG1CQUFtQnVCLFNBQVNDLFVBQVU7WUFDdEN2QyxXQUFXekc7WUFDWHNGLGNBQWN0RjtZQUNkcUYsVUFBVTtZQUNWWSxTQUFTO1FBQ1g7UUFFQSxJQUFJLENBQUMxRSxRQUFRLENBQUMyRSxHQUFHLENBQUN6QyxXQUFXeUY7UUFDN0IsSUFBSSxDQUFDYixxQkFBcUI7UUFDMUIsT0FBTzVFO0lBQ1Q7SUFFQSxNQUFhMEYsWUFDWDFGLFNBQWlCLEVBQ2pCMUMsTUFBcUIsRUFDckIrRixlQUFpQyxFQUNqQ0UsWUFBaUMsRUFDakNFLGtCQUEwQixFQUMxQkUsY0FBc0IsRUFDdEJFLGNBQXNCLEVBQ3RCRSxpQkFBeUIsRUFHUDtZQUZsQm5DLFdBQUFBLGlFQUFvQixPQUNwQitELDhEQUF5Qiw2Q0FBNkM7O1FBRXRFLElBQUk7WUFDRixtREFBbUQ7WUFDbkQsSUFBSSxDQUFDM0YsYUFBYUEsY0FBYyxlQUFlQSxVQUFVM0IsTUFBTSxLQUFLLEdBQUc7Z0JBQ3JFckIsUUFBUTJILElBQUksQ0FBQywyQ0FBMkMzRTtnQkFDeEQsT0FBTztZQUNUO1lBRUEsTUFBTTlCLFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUNzRSxHQUFHLENBQUNwQztZQUNsQyxJQUFJLENBQUM5QixTQUFTO2dCQUNabEIsUUFBUTJDLEtBQUssQ0FBQyxzQkFBc0JLO2dCQUNwQyxPQUFPO1lBQ1Q7WUFFQSwwRUFBMEU7WUFDMUUsSUFBSTRGO1lBRUosSUFBSUQsb0JBQW9CeEIsV0FBVztnQkFDakMsZ0RBQWdEO2dCQUNoRHlCLGlCQUFpQkQ7Z0JBQ2pCM0ksUUFBUUMsR0FBRyxDQUFDLHdDQUE4RCtDLE9BQWhDNEYsZ0JBQWUsbUJBQTJCLE9BQVY1RjtZQUM1RSxPQUFPO2dCQUNMLGlEQUFpRDtnQkFDakQ0RixpQkFBaUIxSCxRQUFRc0UsT0FBTyxJQUFJO2dCQUNwQyxNQUFNcUQsWUFBWSxJQUFJLENBQUN4RCxpQkFBaUIsQ0FBQ0QsR0FBRyxDQUFDcEM7Z0JBQzdDLElBQUk2RixhQUFhakUsVUFBVTtvQkFDekIscUNBQXFDO29CQUNyQ2dFLGlCQUFpQixDQUFDMUgsUUFBUXNFLE9BQU8sSUFBSSxLQUFNbEcsQ0FBQUEsS0FBS0MsR0FBRyxLQUFLc0osU0FBUTtvQkFDaEUscUNBQXFDO29CQUNyQyxJQUFJLENBQUN4RCxpQkFBaUIsQ0FBQ0ksR0FBRyxDQUFDekMsV0FBVzFELEtBQUtDLEdBQUc7Z0JBQ2hELE9BQU8sSUFBSSxDQUFDcUYsWUFBWWlFLFdBQVc7b0JBQ2pDLG9DQUFvQztvQkFDcENELGlCQUFpQixDQUFDMUgsUUFBUXNFLE9BQU8sSUFBSSxLQUFNbEcsQ0FBQUEsS0FBS0MsR0FBRyxLQUFLc0osU0FBUTtvQkFDaEUsSUFBSSxDQUFDeEQsaUJBQWlCLENBQUN5RCxNQUFNLENBQUM5RjtnQkFDaEMsT0FBTyxJQUFJNEIsWUFBWSxDQUFDaUUsV0FBVztvQkFDakMsMENBQTBDO29CQUMxQyxJQUFJLENBQUN4RCxpQkFBaUIsQ0FBQ0ksR0FBRyxDQUFDekMsV0FBVzFELEtBQUtDLEdBQUc7Z0JBQ2hEO1lBQ0Y7WUFFQSxNQUFNd0osaUJBQWlDO2dCQUNyQyxHQUFHN0gsT0FBTztnQkFDVlo7Z0JBQ0ErRixpQkFBaUI7dUJBQUlBO2lCQUFnQjtnQkFDckNFLGNBQWM7dUJBQUlBO2lCQUFhO2dCQUMvQkU7Z0JBQ0FFO2dCQUNBRTtnQkFDQUU7Z0JBQ0FuQztnQkFDQUMsY0FBY3ZGLEtBQUtDLEdBQUc7Z0JBQ3RCaUcsU0FBU29EO1lBQ1g7WUFFQSxJQUFJLENBQUM5SCxRQUFRLENBQUMyRSxHQUFHLENBQUN6QyxXQUFXK0Y7WUFFN0IseUVBQXlFO1lBQ3pFLCtFQUErRTtZQUMvRS9JLFFBQVFDLEdBQUcsQ0FBQyx1RUFBdUUrQztZQUVuRixJQUFJLENBQUM0RSxxQkFBcUI7WUFDMUIsT0FBTztRQUNULEVBQUUsT0FBT2pGLE9BQU87WUFDZDNDLFFBQVEyQyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxPQUFPO1FBQ1Q7SUFDRjtJQUVPcUcsWUFBWWhHLFNBQWlCLEVBQXlCO1FBQzNELE9BQU8sSUFBSSxDQUFDbEMsUUFBUSxDQUFDc0UsR0FBRyxDQUFDcEMsY0FBYztJQUN6QztJQUVBLE1BQWFpRyxjQUFjakcsU0FBaUIsRUFBb0I7UUFDOUQsbUVBQW1FO1FBQ25FLElBQUksSUFBSSxDQUFDZCxVQUFVLElBQUksYUFBa0IsYUFBYTtZQUNwRCxNQUFNd0QsUUFBUXpELGFBQWFuQyxPQUFPLENBQUM7WUFDbkMsSUFBSTRGLE9BQU87Z0JBQ1QsSUFBSTtvQkFDRixNQUFNekcsZ0RBQVVBLENBQUNnSyxhQUFhLENBQUNqRztvQkFDL0JoRCxRQUFRQyxHQUFHLENBQUMsbUNBQW1DK0M7Z0JBQ2pELEVBQUUsT0FBT0wsT0FBTztvQkFDZCxNQUFNNEUsZUFBZTVFLGlCQUFpQkQsUUFBUUMsTUFBTTZFLE9BQU8sR0FBR0MsT0FBTzlFO29CQUNyRSxJQUFJNEUsYUFBYUcsUUFBUSxDQUFDLFVBQVVILGFBQWFHLFFBQVEsQ0FBQyxVQUFVSCxhQUFhRyxRQUFRLENBQUMsbUJBQW1CO3dCQUMzRzFILFFBQVFDLEdBQUcsQ0FBQztvQkFDZCxPQUFPO3dCQUNMRCxRQUFRMkMsS0FBSyxDQUFDLDRDQUE0Q0E7b0JBQzVEO2dCQUNBLDJDQUEyQztnQkFDN0M7WUFDRixPQUFPO2dCQUNMM0MsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLE1BQU1pSixVQUFVLElBQUksQ0FBQ3BJLFFBQVEsQ0FBQ2dJLE1BQU0sQ0FBQzlGO1FBQ3JDLElBQUlrRyxTQUFTO1lBQ1gsSUFBSSxJQUFJLENBQUMzRSxnQkFBZ0IsS0FBS3ZCLFdBQVc7Z0JBQ3ZDLElBQUksQ0FBQ3VCLGdCQUFnQixHQUFHO2dCQUN4QixNQUFNeUQsb0JBQW9CLElBQUksQ0FBQ25GLG9CQUFvQixDQUFDMUQ7Z0JBQ3BEOEMsYUFBYWtILFVBQVUsQ0FBQ25CO1lBQzFCO1lBQ0EsSUFBSSxDQUFDSixxQkFBcUI7UUFDNUI7UUFDQSxPQUFPc0I7SUFDVDtJQUVPdkQsaUJBQW9DO1FBQ3pDLE9BQU8vRSxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDQyxRQUFRLENBQUNDLE1BQU0sSUFBSXFJLEdBQUcsQ0FBQ2xJLENBQUFBLFVBQVk7Z0JBQ3hEMkUsSUFBSTNFLFFBQVEyRSxFQUFFO2dCQUNkMUUsTUFBTUQsUUFBUUMsSUFBSTtnQkFDbEJrSSxNQUFNLEdBQTZCbkksT0FBMUJBLFFBQVFaLE1BQU0sQ0FBQ0MsT0FBTyxFQUFDLEtBQTBCLE9BQXZCVyxRQUFRWixNQUFNLENBQUNFLE9BQU87Z0JBQ3pEd0YsV0FBVzlFLFFBQVE4RSxTQUFTO2dCQUM1Qm5CLGNBQWMzRCxRQUFRMkQsWUFBWTtnQkFDbENELFVBQVUxRCxRQUFRMEQsUUFBUTtnQkFDMUJZLFNBQVMsSUFBSSxDQUFDOEQsaUJBQWlCLENBQUNwSSxRQUFRMkUsRUFBRTtnQkFDMUMwRCxhQUFhckksUUFBUXFGLFlBQVksQ0FBQ2xGLE1BQU07Z0JBQ3hDbUksaUJBQWlCdEksUUFBUXFGLFlBQVksQ0FDbEN0RixNQUFNLENBQUN3SSxDQUFBQSxRQUFTQSxNQUFNQyxTQUFTLEtBQUssVUFBVUQsTUFBTUUseUJBQXlCLEtBQUt4QyxXQUNsRnBDLE1BQU0sQ0FBQyxDQUFDNkUsS0FBS0gsUUFBVUcsTUFBT0gsQ0FBQUEsTUFBTUUseUJBQXlCLElBQUksSUFBSTtZQUMxRTtJQUNGO0lBRU9FLGtCQUFrQjdHLFNBQWlCLEVBQVE7UUFDaEQsSUFBSSxJQUFJLENBQUNsQyxRQUFRLENBQUN3RSxHQUFHLENBQUN0QyxZQUFZO1lBQ2hDLElBQUksQ0FBQ3VCLGdCQUFnQixHQUFHdkI7WUFFeEIsNkZBQTZGO1lBQzdGLE1BQU1nRixvQkFBb0IsSUFBSSxDQUFDbkYsb0JBQW9CLENBQUMxRDtZQUNwRDhDLGFBQWFsQyxPQUFPLENBQUNpSSxtQkFBbUJoRjtZQUN4Q25ELGVBQWVFLE9BQU8sQ0FBQ1oscUJBQXFCNkQ7WUFFNUMsa0VBQWtFO1lBQ2xFLDBGQUEwRjtZQUMxRixNQUFNOUIsVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQ3NFLEdBQUcsQ0FBQ3BDO1lBQ2xDLElBQUk5QixTQUFTO2dCQUNYQSxRQUFRMEQsUUFBUSxHQUFHO2dCQUNuQjFELFFBQVEyRCxZQUFZLEdBQUd2RixLQUFLQyxHQUFHO2dCQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRSxHQUFHLENBQUN6QyxXQUFXOUI7Z0JBQzdCLElBQUksQ0FBQzBHLHFCQUFxQjtnQkFDMUI1SCxRQUFRQyxHQUFHLENBQUMsYUFBc0QsT0FBekMrQyxXQUFVLGlDQUE2QyxPQUFkLElBQUksQ0FBQ3BELFFBQVE7WUFDakY7UUFDRjtJQUNGO0lBRU9rSyxzQkFBcUM7UUFDMUMsT0FBTyxJQUFJLENBQUN2RixnQkFBZ0I7SUFDOUI7SUFFT3dGLHNCQUE0QjtRQUNqQyxpRUFBaUU7UUFDakUsSUFBSSxJQUFJLENBQUN4RixnQkFBZ0IsRUFBRTtZQUN6QixNQUFNckQsVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQ3NFLEdBQUcsQ0FBQyxJQUFJLENBQUNiLGdCQUFnQjtZQUN2RCxJQUFJckQsV0FBV0EsUUFBUTBELFFBQVEsRUFBRTtnQkFDL0Isb0RBQW9EO2dCQUNwRCxNQUFNb0YsdUJBQXVCLElBQUksQ0FBQ2pILDJCQUEyQixDQUFDLElBQUksQ0FBQ3dCLGdCQUFnQjtnQkFFbkYsSUFBSSxDQUFDeUYsc0JBQXNCO29CQUN6QjlJLFFBQVEwRCxRQUFRLEdBQUc7b0JBQ25CMUQsUUFBUTJELFlBQVksR0FBR3ZGLEtBQUtDLEdBQUc7b0JBQy9CLElBQUksQ0FBQ3VCLFFBQVEsQ0FBQzJFLEdBQUcsQ0FBQyxJQUFJLENBQUNsQixnQkFBZ0IsRUFBRXJEO29CQUN6QyxJQUFJLENBQUMwRyxxQkFBcUI7b0JBQzFCNUgsUUFBUUMsR0FBRyxDQUFDLGNBQW9DLE9BQXRCLElBQUksQ0FBQ3NFLGdCQUFnQixFQUFDO2dCQUNsRCxPQUFPO29CQUNMdkUsUUFBUUMsR0FBRyxDQUFDLHdCQUFvQyxPQUF0QixJQUFJLENBQUNzRSxnQkFBZ0IsRUFBQztnQkFDbEQ7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDQSxnQkFBZ0IsR0FBRztRQUN4QixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTXlELG9CQUFvQixJQUFJLENBQUNuRixvQkFBb0IsQ0FBQzFEO1lBQ3BEOEMsYUFBYWtILFVBQVUsQ0FBQ25CO1lBQ3hCbkksZUFBZXNKLFVBQVUsQ0FBQ2hLO1FBQzVCO1FBQ0FhLFFBQVFDLEdBQUcsQ0FBQyxvREFBd0QsT0FBZCxJQUFJLENBQUNMLFFBQVE7SUFDckU7SUFFT3FLLG9CQUFvQmpILFNBQWlCLEVBQVE7UUFDbEQsSUFBSSxDQUFDcUMsaUJBQWlCLENBQUNJLEdBQUcsQ0FBQ3pDLFdBQVcxRCxLQUFLQyxHQUFHO0lBQ2hEO0lBRU8ySyxtQkFBbUJsSCxTQUFpQixFQUFRO1FBQ2pELE1BQU02RixZQUFZLElBQUksQ0FBQ3hELGlCQUFpQixDQUFDRCxHQUFHLENBQUNwQztRQUM3QyxJQUFJNkYsV0FBVztZQUNiLE1BQU0zSCxVQUFVLElBQUksQ0FBQ0osUUFBUSxDQUFDc0UsR0FBRyxDQUFDcEM7WUFDbEMsSUFBSTlCLFNBQVM7Z0JBQ1gsTUFBTWlKLG9CQUFvQjdLLEtBQUtDLEdBQUcsS0FBS3NKO2dCQUN2QzNILFFBQVFzRSxPQUFPLEdBQUcsQ0FBQ3RFLFFBQVFzRSxPQUFPLElBQUksS0FBSzJFO2dCQUMzQ2pKLFFBQVEyRCxZQUFZLEdBQUd2RixLQUFLQyxHQUFHO2dCQUMvQixnR0FBZ0c7Z0JBQ2hHLElBQUksQ0FBQ3VCLFFBQVEsQ0FBQzJFLEdBQUcsQ0FBQ3pDLFdBQVc5QjtnQkFDN0IsSUFBSSxDQUFDMEcscUJBQXFCO1lBQzVCO1lBQ0EsSUFBSSxDQUFDdkMsaUJBQWlCLENBQUN5RCxNQUFNLENBQUM5RjtRQUNoQztJQUNGO0lBRU9vSCxrQkFBa0JwSCxTQUFpQixFQUFRO1FBQ2hELE1BQU05QixVQUFVLElBQUksQ0FBQ0osUUFBUSxDQUFDc0UsR0FBRyxDQUFDcEM7UUFDbEMsSUFBSTlCLFdBQVdBLFFBQVEwRCxRQUFRLEVBQUU7WUFDL0IxRCxRQUFRMEQsUUFBUSxHQUFHO1lBQ25CMUQsUUFBUTJELFlBQVksR0FBR3ZGLEtBQUtDLEdBQUc7WUFDL0IsSUFBSSxDQUFDdUIsUUFBUSxDQUFDMkUsR0FBRyxDQUFDekMsV0FBVzlCO1lBQzdCLElBQUksQ0FBQzBHLHFCQUFxQjtZQUMxQjVILFFBQVFDLEdBQUcsQ0FBQyxjQUF3QixPQUFWK0MsV0FBVTtRQUN0QztJQUNGO0lBRU9zRyxrQkFBa0J0RyxTQUFpQixFQUFVO1FBQ2xELE1BQU05QixVQUFVLElBQUksQ0FBQ0osUUFBUSxDQUFDc0UsR0FBRyxDQUFDcEM7UUFDbEMsSUFBSSxDQUFDOUIsU0FBUyxPQUFPO1FBRXJCLE1BQU0ySCxZQUFZLElBQUksQ0FBQ3hELGlCQUFpQixDQUFDRCxHQUFHLENBQUNwQztRQUM3QyxJQUFJNkYsV0FBVztZQUNiLHNFQUFzRTtZQUN0RSxPQUFPLENBQUMzSCxRQUFRc0UsT0FBTyxJQUFJLEtBQU1sRyxDQUFBQSxLQUFLQyxHQUFHLEtBQUtzSixTQUFRO1FBQ3hEO1FBRUEsZ0RBQWdEO1FBQ2hELE9BQU8zSCxRQUFRc0UsT0FBTyxJQUFJO0lBQzVCO0lBRUEsMERBQTBEO0lBQzFELE1BQWE2RSwyQkFBMEM7UUFDckRySyxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNLElBQUksQ0FBQzZCLDJCQUEyQjtJQUN4QztJQUVBLDhEQUE4RDtJQUN2RHdJLHFCQUEyQjtRQUNoQ3RLLFFBQVFDLEdBQUcsQ0FBQztRQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRztRQUNsQixJQUFJLENBQUNILGNBQWMsR0FBRztJQUN4QjtJQUVBLHdEQUF3RDtJQUNqRHdJLGVBQXFCO1FBQzFCdkssUUFBUUMsR0FBRyxDQUFDO1FBQ1osSUFBSSxDQUFDaUMsVUFBVSxHQUFHO1FBQ2xCLElBQUksQ0FBQ3BCLFFBQVEsQ0FBQzhFLEtBQUs7UUFDbkIsSUFBSSxDQUFDckIsZ0JBQWdCLEdBQUc7UUFDeEIsSUFBSSxDQUFDcEMsdUJBQXVCO0lBQzlCO0lBRU9xSSxvQkFBb0J4SCxTQUFpQixFQUFpQjtRQUMzRCxNQUFNOUIsVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQ3NFLEdBQUcsQ0FBQ3BDO1FBQ2xDLElBQUksQ0FBQzlCLFNBQVMsT0FBTztRQUVyQixPQUFPK0MsS0FBSzhELFNBQVMsQ0FBQzdHLFNBQVMsTUFBTTtJQUN2QztJQUVPdUosc0JBQXNCQyxRQUFnQixFQUFpQjtRQUM1RCxJQUFJO1lBQ0YsTUFBTS9GLGNBQWNWLEtBQUtDLEtBQUssQ0FBQ3dHO1lBQy9CLE1BQU0xSCxZQUFZaEUsZ0RBQU1BO1lBRXhCLE1BQU0yTCxrQkFBa0M7Z0JBQ3RDLEdBQUdoRyxXQUFXO2dCQUNka0IsSUFBSTdDO2dCQUNKNEIsVUFBVTtnQkFDVkMsY0FBY3ZGLEtBQUtDLEdBQUc7WUFDeEI7WUFFQSxJQUFJLENBQUN1QixRQUFRLENBQUMyRSxHQUFHLENBQUN6QyxXQUFXMkg7WUFDN0IsSUFBSSxDQUFDL0MscUJBQXFCO1lBQzFCLE9BQU81RTtRQUNULEVBQUUsT0FBT0wsT0FBTztZQUNkM0MsUUFBUTJDLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE9BQU87UUFDVDtJQUNGO0lBRU9pSSxjQUFjNUgsU0FBaUIsRUFBRTZILE9BQWUsRUFBVztRQUNoRSxNQUFNM0osVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQ3NFLEdBQUcsQ0FBQ3BDO1FBQ2xDLElBQUksQ0FBQzlCLFNBQVMsT0FBTztRQUVyQkEsUUFBUUMsSUFBSSxHQUFHMEo7UUFDZjNKLFFBQVEyRCxZQUFZLEdBQUd2RixLQUFLQyxHQUFHO1FBQy9CLElBQUksQ0FBQ3VCLFFBQVEsQ0FBQzJFLEdBQUcsQ0FBQ3pDLFdBQVc5QjtRQUM3QixJQUFJLENBQUMwRyxxQkFBcUI7UUFDMUIsT0FBTztJQUNUO0lBRUEsTUFBYWtELDJCQUEyQjlILFNBQWlCLEVBQUVpRSxhQUEwQixFQUFvQjtRQUN2RyxNQUFNL0YsVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQ3NFLEdBQUcsQ0FBQ3BDO1FBQ2xDLElBQUksQ0FBQzlCLFNBQVMsT0FBTztRQUVyQkEsUUFBUStGLGFBQWEsR0FBR0E7UUFDeEIvRixRQUFRMkQsWUFBWSxHQUFHdkYsS0FBS0MsR0FBRztRQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRSxHQUFHLENBQUN6QyxXQUFXOUI7UUFFN0IsK0JBQStCO1FBQy9CLElBQUksSUFBSSxDQUFDZ0IsVUFBVSxFQUFFO1lBQ25CLElBQUk7Z0JBQ0YsTUFBTXlDLGNBQWM7b0JBQ2xCeEQsTUFBTUQsUUFBUUMsSUFBSTtvQkFDbEJiLFFBQVFZLFFBQVFaLE1BQU07b0JBQ3RCK0YsaUJBQWlCbkYsUUFBUW1GLGVBQWU7b0JBQ3hDSSxvQkFBb0J2RixRQUFRdUYsa0JBQWtCO29CQUM5Q0UsZ0JBQWdCekYsUUFBUXlGLGNBQWM7b0JBQ3RDRSxnQkFBZ0IzRixRQUFRMkYsY0FBYztvQkFDdENFLG1CQUFtQjdGLFFBQVE2RixpQkFBaUI7b0JBQzVDbkMsVUFBVTFELFFBQVEwRCxRQUFRO29CQUMxQnNDLGdCQUFnQkQ7Z0JBQ2xCO2dCQUVBLE1BQU1oSSxnREFBVUEsQ0FBQzhMLGFBQWEsQ0FBQy9ILFdBQVcyQjtnQkFDMUMzRSxRQUFRQyxHQUFHLENBQUMsOENBQThDK0M7WUFDNUQsRUFBRSxPQUFPTCxPQUFPO2dCQUNkM0MsUUFBUTJDLEtBQUssQ0FBQyx1REFBdURBO1lBQ3JFLDhDQUE4QztZQUNoRDtRQUNGO1FBRUEsSUFBSSxDQUFDaUYscUJBQXFCO1FBQzFCLE9BQU87SUFDVDtJQUVPb0Qsa0JBQWtCaEksU0FBaUIsRUFBdUI7UUFDL0QsTUFBTTlCLFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUNzRSxHQUFHLENBQUNwQztRQUNsQyxPQUFPOUIsVUFBVTtlQUFJQSxRQUFRcUYsWUFBWTtTQUFDLEdBQUcsRUFBRTtJQUNqRDtJQUVPMEUsbUJBQW1CakksU0FBaUIsRUFBaUI7UUFDMUQsTUFBTTlCLFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUNzRSxHQUFHLENBQUNwQztRQUNsQyxJQUFJLENBQUM5QixTQUFTLE9BQU87UUFFckIsTUFBTXFCLFVBQVU7WUFDZDtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUVELE1BQU0ySSxhQUFhO1lBQ2pCM0ksUUFBUTRJLElBQUksQ0FBQztlQUNWakssUUFBUXFGLFlBQVksQ0FBQzZDLEdBQUcsQ0FBQ2dDLENBQUFBO29CQU0xQkEsc0JBQ0FBLGlCQUNBQSxxQkFDQUEsZUFFQUE7dUJBWG1DO29CQUNuQyxJQUFJOUwsS0FBSzhMLE1BQU1DLFNBQVMsRUFBRUMsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3JELElBQUlqTSxLQUFLOEwsTUFBTUMsU0FBUyxFQUFFRyxZQUFZLEdBQUdELEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQkFDdERILE1BQU0vQixJQUFJO29CQUNWK0IsTUFBTUssYUFBYTtvQkFDbkJMLE1BQU0xQixTQUFTO29CQUNmMEIsRUFBQUEsdUJBQUFBLE1BQU1NLGFBQWEsY0FBbkJOLDJDQUFBQSxxQkFBcUJPLE9BQU8sQ0FBQ3pLLFFBQVFaLE1BQU0sQ0FBQ3NMLFNBQVMsTUFBSztvQkFDMURSLEVBQUFBLGtCQUFBQSxNQUFNUyxRQUFRLGNBQWRULHNDQUFBQSxnQkFBZ0JPLE9BQU8sQ0FBQ3pLLFFBQVFaLE1BQU0sQ0FBQ3NMLFNBQVMsTUFBSztvQkFDckRSLEVBQUFBLHNCQUFBQSxNQUFNVSxZQUFZLGNBQWxCViwwQ0FBQUEsb0JBQW9CTyxPQUFPLENBQUN6SyxRQUFRWixNQUFNLENBQUNzTCxTQUFTLE1BQUs7b0JBQ3pEUixFQUFBQSxnQkFBQUEsTUFBTVcsTUFBTSxjQUFaWCxvQ0FBQUEsY0FBY08sT0FBTyxDQUFDekssUUFBUVosTUFBTSxDQUFDc0wsU0FBUyxNQUFLO29CQUNuRFIsTUFBTUssYUFBYTtvQkFDbkJMLEVBQUFBLGdCQUFBQSxNQUFNWSxNQUFNLGNBQVpaLG9DQUFBQSxjQUFjTyxPQUFPLENBQUN6SyxRQUFRWixNQUFNLENBQUNzTCxTQUFTLE1BQUs7b0JBQ25EUixNQUFNYSxhQUFhO2lCQUNwQixDQUFDZCxJQUFJLENBQUM7O1NBQ1IsQ0FBQ0EsSUFBSSxDQUFDO1FBRVAsT0FBT0Q7SUFDVDtJQUVPZ0IsbUJBQXlCO1FBQzlCLElBQUksQ0FBQ3BMLFFBQVEsQ0FBQzhFLEtBQUs7UUFDbkIsSUFBSSxDQUFDckIsZ0JBQWdCLEdBQUc7UUFDeEIsZ0NBQWdDO1FBQ2hDdEMsYUFBYWtILFVBQVUsQ0FBQ2pLO1FBQ3hCLHdDQUF3QztRQUN4QyxNQUFNOEksb0JBQW9CLElBQUksQ0FBQ25GLG9CQUFvQixDQUFDMUQ7UUFDcEQ4QyxhQUFha0gsVUFBVSxDQUFDbkI7SUFDMUI7SUFFQSwwQkFBMEI7SUFDbkJtRSxlQUNMbkosU0FBaUIsRUFDakJvSixjQVNDLEVBRVc7WUFEWkMsYUFBQUEsZ0RBQTJCLGFBQWE7MEJBQW5CO1FBRXJCLE1BQU1DLFdBQVdDLFlBQVk7WUFDM0IsTUFBTUMsT0FBT0o7WUFDYixJQUFJLENBQUMxRCxXQUFXLENBQ2QxRixXQUNBd0osS0FBS2xNLE1BQU0sRUFDWGtNLEtBQUtuRyxlQUFlLEVBQ3BCbUcsS0FBS2pHLFlBQVksRUFDakJpRyxLQUFLL0Ysa0JBQWtCLEVBQ3ZCK0YsS0FBSzdGLGNBQWMsRUFDbkI2RixLQUFLM0YsY0FBYyxFQUNuQjJGLEtBQUt6RixpQkFBaUIsRUFDdEJ5RixLQUFLNUgsUUFBUTtRQUVqQixHQUFHeUg7UUFFSCxPQUFPLElBQU1JLGNBQWNIO0lBQzdCO0lBdDBCQSxhQUFzQjthQVBkeEwsV0FBd0MsSUFBSXFEO2FBQzVDSSxtQkFBa0M7YUFDbENyQyxhQUFzQixLQUFNLGlEQUFpRDs7YUFDN0VILGlCQUEwQixNQUFPLDJDQUEyQzs7YUFDNUVzRCxvQkFBeUMsSUFBSWxCLE1BQU8sc0NBQXNDOztRQUloRyxJQUFJLENBQUN2RSxRQUFRLEdBQUdEO1FBQ2hCSyxRQUFRQyxHQUFHLENBQUMsdURBQTJELE9BQWQsSUFBSSxDQUFDTCxRQUFRO1FBRXRFLHdEQUF3RDtRQUN4RCxJQUFJLENBQUN5RixpQkFBaUIsQ0FBQ08sS0FBSztRQUU1QiwwREFBMEQ7UUFDMUQsSUFBSSxDQUFDMUQsVUFBVSxHQUFHO1FBRWxCLDRDQUE0QztRQUM1QyxJQUFJLENBQUNDLHVCQUF1QjtRQUM1QixJQUFJLENBQUN3QixvQkFBb0I7UUFFekIsK0ZBQStGO1FBQy9GK0ksV0FBVztZQUNULElBQUksQ0FBQzVLLDJCQUEyQjtRQUNsQyxHQUFHLE9BQU8sMERBQTBEO1FBRXBFOUIsUUFBUUMsR0FBRyxDQUFDLHNEQUEwRCxPQUFkLElBQUksQ0FBQ0wsUUFBUTtJQUN2RTtBQW16QkYiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGxpYlxcc2Vzc2lvbi1tYW5hZ2VyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgVHJhZGluZ1Nlc3Npb24sIFNlc3Npb25NZXRhZGF0YSwgVHJhZGluZ0NvbmZpZywgVGFyZ2V0UHJpY2VSb3csIE9yZGVySGlzdG9yeUVudHJ5LCBBcHBTZXR0aW5ncyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnO1xuaW1wb3J0IHsgc2Vzc2lvbkFwaSB9IGZyb20gJ0AvbGliL2FwaSc7XG5cbmNvbnN0IFNFU1NJT05TX1NUT1JBR0VfS0VZID0gJ3BsdXRvX3RyYWRpbmdfc2Vzc2lvbnMnO1xuY29uc3QgQ1VSUkVOVF9TRVNTSU9OX0tFWSA9ICdwbHV0b19jdXJyZW50X3Nlc3Npb24nO1xuXG4vLyBHZW5lcmF0ZSBhIHVuaXF1ZSB3aW5kb3cgSUQgZm9yIHRoaXMgYnJvd3NlciB0YWIvd2luZG93XG5jb25zdCBnZW5lcmF0ZVdpbmRvd0lkID0gKCk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBgd2luZG93XyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcbn07XG5cbi8vIEdldCBvciBjcmVhdGUgd2luZG93IElEIGZvciB0aGlzIHRhYlxuY29uc3QgZ2V0V2luZG93SWQgPSAoKTogc3RyaW5nID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gJ3NlcnZlcic7XG5cbiAgLy8gVXNlIHNlc3Npb25TdG9yYWdlICh0YWItc3BlY2lmaWMpIGluc3RlYWQgb2YgbG9jYWxTdG9yYWdlIChzaGFyZWQgYWNyb3NzIHRhYnMpXG4gIGxldCB3aW5kb3dJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3BsdXRvX3dpbmRvd19pZCcpO1xuICBpZiAoIXdpbmRvd0lkKSB7XG4gICAgd2luZG93SWQgPSBnZW5lcmF0ZVdpbmRvd0lkKCk7XG4gICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgncGx1dG9fd2luZG93X2lkJywgd2luZG93SWQpO1xuICAgIGNvbnNvbGUubG9nKGDwn4aVIENyZWF0ZWQgbmV3IHdpbmRvdyBJRDogJHt3aW5kb3dJZH1gKTtcbiAgfSBlbHNlIHtcbiAgICBjb25zb2xlLmxvZyhg8J+UhCBVc2luZyBleGlzdGluZyB3aW5kb3cgSUQ6ICR7d2luZG93SWR9YCk7XG4gIH1cbiAgcmV0dXJuIHdpbmRvd0lkO1xufTtcblxuZXhwb3J0IGNsYXNzIFNlc3Npb25NYW5hZ2VyIHtcbiAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IFNlc3Npb25NYW5hZ2VyO1xuICBwcml2YXRlIHNlc3Npb25zOiBNYXA8c3RyaW5nLCBUcmFkaW5nU2Vzc2lvbj4gPSBuZXcgTWFwKCk7XG4gIHByaXZhdGUgY3VycmVudFNlc3Npb25JZDogc3RyaW5nIHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgdXNlQmFja2VuZDogYm9vbGVhbiA9IHRydWU7IC8vIEZsYWcgdG8gZGV0ZXJtaW5lIGlmIHdlIHNob3VsZCB1c2UgYmFja2VuZCBBUElcbiAgcHJpdmF0ZSBpc0luaXRpYWxpemluZzogYm9vbGVhbiA9IGZhbHNlOyAvLyBQcmV2ZW50IG11bHRpcGxlIGluaXRpYWxpemF0aW9uIGF0dGVtcHRzXG4gIHByaXZhdGUgc2Vzc2lvblN0YXJ0VGltZXM6IE1hcDxzdHJpbmcsIG51bWJlcj4gPSBuZXcgTWFwKCk7IC8vIFRyYWNrIHdoZW4gc2Vzc2lvbnMgc3RhcnRlZCBydW5uaW5nXG4gIHByaXZhdGUgd2luZG93SWQ6IHN0cmluZztcblxuICBwcml2YXRlIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMud2luZG93SWQgPSBnZXRXaW5kb3dJZCgpO1xuICAgIGNvbnNvbGUubG9nKGDwn6qfIFNlc3Npb25NYW5hZ2VyIGluaXRpYWxpemVkIGZvciB3aW5kb3c6ICR7dGhpcy53aW5kb3dJZH1gKTtcblxuICAgIC8vIENsZWFyIGFueSBzdGFsZSBzZXNzaW9uIHN0YXJ0IHRpbWVzIG9uIGluaXRpYWxpemF0aW9uXG4gICAgdGhpcy5zZXNzaW9uU3RhcnRUaW1lcy5jbGVhcigpO1xuXG4gICAgLy8gRW5hYmxlIGJhY2tlbmQgYnkgZGVmYXVsdCAtIHdlIHdhbnQgcGVyc2lzdGVudCBzZXNzaW9uc1xuICAgIHRoaXMudXNlQmFja2VuZCA9IHRydWU7XG5cbiAgICAvLyBMb2FkIHNlc3Npb25zIGFuZCBzZXR1cCBjcm9zcy13aW5kb3cgc3luY1xuICAgIHRoaXMubG9hZFNlc3Npb25zRnJvbVN0b3JhZ2UoKTtcbiAgICB0aGlzLnNldHVwU3RvcmFnZUxpc3RlbmVyKCk7XG5cbiAgICAvLyBDaGVjayBiYWNrZW5kIGNvbm5lY3Rpb24gYW5kIGxvYWQgc2Vzc2lvbnMgZnJvbSBiYWNrZW5kICh3aXRoIGRlbGF5IHRvIGVuc3VyZSBhdXRoIGlzIHJlYWR5KVxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgdGhpcy5pbml0aWFsaXplQmFja2VuZENvbm5lY3Rpb24oKTtcbiAgICB9LCAxMDAwKTsgLy8gMSBzZWNvbmQgZGVsYXkgdG8gYWxsb3cgYXV0aCB0byBiZSBwcm9wZXJseSBpbml0aWFsaXplZFxuXG4gICAgY29uc29sZS5sb2coYPCfqp8gU2Vzc2lvbk1hbmFnZXIgaW5pdGlhbGl6ZWQgZm9yIHdpbmRvdyAke3RoaXMud2luZG93SWR9YCk7XG4gIH1cblxuICBwdWJsaWMgc3RhdGljIGdldEluc3RhbmNlKCk6IFNlc3Npb25NYW5hZ2VyIHtcbiAgICBpZiAoIVNlc3Npb25NYW5hZ2VyLmluc3RhbmNlKSB7XG4gICAgICBTZXNzaW9uTWFuYWdlci5pbnN0YW5jZSA9IG5ldyBTZXNzaW9uTWFuYWdlcigpO1xuICAgIH1cbiAgICByZXR1cm4gU2Vzc2lvbk1hbmFnZXIuaW5zdGFuY2U7XG4gIH1cblxuICBwcml2YXRlIGdlbmVyYXRlU2Vzc2lvbk5hbWUoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogc3RyaW5nIHtcbiAgICBjb25zdCBjcnlwdG8xID0gY29uZmlnLmNyeXB0bzEgfHwgJ0NyeXB0bzEnO1xuICAgIGNvbnN0IGNyeXB0bzIgPSBjb25maWcuY3J5cHRvMiB8fCAnQ3J5cHRvMic7XG4gICAgY29uc3QgdHJhZGluZ01vZGUgPSBjb25maWcudHJhZGluZ01vZGUgfHwgJ1NpbXBsZVNwb3QnO1xuXG4gICAgY29uc3QgYmFzZU5hbWUgPSBgJHtjcnlwdG8xfS8ke2NyeXB0bzJ9ICR7dHJhZGluZ01vZGV9YDtcblxuICAgIC8vIENoZWNrIGZvciBleGlzdGluZyBzZXNzaW9ucyB3aXRoIHRoZSBzYW1lIGJhc2UgbmFtZVxuICAgIGNvbnN0IGV4aXN0aW5nU2Vzc2lvbnMgPSBBcnJheS5mcm9tKHRoaXMuc2Vzc2lvbnMudmFsdWVzKCkpO1xuICAgIGNvbnN0IHNpbWlsYXJTZXNzaW9ucyA9IGV4aXN0aW5nU2Vzc2lvbnMuZmlsdGVyKHNlc3Npb24gPT5cbiAgICAgIHNlc3Npb24ubmFtZS5zdGFydHNXaXRoKGJhc2VOYW1lKVxuICAgICk7XG5cbiAgICBpZiAoc2ltaWxhclNlc3Npb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIGJhc2VOYW1lO1xuICAgIH1cblxuICAgIC8vIEZpbmQgdGhlIGhpZ2hlc3Qgc2Vzc2lvbiBudW1iZXJcbiAgICBsZXQgbWF4TnVtYmVyID0gMDtcbiAgICBzaW1pbGFyU2Vzc2lvbnMuZm9yRWFjaChzZXNzaW9uID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoID0gc2Vzc2lvbi5uYW1lLm1hdGNoKG5ldyBSZWdFeHAoYF4ke2Jhc2VOYW1lLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCAnXFxcXCQmJyl9IFNlc3Npb24gKFxcXFxkKykkYCkpO1xuICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgIGNvbnN0IG51bWJlciA9IHBhcnNlSW50KG1hdGNoWzFdLCAxMCk7XG4gICAgICAgIGlmIChudW1iZXIgPiBtYXhOdW1iZXIpIHtcbiAgICAgICAgICBtYXhOdW1iZXIgPSBudW1iZXI7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoc2Vzc2lvbi5uYW1lID09PSBiYXNlTmFtZSkge1xuICAgICAgICAvLyBJZiB0aGVyZSdzIGFuIGV4YWN0IG1hdGNoLCB0cmVhdCBpdCBhcyBcIlNlc3Npb24gMVwiXG4gICAgICAgIG1heE51bWJlciA9IE1hdGgubWF4KG1heE51bWJlciwgMSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gYCR7YmFzZU5hbWV9IFNlc3Npb24gJHttYXhOdW1iZXIgKyAxfWA7XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIGluaXRpYWxpemVCYWNrZW5kQ29ubmVjdGlvbigpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICAvLyBQcmV2ZW50IG11bHRpcGxlIGluaXRpYWxpemF0aW9uIGF0dGVtcHRzXG4gICAgaWYgKHRoaXMuaXNJbml0aWFsaXppbmcpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gQmFja2VuZCBpbml0aWFsaXphdGlvbiBhbHJlYWR5IGluIHByb2dyZXNzLCBza2lwcGluZycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRoaXMuaXNJbml0aWFsaXppbmcgPSB0cnVlO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZFxuICAgICAgY29uc3QgYXV0aFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BsdXRvQXV0aFRva2VuJyk7XG4gICAgICBpZiAoIWF1dGhUb2tlbikge1xuICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIE5vIGF1dGggdG9rZW4gZm91bmQsIHVzaW5nIGxvY2FsU3RvcmFnZSBtb2RlIHVudGlsIGxvZ2luJyk7XG4gICAgICAgIHRoaXMudXNlQmFja2VuZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLmxvYWRTZXNzaW9uc0Zyb21TdG9yYWdlKCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gVGVzdCBiYWNrZW5kIGNvbm5lY3Rpb24gKGhlYWx0aCBlbmRwb2ludCBkb2Vzbid0IG5lZWQgYXV0aClcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9oZWFsdGgvJywge1xuICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIEJhY2tlbmQgY29ubmVjdGlvbiBlc3RhYmxpc2hlZCwgdGVzdGluZyBhdXRoIGFuZCBsb2FkaW5nIHNlc3Npb25zJyk7XG4gICAgICAgIHRoaXMudXNlQmFja2VuZCA9IHRydWU7XG4gICAgICAgIC8vIExvYWQgc2Vzc2lvbnMgZnJvbSBiYWNrZW5kIHdoZW4gY29ubmVjdGlvbiBpcyBlc3RhYmxpc2hlZFxuICAgICAgICBhd2FpdCB0aGlzLmxvYWRTZXNzaW9uc0Zyb21CYWNrZW5kKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0JhY2tlbmQgaGVhbHRoIGNoZWNrIGZhaWxlZCcpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pqg77iPIEJhY2tlbmQgbm90IGF2YWlsYWJsZSwgdXNpbmcgbG9jYWxTdG9yYWdlIG1vZGU6JywgZXJyb3IpO1xuICAgICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7XG4gICAgICB0aGlzLmxvYWRTZXNzaW9uc0Zyb21TdG9yYWdlKCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHRoaXMuaXNJbml0aWFsaXppbmcgPSBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIGNoZWNrQmFja2VuZENvbm5lY3Rpb24oKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGF1dGhUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwbHV0b0F1dGhUb2tlbicpO1xuICAgICAgaWYgKCFhdXRoVG9rZW4pIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwOi8vbG9jYWxob3N0OjUwMDAvaGVhbHRoLycsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7YXV0aFRva2VufWBcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gcmVzcG9uc2Uub2s7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gQmFja2VuZCBjb25uZWN0aW9uIGNoZWNrIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBnZXRXaW5kb3dTcGVjaWZpY0tleShiYXNlS2V5OiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIHJldHVybiBgJHtiYXNlS2V5fV8ke3RoaXMud2luZG93SWR9YDtcbiAgfVxuXG4gIHByaXZhdGUgaXNTZXNzaW9uVXNlZEJ5T3RoZXJXaW5kb3dzKHNlc3Npb25JZDogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gZmFsc2U7XG5cbiAgICAvLyBDaGVjayBhbGwgd2luZG93LXNwZWNpZmljIGN1cnJlbnQgc2Vzc2lvbiBrZXlzIGluIGxvY2FsU3RvcmFnZVxuICAgIGNvbnN0IGFsbEtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpO1xuICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5cyA9IGFsbEtleXMuZmlsdGVyKGtleSA9PiBrZXkuc3RhcnRzV2l0aChDVVJSRU5UX1NFU1NJT05fS0VZICsgJ18nKSk7XG5cbiAgICAvLyBDaGVjayBpZiBhbnkgb3RoZXIgd2luZG93IChub3QgdGhpcyBvbmUpIGlzIHVzaW5nIHRoaXMgc2Vzc2lvblxuICAgIGZvciAoY29uc3Qga2V5IG9mIGN1cnJlbnRTZXNzaW9uS2V5cykge1xuICAgICAgaWYgKGtleSAhPT0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKSkge1xuICAgICAgICBjb25zdCBvdGhlcldpbmRvd1Nlc3Npb25JZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgICAgIGlmIChvdGhlcldpbmRvd1Nlc3Npb25JZCA9PT0gc2Vzc2lvbklkKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCflI0gU2Vzc2lvbiAke3Nlc3Npb25JZH0gaXMgYmVpbmcgdXNlZCBieSBhbm90aGVyIHdpbmRvdyAoJHtrZXl9KWApO1xuICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgcHVibGljIGdldEFjdGl2ZVdpbmRvd3NGb3JTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogc3RyaW5nW10ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIFtdO1xuXG4gICAgY29uc3QgYWN0aXZlV2luZG93czogc3RyaW5nW10gPSBbXTtcbiAgICBjb25zdCBhbGxLZXlzID0gT2JqZWN0LmtleXMobG9jYWxTdG9yYWdlKTtcbiAgICBjb25zdCBjdXJyZW50U2Vzc2lvbktleXMgPSBhbGxLZXlzLmZpbHRlcihrZXkgPT4ga2V5LnN0YXJ0c1dpdGgoQ1VSUkVOVF9TRVNTSU9OX0tFWSArICdfJykpO1xuXG4gICAgZm9yIChjb25zdCBrZXkgb2YgY3VycmVudFNlc3Npb25LZXlzKSB7XG4gICAgICBjb25zdCB3aW5kb3dTZXNzaW9uSWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShrZXkpO1xuICAgICAgaWYgKHdpbmRvd1Nlc3Npb25JZCA9PT0gc2Vzc2lvbklkKSB7XG4gICAgICAgIC8vIEV4dHJhY3Qgd2luZG93IElEIGZyb20ga2V5XG4gICAgICAgIGNvbnN0IHdpbmRvd0lkID0ga2V5LnJlcGxhY2UoQ1VSUkVOVF9TRVNTSU9OX0tFWSArICdfJywgJycpO1xuICAgICAgICBhY3RpdmVXaW5kb3dzLnB1c2god2luZG93SWQpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBhY3RpdmVXaW5kb3dzO1xuICB9XG5cbiAgcHJpdmF0ZSBzZXR1cFN0b3JhZ2VMaXN0ZW5lcigpOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcblxuICAgIC8vIExpc3RlbiBmb3Igc3RvcmFnZSBjaGFuZ2VzIGZyb20gb3RoZXIgd2luZG93c1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzdG9yYWdlJywgKGV2ZW50KSA9PiB7XG4gICAgICBpZiAoZXZlbnQua2V5ID09PSBTRVNTSU9OU19TVE9SQUdFX0tFWSAmJiBldmVudC5uZXdWYWx1ZSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIFJlbG9hZCBzZXNzaW9ucyB3aGVuIHRoZXkgY2hhbmdlIGluIGFub3RoZXIgd2luZG93XG4gICAgICAgICAgY29uc3QgcGFyc2VkU2Vzc2lvbnMgPSBKU09OLnBhcnNlKGV2ZW50Lm5ld1ZhbHVlKTtcbiAgICAgICAgICB0aGlzLnNlc3Npb25zID0gbmV3IE1hcChPYmplY3QuZW50cmllcyhwYXJzZWRTZXNzaW9ucykpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFNlc3Npb25zIHN5bmNlZCBmcm9tIGFub3RoZXIgd2luZG93ICgke3RoaXMuc2Vzc2lvbnMuc2l6ZX0gc2Vzc2lvbnMpYCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN5bmMgc2Vzc2lvbnMgZnJvbSBzdG9yYWdlIGV2ZW50OicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pO1xuICB9XG5cbiAgcHJpdmF0ZSBsb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSgpOiB2b2lkIHtcbiAgICB0cnkge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XG5cbiAgICAgIC8vIExvYWQgc2Vzc2lvbnMgZnJvbSBzaGFyZWQgc3RvcmFnZSAoYWxsIHdpbmRvd3Mgc2VlIHNhbWUgc2Vzc2lvbnMpXG4gICAgICBjb25zdCBzZXNzaW9uc0RhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTRVNTSU9OU19TVE9SQUdFX0tFWSk7XG5cbiAgICAgIC8vIFRyeSB0byBsb2FkIGN1cnJlbnQgc2Vzc2lvbiBmcm9tIG11bHRpcGxlIHNvdXJjZXMgZm9yIGJldHRlciBwZXJzaXN0ZW5jZVxuICAgICAgbGV0IGN1cnJlbnRTZXNzaW9uSWQ6IHN0cmluZyB8IG51bGwgPSBudWxsO1xuXG4gICAgICAvLyAxLiBGaXJzdCB0cnkgd2luZG93LXNwZWNpZmljIHN0b3JhZ2UgKGZvciBuZXcgdGFicylcbiAgICAgIGNvbnN0IHdpbmRvd1NwZWNpZmljS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgIGN1cnJlbnRTZXNzaW9uSWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSh3aW5kb3dTcGVjaWZpY0tleSk7XG5cbiAgICAgIC8vIDIuIElmIG5vdCBmb3VuZCwgdHJ5IHNlc3Npb25TdG9yYWdlIChzdXJ2aXZlcyBwYWdlIHJlZnJlc2gpXG4gICAgICBpZiAoIWN1cnJlbnRTZXNzaW9uSWQpIHtcbiAgICAgICAgY3VycmVudFNlc3Npb25JZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oQ1VSUkVOVF9TRVNTSU9OX0tFWSk7XG4gICAgICB9XG5cbiAgICAgIC8vIDMuIElmIHN0aWxsIG5vdCBmb3VuZCwgbG9vayBmb3IgYW55IGFjdGl2ZSBzZXNzaW9uIChmYWxsYmFjaylcbiAgICAgIGlmICghY3VycmVudFNlc3Npb25JZCAmJiBzZXNzaW9uc0RhdGEpIHtcbiAgICAgICAgY29uc3QgcGFyc2VkU2Vzc2lvbnMgPSBKU09OLnBhcnNlKHNlc3Npb25zRGF0YSk7XG4gICAgICAgIGNvbnN0IGFjdGl2ZVNlc3Npb25zID0gT2JqZWN0LmVudHJpZXMocGFyc2VkU2Vzc2lvbnMpLmZpbHRlcigoW18sIHNlc3Npb25dKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc2Vzc2lvbkRhdGEgPSBzZXNzaW9uIGFzIGFueTtcbiAgICAgICAgICByZXR1cm4gc2Vzc2lvbkRhdGEuaXNBY3RpdmUgJiYgc2Vzc2lvbkRhdGEubGFzdE1vZGlmaWVkICYmIChEYXRlLm5vdygpIC0gc2Vzc2lvbkRhdGEubGFzdE1vZGlmaWVkKSA8IDMwICogNjAgKiAxMDAwOyAvLyBBY3RpdmUgd2l0aGluIGxhc3QgMzAgbWludXRlc1xuICAgICAgICB9KTtcbiAgICAgICAgaWYgKGFjdGl2ZVNlc3Npb25zLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAvLyBHZXQgdGhlIG1vc3QgcmVjZW50bHkgYWN0aXZlIHNlc3Npb25cbiAgICAgICAgICBjb25zdCBtb3N0UmVjZW50U2Vzc2lvbiA9IGFjdGl2ZVNlc3Npb25zLnJlZHVjZSgobGF0ZXN0LCBjdXJyZW50KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBsYXRlc3RTZXNzaW9uID0gbGF0ZXN0WzFdIGFzIGFueTtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uID0gY3VycmVudFsxXSBhcyBhbnk7XG4gICAgICAgICAgICByZXR1cm4gY3VycmVudFNlc3Npb24ubGFzdE1vZGlmaWVkID4gbGF0ZXN0U2Vzc2lvbi5sYXN0TW9kaWZpZWQgPyBjdXJyZW50IDogbGF0ZXN0O1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIGN1cnJlbnRTZXNzaW9uSWQgPSBtb3N0UmVjZW50U2Vzc2lvblswXTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UhCBSZXN0b3JlZCBtb3N0IHJlY2VudCBhY3RpdmUgc2Vzc2lvbjogJHtjdXJyZW50U2Vzc2lvbklkfWApO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChzZXNzaW9uc0RhdGEpIHtcbiAgICAgICAgY29uc3QgcGFyc2VkU2Vzc2lvbnMgPSBKU09OLnBhcnNlKHNlc3Npb25zRGF0YSk7XG4gICAgICAgIHRoaXMuc2Vzc2lvbnMgPSBuZXcgTWFwKE9iamVjdC5lbnRyaWVzKHBhcnNlZFNlc3Npb25zKSk7XG4gICAgICB9XG5cbiAgICAgIHRoaXMuY3VycmVudFNlc3Npb25JZCA9IGN1cnJlbnRTZXNzaW9uSWQ7XG5cbiAgICAgIC8vIFN0b3JlIGN1cnJlbnQgc2Vzc2lvbiBpbiBzZXNzaW9uU3RvcmFnZSBmb3IgcGFnZSByZWZyZXNoIHBlcnNpc3RlbmNlXG4gICAgICBpZiAoY3VycmVudFNlc3Npb25JZCkge1xuICAgICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKENVUlJFTlRfU0VTU0lPTl9LRVksIGN1cnJlbnRTZXNzaW9uSWQpO1xuXG4gICAgICAgIC8vIElmIHRoaXMgc2Vzc2lvbiBpcyBhY3RpdmUsIGVuc3VyZSBpdCBoYXMgYSBzdGFydCB0aW1lIGZvciBydW50aW1lIGNhbGN1bGF0aW9uXG4gICAgICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgICAgaWYgKHNlc3Npb24gJiYgc2Vzc2lvbi5pc0FjdGl2ZSAmJiAhdGhpcy5zZXNzaW9uU3RhcnRUaW1lcy5oYXMoY3VycmVudFNlc3Npb25JZCkpIHtcbiAgICAgICAgICAvLyBSZXN0b3JlIHN0YXJ0IHRpbWUgYmFzZWQgb24gd2hlbiB0aGUgc2Vzc2lvbiB3YXMgbGFzdCBtb2RpZmllZFxuICAgICAgICAgIGNvbnN0IGVzdGltYXRlZFN0YXJ0VGltZSA9IERhdGUubm93KCkgLSAoc2Vzc2lvbi5ydW50aW1lIHx8IDApO1xuICAgICAgICAgIHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuc2V0KGN1cnJlbnRTZXNzaW9uSWQsIGVzdGltYXRlZFN0YXJ0VGltZSk7XG4gICAgICAgICAgY29uc29sZS5sb2coYOKPsCBSZXN0b3JlZCBzZXNzaW9uIHN0YXJ0IHRpbWUgZm9yIGFjdGl2ZSBzZXNzaW9uOiAke2N1cnJlbnRTZXNzaW9uSWR9YCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYPCfk4IgTG9hZGVkICR7dGhpcy5zZXNzaW9ucy5zaXplfSBzaGFyZWQgc2Vzc2lvbnMgZm9yIHdpbmRvdyAke3RoaXMud2luZG93SWR9LCBjdXJyZW50OiAke2N1cnJlbnRTZXNzaW9uSWR9YCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHNlc3Npb25zIGZyb20gc3RvcmFnZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBsb2FkU2Vzc2lvbnNGcm9tQmFja2VuZCgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkIGJlZm9yZSBtYWtpbmcgQVBJIGNhbGxzXG4gICAgICBjb25zdCB0b2tlbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BsdXRvQXV0aFRva2VuJykgOiBudWxsO1xuICAgICAgaWYgKCF0b2tlbiB8fCB0b2tlbi5sZW5ndGggPCAxMCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIEludmFsaWQgb3IgbWlzc2luZyBhdXRoIHRva2VuLCBza2lwcGluZyBiYWNrZW5kIHNlc3Npb24gbG9hZGluZycpO1xuICAgICAgICB0aGlzLnVzZUJhY2tlbmQgPSBmYWxzZTsgLy8gRGlzYWJsZSBiYWNrZW5kIG1vZGVcbiAgICAgICAgdGhpcy5sb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSgpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIExvYWRpbmcgc2Vzc2lvbnMgZnJvbSBiYWNrZW5kLi4uJyk7XG4gICAgICBjb25zdCB7IHNlc3Npb25BcGkgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvYXBpJyk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNlc3Npb25BcGkuZ2V0QWxsU2Vzc2lvbnModHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIEJhY2tlbmQgcmVzcG9uc2UgcmVjZWl2ZWQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uuc2Vzc2lvbnMpIHtcbiAgICAgICAgLy8gQ29udmVydCBiYWNrZW5kIHNlc3Npb25zIHRvIG91ciBpbnRlcm5hbCBmb3JtYXRcbiAgICAgICAgdGhpcy5zZXNzaW9ucy5jbGVhcigpO1xuICAgICAgICByZXNwb25zZS5zZXNzaW9ucy5mb3JFYWNoKChzZXNzaW9uOiBhbnkpID0+IHtcbiAgICAgICAgICBjb25zdCBzZXNzaW9uRGF0YTogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICAgICAgICBpZDogc2Vzc2lvbi5zZXNzaW9uX3V1aWQsXG4gICAgICAgICAgICBuYW1lOiBzZXNzaW9uLm5hbWUsXG4gICAgICAgICAgICBjb25maWc6IEpTT04ucGFyc2Uoc2Vzc2lvbi5jb25maWdfc25hcHNob3QgfHwgJ3t9JyksXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKHNlc3Npb24uY3JlYXRlZF9hdCkuZ2V0VGltZSgpLFxuICAgICAgICAgICAgbGFzdE1vZGlmaWVkOiBuZXcgRGF0ZShzZXNzaW9uLmxhc3RfbW9kaWZpZWQpLmdldFRpbWUoKSxcbiAgICAgICAgICAgIGlzQWN0aXZlOiBzZXNzaW9uLmlzX2FjdGl2ZSxcbiAgICAgICAgICAgIHJ1bnRpbWU6IHNlc3Npb24ucnVudGltZSB8fCAwLFxuICAgICAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBzZXNzaW9uLnRhcmdldF9wcmljZV9yb3dzID8gSlNPTi5wYXJzZShzZXNzaW9uLnRhcmdldF9wcmljZV9yb3dzKSA6IFtdLFxuICAgICAgICAgICAgb3JkZXJIaXN0b3J5OiBzZXNzaW9uLm9yZGVyX2hpc3RvcnkgPyBKU09OLnBhcnNlKHNlc3Npb24ub3JkZXJfaGlzdG9yeSkgOiBbXSxcbiAgICAgICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogc2Vzc2lvbi5jdXJyZW50X21hcmtldF9wcmljZSB8fCAxMDAwMDAsXG4gICAgICAgICAgICBjcnlwdG8xQmFsYW5jZTogc2Vzc2lvbi5jcnlwdG8xX2JhbGFuY2UgfHwgMTAwMDAsXG4gICAgICAgICAgICBjcnlwdG8yQmFsYW5jZTogc2Vzc2lvbi5jcnlwdG8yX2JhbGFuY2UgfHwgMTAwMDAsXG4gICAgICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogc2Vzc2lvbi5zdGFibGVjb2luX2JhbGFuY2UgfHwgMTAwMDAsXG4gICAgICAgICAgICBhbGFybVNldHRpbmdzOiBzZXNzaW9uLmFsYXJtX3NldHRpbmdzID8gSlNPTi5wYXJzZShzZXNzaW9uLmFsYXJtX3NldHRpbmdzKSA6IHVuZGVmaW5lZFxuICAgICAgICAgIH07XG4gICAgICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbi5zZXNzaW9uX3V1aWQsIHNlc3Npb25EYXRhKTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gRmluZCBhY3RpdmUgc2Vzc2lvblxuICAgICAgICBjb25zdCBhY3RpdmVTZXNzaW9uID0gcmVzcG9uc2Uuc2Vzc2lvbnMuZmluZCgoczogYW55KSA9PiBzLmlzX2FjdGl2ZSk7XG4gICAgICAgIGlmIChhY3RpdmVTZXNzaW9uKSB7XG4gICAgICAgICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gYWN0aXZlU2Vzc2lvbi5zZXNzaW9uX3V1aWQ7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZyhg8J+TgiBMb2FkZWQgJHt0aGlzLnNlc3Npb25zLnNpemV9IHNlc3Npb25zIGZyb20gYmFja2VuZGApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFN0cmluZyhlcnJvcik7XG5cbiAgICAgIC8vIEhhbmRsZSBhdXRoZW50aWNhdGlvbiBlcnJvcnMgZ3JhY2VmdWxseVxuICAgICAgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnQXV0aGVudGljYXRpb24nKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJzQwMScpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnNDIyJykpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflJAgQXV0aGVudGljYXRpb24gaXNzdWUgZGV0ZWN0ZWQsIGRpc2FibGluZyBiYWNrZW5kIG1vZGUnKTtcbiAgICAgICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7IC8vIERpc2FibGUgYmFja2VuZCBtb2RlIHRvIHByZXZlbnQgZnV0dXJlIEFQSSBjYWxsc1xuICAgICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ0Nhbm5vdCBjb25uZWN0IHRvIHNlcnZlcicpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4yQIEJhY2tlbmQgc2VydmVyIG5vdCBhdmFpbGFibGUsIHVzaW5nIGxvY2FsIHN0b3JhZ2Ugb25seScpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gT25seSBsb2cgZGV0YWlsZWQgZXJyb3JzIGZvciB1bmV4cGVjdGVkIGlzc3Vlc1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBCYWNrZW5kIHNlc3Npb24gbG9hZGluZyBmYWlsZWQsIGZhbGxpbmcgYmFjayB0byBsb2NhbCBzdG9yYWdlOicsIGVycm9yTWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsU3RvcmFnZVxuICAgICAgdGhpcy5sb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSgpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk6IHZvaWQge1xuICAgIHRyeSB7XG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcblxuICAgICAgLy8gU2F2ZSBzZXNzaW9ucyB0byBzaGFyZWQgc3RvcmFnZSAoYWxsIHdpbmRvd3Mgc2VlIHNhbWUgc2Vzc2lvbnMpXG4gICAgICBjb25zdCBzZXNzaW9uc09iamVjdCA9IE9iamVjdC5mcm9tRW50cmllcyh0aGlzLnNlc3Npb25zKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFNFU1NJT05TX1NUT1JBR0VfS0VZLCBKU09OLnN0cmluZ2lmeShzZXNzaW9uc09iamVjdCkpO1xuXG4gICAgICAvLyBTYXZlIGN1cnJlbnQgc2Vzc2lvbiB0byB3aW5kb3ctc3BlY2lmaWMgc3RvcmFnZVxuICAgICAgY29uc3QgY3VycmVudFNlc3Npb25LZXkgPSB0aGlzLmdldFdpbmRvd1NwZWNpZmljS2V5KENVUlJFTlRfU0VTU0lPTl9LRVkpO1xuICAgICAgaWYgKHRoaXMuY3VycmVudFNlc3Npb25JZCkge1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShjdXJyZW50U2Vzc2lvbktleSwgdGhpcy5jdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNhdmUgc2Vzc2lvbnMgdG8gc3RvcmFnZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGFzeW5jIGNyZWF0ZU5ld1Nlc3Npb25XaXRoQXV0b05hbWUoY29uZmlnOiBUcmFkaW5nQ29uZmlnLCBjdXN0b21OYW1lPzogc3RyaW5nLCBjdXJyZW50QmFsYW5jZXM/OiB7IGNyeXB0bzE6IG51bWJlcjsgY3J5cHRvMjogbnVtYmVyOyBzdGFibGVjb2luOiBudW1iZXIgfSk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgY29uc3Qgc2Vzc2lvbk5hbWUgPSBjdXN0b21OYW1lIHx8IHRoaXMuZ2VuZXJhdGVTZXNzaW9uTmFtZShjb25maWcpO1xuICAgIHJldHVybiB0aGlzLmNyZWF0ZU5ld1Nlc3Npb24oc2Vzc2lvbk5hbWUsIGNvbmZpZywgY3VycmVudEJhbGFuY2VzKTtcbiAgfVxuXG4gIHB1YmxpYyBhc3luYyBjcmVhdGVOZXdTZXNzaW9uKG5hbWU6IHN0cmluZywgY29uZmlnOiBUcmFkaW5nQ29uZmlnLCBjdXJyZW50QmFsYW5jZXM/OiB7IGNyeXB0bzE6IG51bWJlcjsgY3J5cHRvMjogbnVtYmVyOyBzdGFibGVjb2luOiBudW1iZXIgfSk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgLy8gVmFsaWRhdGUgY29uZmlnIGJlZm9yZSBjcmVhdGluZyBzZXNzaW9uXG4gICAgaWYgKCFjb25maWcuY3J5cHRvMSB8fCAhY29uZmlnLmNyeXB0bzIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CfmqggQ2Fubm90IGNyZWF0ZSBzZXNzaW9uIHdpdGggZW1wdHkgY3J5cHRvIGNvbmZpZ3VyYXRpb246JywgY29uZmlnKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBjb25maWd1cmF0aW9uOiBjcnlwdG8xIGFuZCBjcnlwdG8yIG11c3QgYmUgc2V0Jyk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ+KchSBDcmVhdGluZyBzZXNzaW9uIHdpdGggY29uZmlnOicsIGNvbmZpZyk7XG5cbiAgICAvLyBVc2UgcHJvdmlkZWQgYmFsYW5jZXMgb3IgZGVmYXVsdCB2YWx1ZXNcbiAgICBjb25zdCBiYWxhbmNlcyA9IGN1cnJlbnRCYWxhbmNlcyB8fCB7IGNyeXB0bzE6IDEwLCBjcnlwdG8yOiAxMDAwMDAsIHN0YWJsZWNvaW46IDAgfTtcblxuICAgIGlmICh0aGlzLnVzZUJhY2tlbmQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHNlc3Npb25EYXRhID0ge1xuICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgY29uZmlnOiBjb25maWcsIC8vIEJhY2tlbmQgZXhwZWN0cyAnY29uZmlnJywgbm90ICdjb25maWdfc25hcHNob3QnXG4gICAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBbXSwgLy8gQmFja2VuZCBleHBlY3RzIGNhbWVsQ2FzZVxuICAgICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogMTAwMDAwLFxuICAgICAgICAgIGNyeXB0bzFCYWxhbmNlOiBiYWxhbmNlcy5jcnlwdG8xLFxuICAgICAgICAgIGNyeXB0bzJCYWxhbmNlOiBiYWxhbmNlcy5jcnlwdG8yLFxuICAgICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBiYWxhbmNlcy5zdGFibGVjb2luXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzZXNzaW9uQXBpLmNyZWF0ZVNlc3Npb24oc2Vzc2lvbkRhdGEpO1xuXG4gICAgICAgIC8vIEFkZCB0aGUgc2Vzc2lvbiB0byBvdXIgbG9jYWwgY2FjaGVcbiAgICAgICAgY29uc3QgbmV3U2Vzc2lvbjogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICAgICAgaWQ6IHJlc3BvbnNlLnNlc3Npb24uc2Vzc2lvbl91dWlkLFxuICAgICAgICAgIG5hbWU6IHJlc3BvbnNlLnNlc3Npb24ubmFtZSxcbiAgICAgICAgICBjb25maWcsXG4gICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShyZXNwb25zZS5zZXNzaW9uLmNyZWF0ZWRfYXQpLmdldFRpbWUoKSxcbiAgICAgICAgICBsYXN0TW9kaWZpZWQ6IG5ldyBEYXRlKHJlc3BvbnNlLnNlc3Npb24ubGFzdF9tb2RpZmllZCkuZ2V0VGltZSgpLFxuICAgICAgICAgIGlzQWN0aXZlOiByZXNwb25zZS5zZXNzaW9uLmlzX2FjdGl2ZSxcbiAgICAgICAgICBydW50aW1lOiByZXNwb25zZS5zZXNzaW9uLnJ1bnRpbWUgfHwgMCxcbiAgICAgICAgICB0YXJnZXRQcmljZVJvd3M6IFtdLFxuICAgICAgICAgIG9yZGVySGlzdG9yeTogW10sXG4gICAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiAxMDAwMDAsXG4gICAgICAgICAgY3J5cHRvMUJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzEsXG4gICAgICAgICAgY3J5cHRvMkJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzIsXG4gICAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IGJhbGFuY2VzLnN0YWJsZWNvaW5cbiAgICAgICAgfTtcblxuICAgICAgICB0aGlzLnNlc3Npb25zLnNldChyZXNwb25zZS5zZXNzaW9uLnNlc3Npb25fdXVpZCwgbmV3U2Vzc2lvbik7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU2Vzc2lvbiBjcmVhdGVkIG9uIGJhY2tlbmQ6JywgcmVzcG9uc2Uuc2Vzc2lvbi5zZXNzaW9uX3V1aWQpO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2Uuc2Vzc2lvbi5zZXNzaW9uX3V1aWQ7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNyZWF0ZSBzZXNzaW9uIG9uIGJhY2tlbmQsIGZhbGxpbmcgYmFjayB0byBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xuICAgICAgICB0aGlzLnVzZUJhY2tlbmQgPSBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBsb2NhbFN0b3JhZ2VcbiAgICBjb25zdCBzZXNzaW9uSWQgPSB1dWlkdjQoKTtcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuXG4gICAgY29uc3QgbmV3U2Vzc2lvbjogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICBpZDogc2Vzc2lvbklkLFxuICAgICAgbmFtZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIHRhcmdldFByaWNlUm93czogW10sXG4gICAgICBvcmRlckhpc3Rvcnk6IFtdLFxuICAgICAgY3VycmVudE1hcmtldFByaWNlOiAwLFxuICAgICAgY3J5cHRvMUJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzEsXG4gICAgICBjcnlwdG8yQmFsYW5jZTogYmFsYW5jZXMuY3J5cHRvMixcbiAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBiYWxhbmNlcy5zdGFibGVjb2luLFxuICAgICAgY3JlYXRlZEF0OiBub3csXG4gICAgICBsYXN0TW9kaWZpZWQ6IG5vdyxcbiAgICAgIGlzQWN0aXZlOiBmYWxzZSxcbiAgICAgIHJ1bnRpbWU6IDBcbiAgICB9O1xuXG4gICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBuZXdTZXNzaW9uKTtcbiAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgIHJldHVybiBzZXNzaW9uSWQ7XG4gIH1cblxuICBwdWJsaWMgYXN5bmMgc2F2ZVNlc3Npb24oXG4gICAgc2Vzc2lvbklkOiBzdHJpbmcsXG4gICAgY29uZmlnOiBUcmFkaW5nQ29uZmlnLFxuICAgIHRhcmdldFByaWNlUm93czogVGFyZ2V0UHJpY2VSb3dbXSxcbiAgICBvcmRlckhpc3Rvcnk6IE9yZGVySGlzdG9yeUVudHJ5W10sXG4gICAgY3VycmVudE1hcmtldFByaWNlOiBudW1iZXIsXG4gICAgY3J5cHRvMUJhbGFuY2U6IG51bWJlcixcbiAgICBjcnlwdG8yQmFsYW5jZTogbnVtYmVyLFxuICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBudW1iZXIsXG4gICAgaXNBY3RpdmU6IGJvb2xlYW4gPSBmYWxzZSxcbiAgICBvdmVycmlkZVJ1bnRpbWU/OiBudW1iZXIgLy8gT3B0aW9uYWwgcGFyYW1ldGVyIHRvIHNldCBzcGVjaWZpYyBydW50aW1lXG4gICk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBTYWZldHkgY2hlY2sgZm9yIHVuZGVmaW5lZCBvciBpbnZhbGlkIHNlc3Npb24gSURcbiAgICAgIGlmICghc2Vzc2lvbklkIHx8IHNlc3Npb25JZCA9PT0gJ3VuZGVmaW5lZCcgfHwgc2Vzc2lvbklkLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBDYW5ub3Qgc2F2ZSBzZXNzaW9uIHdpdGggaW52YWxpZCBJRDonLCBzZXNzaW9uSWQpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Nlc3Npb24gbm90IGZvdW5kOicsIHNlc3Npb25JZCk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIHJ1bnRpbWUgLSB1c2Ugb3ZlcnJpZGUgaWYgcHJvdmlkZWQsIG90aGVyd2lzZSBjYWxjdWxhdGUgbm9ybWFsbHlcbiAgICAgIGxldCBjdXJyZW50UnVudGltZTogbnVtYmVyO1xuXG4gICAgICBpZiAob3ZlcnJpZGVSdW50aW1lICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgLy8gVXNlIHRoZSBwcm92aWRlZCBydW50aW1lIChmb3Igc2F2ZWQgc2Vzc2lvbnMpXG4gICAgICAgIGN1cnJlbnRSdW50aW1lID0gb3ZlcnJpZGVSdW50aW1lO1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiBVc2luZyBvdmVycmlkZSBydW50aW1lOiAke2N1cnJlbnRSdW50aW1lfW1zIGZvciBzZXNzaW9uICR7c2Vzc2lvbklkfWApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gQ2FsY3VsYXRlIHJ1bnRpbWUgbm9ybWFsbHkgZm9yIGFjdGl2ZSBzZXNzaW9uc1xuICAgICAgICBjdXJyZW50UnVudGltZSA9IHNlc3Npb24ucnVudGltZSB8fCAwO1xuICAgICAgICBjb25zdCBzdGFydFRpbWUgPSB0aGlzLnNlc3Npb25TdGFydFRpbWVzLmdldChzZXNzaW9uSWQpO1xuICAgICAgICBpZiAoc3RhcnRUaW1lICYmIGlzQWN0aXZlKSB7XG4gICAgICAgICAgLy8gU2Vzc2lvbiBpcyBydW5uaW5nLCB1cGRhdGUgcnVudGltZVxuICAgICAgICAgIGN1cnJlbnRSdW50aW1lID0gKHNlc3Npb24ucnVudGltZSB8fCAwKSArIChEYXRlLm5vdygpIC0gc3RhcnRUaW1lKTtcbiAgICAgICAgICAvLyBSZXNldCBzdGFydCB0aW1lIGZvciBuZXh0IGludGVydmFsXG4gICAgICAgICAgdGhpcy5zZXNzaW9uU3RhcnRUaW1lcy5zZXQoc2Vzc2lvbklkLCBEYXRlLm5vdygpKTtcbiAgICAgICAgfSBlbHNlIGlmICghaXNBY3RpdmUgJiYgc3RhcnRUaW1lKSB7XG4gICAgICAgICAgLy8gU2Vzc2lvbiBzdG9wcGVkLCBmaW5hbGl6ZSBydW50aW1lXG4gICAgICAgICAgY3VycmVudFJ1bnRpbWUgPSAoc2Vzc2lvbi5ydW50aW1lIHx8IDApICsgKERhdGUubm93KCkgLSBzdGFydFRpbWUpO1xuICAgICAgICAgIHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuZGVsZXRlKHNlc3Npb25JZCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaXNBY3RpdmUgJiYgIXN0YXJ0VGltZSkge1xuICAgICAgICAgIC8vIFNlc3Npb24ganVzdCBzdGFydGVkLCByZWNvcmQgc3RhcnQgdGltZVxuICAgICAgICAgIHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuc2V0KHNlc3Npb25JZCwgRGF0ZS5ub3coKSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3QgdXBkYXRlZFNlc3Npb246IFRyYWRpbmdTZXNzaW9uID0ge1xuICAgICAgICAuLi5zZXNzaW9uLFxuICAgICAgICBjb25maWcsXG4gICAgICAgIHRhcmdldFByaWNlUm93czogWy4uLnRhcmdldFByaWNlUm93c10sXG4gICAgICAgIG9yZGVySGlzdG9yeTogWy4uLm9yZGVySGlzdG9yeV0sXG4gICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgIGNyeXB0bzJCYWxhbmNlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgaXNBY3RpdmUsXG4gICAgICAgIGxhc3RNb2RpZmllZDogRGF0ZS5ub3coKSxcbiAgICAgICAgcnVudGltZTogY3VycmVudFJ1bnRpbWVcbiAgICAgIH07XG5cbiAgICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb25JZCwgdXBkYXRlZFNlc3Npb24pO1xuXG4gICAgICAvLyBGb3Igbm93LCBmb2N1cyBvbiBsb2NhbFN0b3JhZ2UgcGVyc2lzdGVuY2UgdG8gYXZvaWQgYmFja2VuZCBBUEkgaXNzdWVzXG4gICAgICAvLyBCYWNrZW5kIGludGVncmF0aW9uIGNhbiBiZSBlbmFibGVkIGxhdGVyIHdoZW4gYmFja2VuZCBpcyBwcm9wZXJseSBjb25maWd1cmVkXG4gICAgICBjb25zb2xlLmxvZygn8J+SviBTYXZpbmcgc2Vzc2lvbiB0byBsb2NhbFN0b3JhZ2UgKGJhY2tlbmQgZGlzYWJsZWQgZm9yIHN0YWJpbGl0eSk6Jywgc2Vzc2lvbklkKTtcblxuICAgICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBzZXNzaW9uOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBwdWJsaWMgbG9hZFNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiBUcmFkaW5nU2Vzc2lvbiB8IG51bGwge1xuICAgIHJldHVybiB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpIHx8IG51bGw7XG4gIH1cblxuICBwdWJsaWMgYXN5bmMgZGVsZXRlU2Vzc2lvbihzZXNzaW9uSWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIC8vIERlbGV0ZSBmcm9tIGJhY2tlbmQgZmlyc3QgaWYgYXZhaWxhYmxlIGFuZCB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcbiAgICBpZiAodGhpcy51c2VCYWNrZW5kICYmIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwbHV0b0F1dGhUb2tlbicpO1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgc2Vzc2lvbkFwaS5kZWxldGVTZXNzaW9uKHNlc3Npb25JZCk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXNzaW9uIGRlbGV0ZWQgZnJvbSBiYWNrZW5kOicsIHNlc3Npb25JZCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpO1xuICAgICAgICAgIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJzQwMScpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnNDIyJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdBdXRoZW50aWNhdGlvbicpKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UkCBBdXRoZW50aWNhdGlvbiBpc3N1ZSBkdXJpbmcgc2Vzc2lvbiBkZWxldGlvbiwgcHJvY2VlZGluZyB3aXRoIGxvY2FsIGRlbGV0aW9uJyk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gZGVsZXRlIHNlc3Npb24gZnJvbSBiYWNrZW5kOicsIGVycm9yKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgLy8gQ29udGludWUgd2l0aCBsb2NhbCBkZWxldGlvbiBhcyBmYWxsYmFja1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIE5vIGF1dGggdG9rZW4sIHNraXBwaW5nIGJhY2tlbmQgc2Vzc2lvbiBkZWxldGlvbicpO1xuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IGRlbGV0ZWQgPSB0aGlzLnNlc3Npb25zLmRlbGV0ZShzZXNzaW9uSWQpO1xuICAgIGlmIChkZWxldGVkKSB7XG4gICAgICBpZiAodGhpcy5jdXJyZW50U2Vzc2lvbklkID09PSBzZXNzaW9uSWQpIHtcbiAgICAgICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gbnVsbDtcbiAgICAgICAgY29uc3QgY3VycmVudFNlc3Npb25LZXkgPSB0aGlzLmdldFdpbmRvd1NwZWNpZmljS2V5KENVUlJFTlRfU0VTU0lPTl9LRVkpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShjdXJyZW50U2Vzc2lvbktleSk7XG4gICAgICB9XG4gICAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgIH1cbiAgICByZXR1cm4gZGVsZXRlZDtcbiAgfVxuXG4gIHB1YmxpYyBnZXRBbGxTZXNzaW9ucygpOiBTZXNzaW9uTWV0YWRhdGFbXSB7XG4gICAgcmV0dXJuIEFycmF5LmZyb20odGhpcy5zZXNzaW9ucy52YWx1ZXMoKSkubWFwKHNlc3Npb24gPT4gKHtcbiAgICAgIGlkOiBzZXNzaW9uLmlkLFxuICAgICAgbmFtZTogc2Vzc2lvbi5uYW1lLFxuICAgICAgcGFpcjogYCR7c2Vzc2lvbi5jb25maWcuY3J5cHRvMX0vJHtzZXNzaW9uLmNvbmZpZy5jcnlwdG8yfWAsXG4gICAgICBjcmVhdGVkQXQ6IHNlc3Npb24uY3JlYXRlZEF0LFxuICAgICAgbGFzdE1vZGlmaWVkOiBzZXNzaW9uLmxhc3RNb2RpZmllZCxcbiAgICAgIGlzQWN0aXZlOiBzZXNzaW9uLmlzQWN0aXZlLFxuICAgICAgcnVudGltZTogdGhpcy5nZXRDdXJyZW50UnVudGltZShzZXNzaW9uLmlkKSwgLy8gVXNlIGN1cnJlbnQgcnVudGltZSBpbmNsdWRpbmcgYWN0aXZlIHRpbWVcbiAgICAgIHRvdGFsVHJhZGVzOiBzZXNzaW9uLm9yZGVySGlzdG9yeS5sZW5ndGgsXG4gICAgICB0b3RhbFByb2ZpdExvc3M6IHNlc3Npb24ub3JkZXJIaXN0b3J5XG4gICAgICAgIC5maWx0ZXIodHJhZGUgPT4gdHJhZGUub3JkZXJUeXBlID09PSAnU0VMTCcgJiYgdHJhZGUucmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMiAhPT0gdW5kZWZpbmVkKVxuICAgICAgICAucmVkdWNlKChzdW0sIHRyYWRlKSA9PiBzdW0gKyAodHJhZGUucmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMiB8fCAwKSwgMClcbiAgICB9KSk7XG4gIH1cblxuICBwdWJsaWMgc2V0Q3VycmVudFNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5zZXNzaW9ucy5oYXMoc2Vzc2lvbklkKSkge1xuICAgICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbklkO1xuXG4gICAgICAvLyBTdG9yZSBpbiBib3RoIGxvY2FsU3RvcmFnZSAod2luZG93LXNwZWNpZmljKSBhbmQgc2Vzc2lvblN0b3JhZ2UgKHBhZ2UgcmVmcmVzaCBwZXJzaXN0ZW5jZSlcbiAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGN1cnJlbnRTZXNzaW9uS2V5LCBzZXNzaW9uSWQpO1xuICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbShDVVJSRU5UX1NFU1NJT05fS0VZLCBzZXNzaW9uSWQpO1xuXG4gICAgICAvLyBNYXJrIHRoaXMgc2Vzc2lvbiBhcyBhY3RpdmUgd2hlbiBpdCBiZWNvbWVzIHRoZSBjdXJyZW50IHNlc3Npb25cbiAgICAgIC8vIEJ1dCBkb24ndCBkZWFjdGl2YXRlIG90aGVyIHNlc3Npb25zIC0gbXVsdGlwbGUgc2Vzc2lvbnMgY2FuIGJlIGFjdGl2ZSBpbiBkaWZmZXJlbnQgdGFic1xuICAgICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgICBpZiAoc2Vzc2lvbikge1xuICAgICAgICBzZXNzaW9uLmlzQWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgICAgICB0aGlzLnNlc3Npb25zLnNldChzZXNzaW9uSWQsIHNlc3Npb24pO1xuICAgICAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFNlc3Npb24gJHtzZXNzaW9uSWR9IG1hcmtlZCBhcyBhY3RpdmUgZm9yIHdpbmRvdyAke3RoaXMud2luZG93SWR9YCk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGdldEN1cnJlbnRTZXNzaW9uSWQoKTogc3RyaW5nIHwgbnVsbCB7XG4gICAgcmV0dXJuIHRoaXMuY3VycmVudFNlc3Npb25JZDtcbiAgfVxuXG4gIHB1YmxpYyBjbGVhckN1cnJlbnRTZXNzaW9uKCk6IHZvaWQge1xuICAgIC8vIE9ubHkgbWFyayBzZXNzaW9uIGFzIGluYWN0aXZlIGlmIG5vIG90aGVyIHdpbmRvd3MgYXJlIHVzaW5nIGl0XG4gICAgaWYgKHRoaXMuY3VycmVudFNlc3Npb25JZCkge1xuICAgICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHRoaXMuY3VycmVudFNlc3Npb25JZCk7XG4gICAgICBpZiAoc2Vzc2lvbiAmJiBzZXNzaW9uLmlzQWN0aXZlKSB7XG4gICAgICAgIC8vIENoZWNrIGlmIGFueSBvdGhlciB3aW5kb3dzIGFyZSB1c2luZyB0aGlzIHNlc3Npb25cbiAgICAgICAgY29uc3QgaXNVc2VkQnlPdGhlcldpbmRvd3MgPSB0aGlzLmlzU2Vzc2lvblVzZWRCeU90aGVyV2luZG93cyh0aGlzLmN1cnJlbnRTZXNzaW9uSWQpO1xuXG4gICAgICAgIGlmICghaXNVc2VkQnlPdGhlcldpbmRvd3MpIHtcbiAgICAgICAgICBzZXNzaW9uLmlzQWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgICAgICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHRoaXMuY3VycmVudFNlc3Npb25JZCwgc2Vzc2lvbik7XG4gICAgICAgICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4o+577iPIFNlc3Npb24gJHt0aGlzLmN1cnJlbnRTZXNzaW9uSWR9IG1hcmtlZCBhcyBpbmFjdGl2ZSAobm8gb3RoZXIgd2luZG93cyB1c2luZyBpdClgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UhCBTZXNzaW9uICR7dGhpcy5jdXJyZW50U2Vzc2lvbklkfSByZW1haW5zIGFjdGl2ZSAodXNlZCBieSBvdGhlciB3aW5kb3dzKWApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gbnVsbDtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGN1cnJlbnRTZXNzaW9uS2V5KTtcbiAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oQ1VSUkVOVF9TRVNTSU9OX0tFWSk7XG4gICAgfVxuICAgIGNvbnNvbGUubG9nKGDwn5eR77iPIENsZWFyZWQgY3VycmVudCBzZXNzaW9uIGZvciB3aW5kb3cgJHt0aGlzLndpbmRvd0lkfWApO1xuICB9XG5cbiAgcHVibGljIHN0YXJ0U2Vzc2lvblJ1bnRpbWUoc2Vzc2lvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICB0aGlzLnNlc3Npb25TdGFydFRpbWVzLnNldChzZXNzaW9uSWQsIERhdGUubm93KCkpO1xuICB9XG5cbiAgcHVibGljIHN0b3BTZXNzaW9uUnVudGltZShzZXNzaW9uSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKHN0YXJ0VGltZSkge1xuICAgICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgICBpZiAoc2Vzc2lvbikge1xuICAgICAgICBjb25zdCBhZGRpdGlvbmFsUnVudGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgICAgIHNlc3Npb24ucnVudGltZSA9IChzZXNzaW9uLnJ1bnRpbWUgfHwgMCkgKyBhZGRpdGlvbmFsUnVudGltZTtcbiAgICAgICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgICAgICAvLyBLZWVwIHNlc3Npb24gYWN0aXZlIGV2ZW4gd2hlbiBydW50aW1lIHN0b3BzIC0gb25seSBkZWFjdGl2YXRlIG9uIG1hbnVhbCBzYXZlIG9yIHNlc3Npb24gY2xlYXJcbiAgICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBzZXNzaW9uKTtcbiAgICAgICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICAgIH1cbiAgICAgIHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuZGVsZXRlKHNlc3Npb25JZCk7XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGRlYWN0aXZhdGVTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogdm9pZCB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKHNlc3Npb24gJiYgc2Vzc2lvbi5pc0FjdGl2ZSkge1xuICAgICAgc2Vzc2lvbi5pc0FjdGl2ZSA9IGZhbHNlO1xuICAgICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBzZXNzaW9uKTtcbiAgICAgIHRoaXMuc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gICAgICBjb25zb2xlLmxvZyhg4o+577iPIFNlc3Npb24gJHtzZXNzaW9uSWR9IGRlYWN0aXZhdGVkYCk7XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGdldEN1cnJlbnRSdW50aW1lKHNlc3Npb25JZDogc3RyaW5nKTogbnVtYmVyIHtcbiAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKTtcbiAgICBpZiAoIXNlc3Npb24pIHJldHVybiAwO1xuXG4gICAgY29uc3Qgc3RhcnRUaW1lID0gdGhpcy5zZXNzaW9uU3RhcnRUaW1lcy5nZXQoc2Vzc2lvbklkKTtcbiAgICBpZiAoc3RhcnRUaW1lKSB7XG4gICAgICAvLyBTZXNzaW9uIGlzIGN1cnJlbnRseSBydW5uaW5nLCBhZGQgY3VycmVudCBydW50aW1lIHRvIHN0b3JlZCBydW50aW1lXG4gICAgICByZXR1cm4gKHNlc3Npb24ucnVudGltZSB8fCAwKSArIChEYXRlLm5vdygpIC0gc3RhcnRUaW1lKTtcbiAgICB9XG5cbiAgICAvLyBTZXNzaW9uIGlzIG5vdCBydW5uaW5nLCByZXR1cm4gc3RvcmVkIHJ1bnRpbWVcbiAgICByZXR1cm4gc2Vzc2lvbi5ydW50aW1lIHx8IDA7XG4gIH1cblxuICAvLyBNZXRob2QgdG8gcmVmcmVzaCBiYWNrZW5kIGNvbm5lY3Rpb24gYWZ0ZXIgbG9naW4vbG9nb3V0XG4gIHB1YmxpYyBhc3luYyByZWZyZXNoQmFja2VuZENvbm5lY3Rpb24oKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc29sZS5sb2coJ/CflIQgUmVmcmVzaGluZyBiYWNrZW5kIGNvbm5lY3Rpb24uLi4nKTtcbiAgICBhd2FpdCB0aGlzLmluaXRpYWxpemVCYWNrZW5kQ29ubmVjdGlvbigpO1xuICB9XG5cbiAgLy8gTWV0aG9kIHRvIGRpc2FibGUgYmFja2VuZCBtb2RlIGR1ZSB0byBhdXRoZW50aWNhdGlvbiBpc3N1ZXNcbiAgcHVibGljIGRpc2FibGVCYWNrZW5kTW9kZSgpOiB2b2lkIHtcbiAgICBjb25zb2xlLmxvZygn8J+UkCBEaXNhYmxpbmcgYmFja2VuZCBtb2RlIGR1ZSB0byBhdXRoZW50aWNhdGlvbiBpc3N1ZXMnKTtcbiAgICB0aGlzLnVzZUJhY2tlbmQgPSBmYWxzZTtcbiAgICB0aGlzLmlzSW5pdGlhbGl6aW5nID0gZmFsc2U7XG4gIH1cblxuICAvLyBNZXRob2QgdG8gaGFuZGxlIGxvZ291dCAtIHN3aXRjaCB0byBsb2NhbFN0b3JhZ2UgbW9kZVxuICBwdWJsaWMgaGFuZGxlTG9nb3V0KCk6IHZvaWQge1xuICAgIGNvbnNvbGUubG9nKCfwn5GLIFVzZXIgbG9nZ2VkIG91dCwgc3dpdGNoaW5nIHRvIGxvY2FsU3RvcmFnZSBtb2RlJyk7XG4gICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7XG4gICAgdGhpcy5zZXNzaW9ucy5jbGVhcigpO1xuICAgIHRoaXMuY3VycmVudFNlc3Npb25JZCA9IG51bGw7XG4gICAgdGhpcy5sb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSgpO1xuICB9XG5cbiAgcHVibGljIGV4cG9ydFNlc3Npb25Ub0pTT04oc2Vzc2lvbklkOiBzdHJpbmcpOiBzdHJpbmcgfCBudWxsIHtcbiAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKTtcbiAgICBpZiAoIXNlc3Npb24pIHJldHVybiBudWxsO1xuXG4gICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KHNlc3Npb24sIG51bGwsIDIpO1xuICB9XG5cbiAgcHVibGljIGltcG9ydFNlc3Npb25Gcm9tSlNPTihqc29uRGF0YTogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNlc3Npb25EYXRhID0gSlNPTi5wYXJzZShqc29uRGF0YSk7XG4gICAgICBjb25zdCBzZXNzaW9uSWQgPSB1dWlkdjQoKTtcbiAgICAgIFxuICAgICAgY29uc3QgaW1wb3J0ZWRTZXNzaW9uOiBUcmFkaW5nU2Vzc2lvbiA9IHtcbiAgICAgICAgLi4uc2Vzc2lvbkRhdGEsXG4gICAgICAgIGlkOiBzZXNzaW9uSWQsXG4gICAgICAgIGlzQWN0aXZlOiBmYWxzZSxcbiAgICAgICAgbGFzdE1vZGlmaWVkOiBEYXRlLm5vdygpXG4gICAgICB9O1xuXG4gICAgICB0aGlzLnNlc3Npb25zLnNldChzZXNzaW9uSWQsIGltcG9ydGVkU2Vzc2lvbik7XG4gICAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgICAgcmV0dXJuIHNlc3Npb25JZDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGltcG9ydCBzZXNzaW9uOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfVxuXG4gIHB1YmxpYyByZW5hbWVTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nLCBuZXdOYW1lOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKTtcbiAgICBpZiAoIXNlc3Npb24pIHJldHVybiBmYWxzZTtcblxuICAgIHNlc3Npb24ubmFtZSA9IG5ld05hbWU7XG4gICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb25JZCwgc2Vzc2lvbik7XG4gICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuXG4gIHB1YmxpYyBhc3luYyB1cGRhdGVTZXNzaW9uQWxhcm1TZXR0aW5ncyhzZXNzaW9uSWQ6IHN0cmluZywgYWxhcm1TZXR0aW5nczogQXBwU2V0dGluZ3MpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKTtcbiAgICBpZiAoIXNlc3Npb24pIHJldHVybiBmYWxzZTtcblxuICAgIHNlc3Npb24uYWxhcm1TZXR0aW5ncyA9IGFsYXJtU2V0dGluZ3M7XG4gICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb25JZCwgc2Vzc2lvbik7XG5cbiAgICAvLyBTYXZlIHRvIGJhY2tlbmQgaWYgYXZhaWxhYmxlXG4gICAgaWYgKHRoaXMudXNlQmFja2VuZCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2Vzc2lvbkRhdGEgPSB7XG4gICAgICAgICAgbmFtZTogc2Vzc2lvbi5uYW1lLFxuICAgICAgICAgIGNvbmZpZzogc2Vzc2lvbi5jb25maWcsXG4gICAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBzZXNzaW9uLnRhcmdldFByaWNlUm93cyxcbiAgICAgICAgICBjdXJyZW50TWFya2V0UHJpY2U6IHNlc3Npb24uY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgIGNyeXB0bzFCYWxhbmNlOiBzZXNzaW9uLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICAgIGNyeXB0bzJCYWxhbmNlOiBzZXNzaW9uLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBzZXNzaW9uLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgIGlzQWN0aXZlOiBzZXNzaW9uLmlzQWN0aXZlLFxuICAgICAgICAgIGFsYXJtX3NldHRpbmdzOiBhbGFybVNldHRpbmdzXG4gICAgICAgIH07XG5cbiAgICAgICAgYXdhaXQgc2Vzc2lvbkFwaS51cGRhdGVTZXNzaW9uKHNlc3Npb25JZCwgc2Vzc2lvbkRhdGEpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFNlc3Npb24gYWxhcm0gc2V0dGluZ3Mgc2F2ZWQgdG8gYmFja2VuZDonLCBzZXNzaW9uSWQpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBzYXZlIHNlc3Npb24gYWxhcm0gc2V0dGluZ3MgdG8gYmFja2VuZDonLCBlcnJvcik7XG4gICAgICAgIC8vIENvbnRpbnVlIHdpdGggbG9jYWxTdG9yYWdlIHNhdmUgYXMgZmFsbGJhY2tcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgIHJldHVybiB0cnVlO1xuICB9XG5cbiAgcHVibGljIGdldFNlc3Npb25IaXN0b3J5KHNlc3Npb25JZDogc3RyaW5nKTogT3JkZXJIaXN0b3J5RW50cnlbXSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgcmV0dXJuIHNlc3Npb24gPyBbLi4uc2Vzc2lvbi5vcmRlckhpc3RvcnldIDogW107XG4gIH1cblxuICBwdWJsaWMgZXhwb3J0U2Vzc2lvblRvQ1NWKHNlc3Npb25JZDogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm4gbnVsbDtcblxuICAgIGNvbnN0IGhlYWRlcnMgPSBbXG4gICAgICAnRGF0ZScsXG4gICAgICAnVGltZScsXG4gICAgICAnUGFpcicsXG4gICAgICAnQ3J5cHRvJyxcbiAgICAgICdPcmRlciBUeXBlJyxcbiAgICAgICdBbW91bnQnLFxuICAgICAgJ0F2ZyBQcmljZScsXG4gICAgICAnVmFsdWUnLFxuICAgICAgJ1ByaWNlIDEnLFxuICAgICAgJ0NyeXB0byAxJyxcbiAgICAgICdQcmljZSAyJyxcbiAgICAgICdDcnlwdG8gMidcbiAgICBdO1xuXG4gICAgY29uc3QgY3N2Q29udGVudCA9IFtcbiAgICAgIGhlYWRlcnMuam9pbignLCcpLFxuICAgICAgLi4uc2Vzc2lvbi5vcmRlckhpc3RvcnkubWFwKGVudHJ5ID0+IFtcbiAgICAgICAgbmV3IERhdGUoZW50cnkudGltZXN0YW1wKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgIG5ldyBEYXRlKGVudHJ5LnRpbWVzdGFtcCkudG9UaW1lU3RyaW5nKCkuc3BsaXQoJyAnKVswXSxcbiAgICAgICAgZW50cnkucGFpcixcbiAgICAgICAgZW50cnkuY3J5cHRvMVN5bWJvbCxcbiAgICAgICAgZW50cnkub3JkZXJUeXBlLFxuICAgICAgICBlbnRyeS5hbW91bnRDcnlwdG8xPy50b0ZpeGVkKHNlc3Npb24uY29uZmlnLm51bURpZ2l0cykgfHwgJycsXG4gICAgICAgIGVudHJ5LmF2Z1ByaWNlPy50b0ZpeGVkKHNlc3Npb24uY29uZmlnLm51bURpZ2l0cykgfHwgJycsXG4gICAgICAgIGVudHJ5LnZhbHVlQ3J5cHRvMj8udG9GaXhlZChzZXNzaW9uLmNvbmZpZy5udW1EaWdpdHMpIHx8ICcnLFxuICAgICAgICBlbnRyeS5wcmljZTE/LnRvRml4ZWQoc2Vzc2lvbi5jb25maWcubnVtRGlnaXRzKSB8fCAnJyxcbiAgICAgICAgZW50cnkuY3J5cHRvMVN5bWJvbCxcbiAgICAgICAgZW50cnkucHJpY2UyPy50b0ZpeGVkKHNlc3Npb24uY29uZmlnLm51bURpZ2l0cykgfHwgJycsXG4gICAgICAgIGVudHJ5LmNyeXB0bzJTeW1ib2xcbiAgICAgIF0uam9pbignLCcpKVxuICAgIF0uam9pbignXFxuJyk7XG5cbiAgICByZXR1cm4gY3N2Q29udGVudDtcbiAgfVxuXG4gIHB1YmxpYyBjbGVhckFsbFNlc3Npb25zKCk6IHZvaWQge1xuICAgIHRoaXMuc2Vzc2lvbnMuY2xlYXIoKTtcbiAgICB0aGlzLmN1cnJlbnRTZXNzaW9uSWQgPSBudWxsO1xuICAgIC8vIENsZWFyIHNoYXJlZCBzZXNzaW9ucyBzdG9yYWdlXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oU0VTU0lPTlNfU1RPUkFHRV9LRVkpO1xuICAgIC8vIENsZWFyIHdpbmRvdy1zcGVjaWZpYyBjdXJyZW50IHNlc3Npb25cbiAgICBjb25zdCBjdXJyZW50U2Vzc2lvbktleSA9IHRoaXMuZ2V0V2luZG93U3BlY2lmaWNLZXkoQ1VSUkVOVF9TRVNTSU9OX0tFWSk7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oY3VycmVudFNlc3Npb25LZXkpO1xuICB9XG5cbiAgLy8gQXV0by1zYXZlIGZ1bmN0aW9uYWxpdHlcbiAgcHVibGljIGVuYWJsZUF1dG9TYXZlKFxuICAgIHNlc3Npb25JZDogc3RyaW5nLFxuICAgIGdldFNlc3Npb25EYXRhOiAoKSA9PiB7XG4gICAgICBjb25maWc6IFRyYWRpbmdDb25maWc7XG4gICAgICB0YXJnZXRQcmljZVJvd3M6IFRhcmdldFByaWNlUm93W107XG4gICAgICBvcmRlckhpc3Rvcnk6IE9yZGVySGlzdG9yeUVudHJ5W107XG4gICAgICBjdXJyZW50TWFya2V0UHJpY2U6IG51bWJlcjtcbiAgICAgIGNyeXB0bzFCYWxhbmNlOiBudW1iZXI7XG4gICAgICBjcnlwdG8yQmFsYW5jZTogbnVtYmVyO1xuICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IG51bWJlcjtcbiAgICAgIGlzQWN0aXZlOiBib29sZWFuO1xuICAgIH0sXG4gICAgaW50ZXJ2YWxNczogbnVtYmVyID0gMzAwMDAgLy8gMzAgc2Vjb25kc1xuICApOiAoKSA9PiB2b2lkIHtcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBnZXRTZXNzaW9uRGF0YSgpO1xuICAgICAgdGhpcy5zYXZlU2Vzc2lvbihcbiAgICAgICAgc2Vzc2lvbklkLFxuICAgICAgICBkYXRhLmNvbmZpZyxcbiAgICAgICAgZGF0YS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgIGRhdGEub3JkZXJIaXN0b3J5LFxuICAgICAgICBkYXRhLmN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgZGF0YS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgZGF0YS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgZGF0YS5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgZGF0YS5pc0FjdGl2ZVxuICAgICAgKTtcbiAgICB9LCBpbnRlcnZhbE1zKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbInY0IiwidXVpZHY0Iiwic2Vzc2lvbkFwaSIsIlNFU1NJT05TX1NUT1JBR0VfS0VZIiwiQ1VSUkVOVF9TRVNTSU9OX0tFWSIsImdlbmVyYXRlV2luZG93SWQiLCJNYXRoIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwiZ2V0V2luZG93SWQiLCJ3aW5kb3dJZCIsInNlc3Npb25TdG9yYWdlIiwiZ2V0SXRlbSIsInNldEl0ZW0iLCJjb25zb2xlIiwibG9nIiwiU2Vzc2lvbk1hbmFnZXIiLCJnZXRJbnN0YW5jZSIsImluc3RhbmNlIiwiZ2VuZXJhdGVTZXNzaW9uTmFtZSIsImNvbmZpZyIsImNyeXB0bzEiLCJjcnlwdG8yIiwidHJhZGluZ01vZGUiLCJiYXNlTmFtZSIsImV4aXN0aW5nU2Vzc2lvbnMiLCJBcnJheSIsImZyb20iLCJzZXNzaW9ucyIsInZhbHVlcyIsInNpbWlsYXJTZXNzaW9ucyIsImZpbHRlciIsInNlc3Npb24iLCJuYW1lIiwic3RhcnRzV2l0aCIsImxlbmd0aCIsIm1heE51bWJlciIsImZvckVhY2giLCJtYXRjaCIsIlJlZ0V4cCIsInJlcGxhY2UiLCJudW1iZXIiLCJwYXJzZUludCIsIm1heCIsImluaXRpYWxpemVCYWNrZW5kQ29ubmVjdGlvbiIsImlzSW5pdGlhbGl6aW5nIiwiYXV0aFRva2VuIiwibG9jYWxTdG9yYWdlIiwidXNlQmFja2VuZCIsImxvYWRTZXNzaW9uc0Zyb21TdG9yYWdlIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJvayIsImxvYWRTZXNzaW9uc0Zyb21CYWNrZW5kIiwiRXJyb3IiLCJlcnJvciIsImNoZWNrQmFja2VuZENvbm5lY3Rpb24iLCJnZXRXaW5kb3dTcGVjaWZpY0tleSIsImJhc2VLZXkiLCJpc1Nlc3Npb25Vc2VkQnlPdGhlcldpbmRvd3MiLCJzZXNzaW9uSWQiLCJhbGxLZXlzIiwiT2JqZWN0Iiwia2V5cyIsImN1cnJlbnRTZXNzaW9uS2V5cyIsImtleSIsIm90aGVyV2luZG93U2Vzc2lvbklkIiwiZ2V0QWN0aXZlV2luZG93c0ZvclNlc3Npb24iLCJhY3RpdmVXaW5kb3dzIiwid2luZG93U2Vzc2lvbklkIiwicHVzaCIsInNldHVwU3RvcmFnZUxpc3RlbmVyIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsImV2ZW50IiwibmV3VmFsdWUiLCJwYXJzZWRTZXNzaW9ucyIsIkpTT04iLCJwYXJzZSIsIk1hcCIsImVudHJpZXMiLCJzaXplIiwic2Vzc2lvbnNEYXRhIiwiY3VycmVudFNlc3Npb25JZCIsIndpbmRvd1NwZWNpZmljS2V5IiwiYWN0aXZlU2Vzc2lvbnMiLCJfIiwic2Vzc2lvbkRhdGEiLCJpc0FjdGl2ZSIsImxhc3RNb2RpZmllZCIsIm1vc3RSZWNlbnRTZXNzaW9uIiwicmVkdWNlIiwibGF0ZXN0IiwiY3VycmVudCIsImxhdGVzdFNlc3Npb24iLCJjdXJyZW50U2Vzc2lvbiIsImdldCIsInNlc3Npb25TdGFydFRpbWVzIiwiaGFzIiwiZXN0aW1hdGVkU3RhcnRUaW1lIiwicnVudGltZSIsInNldCIsInRva2VuIiwiZ2V0QWxsU2Vzc2lvbnMiLCJjbGVhciIsImlkIiwic2Vzc2lvbl91dWlkIiwiY29uZmlnX3NuYXBzaG90IiwiY3JlYXRlZEF0IiwiY3JlYXRlZF9hdCIsImdldFRpbWUiLCJsYXN0X21vZGlmaWVkIiwiaXNfYWN0aXZlIiwidGFyZ2V0UHJpY2VSb3dzIiwidGFyZ2V0X3ByaWNlX3Jvd3MiLCJvcmRlckhpc3RvcnkiLCJvcmRlcl9oaXN0b3J5IiwiY3VycmVudE1hcmtldFByaWNlIiwiY3VycmVudF9tYXJrZXRfcHJpY2UiLCJjcnlwdG8xQmFsYW5jZSIsImNyeXB0bzFfYmFsYW5jZSIsImNyeXB0bzJCYWxhbmNlIiwiY3J5cHRvMl9iYWxhbmNlIiwic3RhYmxlY29pbkJhbGFuY2UiLCJzdGFibGVjb2luX2JhbGFuY2UiLCJhbGFybVNldHRpbmdzIiwiYWxhcm1fc2V0dGluZ3MiLCJ1bmRlZmluZWQiLCJhY3RpdmVTZXNzaW9uIiwiZmluZCIsInMiLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwiU3RyaW5nIiwiaW5jbHVkZXMiLCJ3YXJuIiwic2F2ZVNlc3Npb25zVG9TdG9yYWdlIiwic2Vzc2lvbnNPYmplY3QiLCJmcm9tRW50cmllcyIsInN0cmluZ2lmeSIsImN1cnJlbnRTZXNzaW9uS2V5IiwiY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZSIsImN1c3RvbU5hbWUiLCJjdXJyZW50QmFsYW5jZXMiLCJzZXNzaW9uTmFtZSIsImNyZWF0ZU5ld1Nlc3Npb24iLCJiYWxhbmNlcyIsInN0YWJsZWNvaW4iLCJjcmVhdGVTZXNzaW9uIiwibmV3U2Vzc2lvbiIsInNhdmVTZXNzaW9uIiwib3ZlcnJpZGVSdW50aW1lIiwiY3VycmVudFJ1bnRpbWUiLCJzdGFydFRpbWUiLCJkZWxldGUiLCJ1cGRhdGVkU2Vzc2lvbiIsImxvYWRTZXNzaW9uIiwiZGVsZXRlU2Vzc2lvbiIsImRlbGV0ZWQiLCJyZW1vdmVJdGVtIiwibWFwIiwicGFpciIsImdldEN1cnJlbnRSdW50aW1lIiwidG90YWxUcmFkZXMiLCJ0b3RhbFByb2ZpdExvc3MiLCJ0cmFkZSIsIm9yZGVyVHlwZSIsInJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIiLCJzdW0iLCJzZXRDdXJyZW50U2Vzc2lvbiIsImdldEN1cnJlbnRTZXNzaW9uSWQiLCJjbGVhckN1cnJlbnRTZXNzaW9uIiwiaXNVc2VkQnlPdGhlcldpbmRvd3MiLCJzdGFydFNlc3Npb25SdW50aW1lIiwic3RvcFNlc3Npb25SdW50aW1lIiwiYWRkaXRpb25hbFJ1bnRpbWUiLCJkZWFjdGl2YXRlU2Vzc2lvbiIsInJlZnJlc2hCYWNrZW5kQ29ubmVjdGlvbiIsImRpc2FibGVCYWNrZW5kTW9kZSIsImhhbmRsZUxvZ291dCIsImV4cG9ydFNlc3Npb25Ub0pTT04iLCJpbXBvcnRTZXNzaW9uRnJvbUpTT04iLCJqc29uRGF0YSIsImltcG9ydGVkU2Vzc2lvbiIsInJlbmFtZVNlc3Npb24iLCJuZXdOYW1lIiwidXBkYXRlU2Vzc2lvbkFsYXJtU2V0dGluZ3MiLCJ1cGRhdGVTZXNzaW9uIiwiZ2V0U2Vzc2lvbkhpc3RvcnkiLCJleHBvcnRTZXNzaW9uVG9DU1YiLCJjc3ZDb250ZW50Iiwiam9pbiIsImVudHJ5IiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInRvVGltZVN0cmluZyIsImNyeXB0bzFTeW1ib2wiLCJhbW91bnRDcnlwdG8xIiwidG9GaXhlZCIsIm51bURpZ2l0cyIsImF2Z1ByaWNlIiwidmFsdWVDcnlwdG8yIiwicHJpY2UxIiwicHJpY2UyIiwiY3J5cHRvMlN5bWJvbCIsImNsZWFyQWxsU2Vzc2lvbnMiLCJlbmFibGVBdXRvU2F2ZSIsImdldFNlc3Npb25EYXRhIiwiaW50ZXJ2YWxNcyIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJkYXRhIiwiY2xlYXJJbnRlcnZhbCIsInNldFRpbWVvdXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});